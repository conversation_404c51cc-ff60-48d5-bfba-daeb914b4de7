<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== VÉRIFICATION DES DEVISES ===\n\n";

try {
    // 1. Vérifier la table currencies
    echo "1. TABLE CURRENCIES\n";
    echo "==================\n";
    
    if (\Schema::hasTable('currencies')) {
        $currencies = \DB::table('currencies')->get();
        echo "Nombre de devises: " . $currencies->count() . "\n";
        
        foreach ($currencies as $currency) {
            echo "- ID: {$currency->id}\n";
            echo "  Code: " . ($currency->code ?? 'NULL') . "\n";
            echo "  Nom: " . ($currency->name ?? 'NULL') . "\n";
            echo "  Symbole: " . ($currency->symbol ?? 'NULL') . "\n";
            echo "  Taux: " . ($currency->exchange_rate ?? 'NULL') . "\n";
            echo "  Défaut: " . (($currency->is_default ?? false) ? 'OUI' : 'NON') . "\n";
            echo "  ---\n";
        }
    } else {
        echo "Table 'currencies' n'existe pas\n";
    }
    
    echo "\n";
    
    // 2. Vérifier les devises dans CommandePrincipale
    echo "2. DEVISES DANS COMMANDES PRINCIPALES\n";
    echo "====================================\n";
    
    $commandesDevises = \App\Models\CommandePrincipale::select('devise', \DB::raw('count(*) as count'))
        ->groupBy('devise')
        ->get();
    
    echo "Devises utilisées dans les commandes principales:\n";
    foreach ($commandesDevises as $cmd) {
        echo "- " . ($cmd->devise ?? 'NULL') . ": {$cmd->count} commande(s)\n";
    }
    
    echo "\n";
    
    // 3. Vérifier les devises dans SousCommandeVendeur
    echo "3. DEVISES DANS SOUS-COMMANDES\n";
    echo "=============================\n";
    
    $sousCommandesDevises = \App\Models\SousCommandeVendeur::select('devise', \DB::raw('count(*) as count'))
        ->groupBy('devise')
        ->get();
    
    echo "Devises utilisées dans les sous-commandes:\n";
    foreach ($sousCommandesDevises as $sousCmd) {
        echo "- " . ($sousCmd->devise ?? 'NULL') . ": {$sousCmd->count} sous-commande(s)\n";
    }
    
    echo "\n";
    
    // 4. Vérifier la configuration Laravel
    echo "4. CONFIGURATION LARAVEL\n";
    echo "========================\n";
    
    echo "Locale: " . config('app.locale') . "\n";
    echo "Timezone: " . config('app.timezone') . "\n";
    
    // Vérifier si il y a une config pour les devises
    if (config('currency')) {
        echo "Config currency: " . json_encode(config('currency')) . "\n";
    } else {
        echo "Pas de config currency\n";
    }
    
    echo "\n";
    
    // 5. Test de formatage des montants
    echo "5. TEST FORMATAGE MONTANTS\n";
    echo "=========================\n";
    
    $montantTest = 67000;
    
    echo "Montant test: {$montantTest}\n";
    echo "Format EUR: " . number_format($montantTest, 2, ',', ' ') . " €\n";
    echo "Format FCFA: " . number_format($montantTest, 0, ',', ' ') . " FCFA\n";
    
    // Test avec les helpers Laravel si disponibles
    try {
        echo "Laravel money EUR: " . \Illuminate\Support\Number::currency($montantTest, 'EUR') . "\n";
        echo "Laravel money XOF: " . \Illuminate\Support\Number::currency($montantTest, 'XOF') . "\n";
    } catch (Exception $e) {
        echo "Helpers Laravel money non disponibles\n";
    }
    
    echo "\n";
    
    // 6. Recommandations
    echo "6. RECOMMANDATIONS\n";
    echo "==================\n";
    
    $fcfaExists = \DB::table('currencies')->where('code', 'XOF')->exists();
    echo "Devise FCFA (XOF) existe: " . ($fcfaExists ? 'OUI' : 'NON') . "\n";
    
    if (!$fcfaExists) {
        echo "\nACTIONS RECOMMANDÉES:\n";
        echo "1. Ajouter la devise FCFA (XOF) dans la table currencies\n";
        echo "2. Mettre à jour les commandes pour utiliser FCFA\n";
        echo "3. Modifier les interfaces Filament pour afficher FCFA\n";
    }
    
    echo "\n=== FIN VÉRIFICATION ===\n";
    
} catch (Exception $e) {
    echo "ERREUR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
