<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marchand_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('marchand_id')->constrained('marchands')->onDelete('cascade');
            $table->string('type_document');
            $table->string('nom_original');
            $table->string('nom_stockage');
            $table->string('chemin_fichier');
            $table->string('extension');
            $table->bigInteger('taille_fichier');
            $table->string('mime_type');
            $table->string('hash_fichier');
            $table->enum('statut_validation', ['en_attente', 'valide', 'rejete'])->default('en_attente');
            $table->foreignId('validateur_id')->nullable()->constrained('users');
            $table->timestamp('date_validation')->nullable();
            $table->text('commentaires_validation')->nullable();
            $table->text('raison_rejet')->nullable();
            $table->timestamp('date_upload')->useCurrent();
            $table->timestamp('date_expiration')->nullable();
            $table->timestamp('date_derniere_modification')->useCurrent();
            $table->boolean('est_obligatoire')->default(false);
            $table->boolean('est_confidentiel')->default(false);
            $table->string('version')->nullable();
            $table->json('metadonnees')->nullable();
            $table->boolean('est_crypte')->default(false);
            $table->text('cle_cryptage')->nullable();
            $table->string('adresse_ip_upload')->nullable();
            $table->string('user_agent_upload')->nullable();
            $table->boolean('ocr_effectue')->default(false);
            $table->json('donnees_ocr')->nullable();
            $table->decimal('score_qualite', 5, 2)->nullable();
            $table->json('verifications_automatiques')->nullable();
            $table->timestamps();

            // Index pour améliorer les performances
            $table->index(['marchand_id', 'type_document']);
            $table->index(['statut_validation']);
            $table->index(['date_expiration']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marchand_documents');
    }
}; 