<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Ajout de la colonne role
            $table->enum('role', ['Client', 'Marchand', 'Admin'])->default('Client');

            // Ajout de la colonne is_active
            $table->boolean('is_active')->default(true);

            // Ajout de la colonne last_login_at
            $table->timestamp('last_login_at')->nullable();

            // Ajout des colonnes pour la vérification d'email et la réinitialisation de mot de passe
            $table->string('email_verification_token', 100)->nullable()->unique();
            $table->string('password_reset_token', 100)->nullable()->unique();
            $table->timestamp('password_reset_expires_at')->nullable();

            // Suppression de la colonne name si elle existe
            if (Schema::hasColumn('users', 'name')) {
                $table->dropColumn('name');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Suppression des colonnes ajoutées
            $table->dropColumn([
                'role',
                'is_active',
                'last_login_at',
                'email_verification_token',
                'password_reset_token',
                'password_reset_expires_at'
            ]);
        });
    }
};
