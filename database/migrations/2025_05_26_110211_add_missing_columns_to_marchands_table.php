<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('marchands', function (Blueprint $table) {
            if (!Schema::hasColumn('marchands', 'pays_business')) {
                $table->string('pays_business')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'ville_business')) {
                $table->string('ville_business')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'type_business')) {
                $table->enum('type_business', ['individuel', 'entreprise', 'association'])->nullable();
            }
            if (!Schema::hasColumn('marchands', 'statut_validation')) {
                $table->enum('statut_validation', ['en_attente', 'valide', 'rejete'])->default('en_attente');
            }
            if (!Schema::hasColumn('marchands', 'etape_inscription')) {
                $table->enum('etape_inscription', ['debut', 'informations', 'documents', 'validation', 'termine'])->default('debut');
            }
            if (!Schema::hasColumn('marchands', 'documents_soumis')) {
                $table->json('documents_soumis')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'documents_requis')) {
                $table->json('documents_requis')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'date_soumission_documents')) {
                $table->timestamp('date_soumission_documents')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'date_validation')) {
                $table->timestamp('date_validation')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'commentaires_validation')) {
                $table->text('commentaires_validation')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'validateur_id')) {
                $table->unsignedBigInteger('validateur_id')->nullable();
                $table->foreign('validateur_id')->references('id')->on('users')->onDelete('set null');
            }
            if (!Schema::hasColumn('marchands', 'telephone_principal')) {
                $table->string('telephone_principal')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'telephone_secondaire')) {
                $table->string('telephone_secondaire')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'email_business')) {
                $table->string('email_business')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'site_web')) {
                $table->string('site_web')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'methode_paiement_preferee')) {
                $table->enum('methode_paiement_preferee', ['bancaire', 'mobile_money', 'paypal','stripe'])->nullable();
            }
            if (!Schema::hasColumn('marchands', 'iban_crypte')) {
                $table->text('iban_crypte')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'nom_titulaire_compte')) {
                $table->string('nom_titulaire_compte')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'numero_orange_money')) {
                $table->string('numero_orange_money')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'numero_mtn_money')) {
                $table->string('numero_mtn_money')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'description_business')) {
                $table->text('description_business')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'categories_produits')) {
                $table->json('categories_produits')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'chiffre_affaires_estime')) {
                $table->decimal('chiffre_affaires_estime', 15, 2)->nullable();
            }
            if (!Schema::hasColumn('marchands', 'nombre_employes')) {
                $table->integer('nombre_employes')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'accepte_conditions')) {
                $table->boolean('accepte_conditions')->default(false);
            }
            if (!Schema::hasColumn('marchands', 'accepte_newsletter')) {
                $table->boolean('accepte_newsletter')->default(false);
            }
            if (!Schema::hasColumn('marchands', 'langue_preferee')) {
                $table->string('langue_preferee')->default('fr');
            }
            if (!Schema::hasColumn('marchands', 'notifications_preferences')) {
                $table->json('notifications_preferences')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'source_inscription')) {
                $table->enum('source_inscription', ['direct', 'parrainage', 'publicite_facebook', 'publicite_google', 'autre'])->nullable();
            }
            if (!Schema::hasColumn('marchands', 'code_parrainage')) {
                $table->string('code_parrainage')->nullable();
            }
            if (!Schema::hasColumn('marchands', 'parrain_id')) {
                $table->unsignedBigInteger('parrain_id')->nullable();
                $table->foreign('parrain_id')->references('id')->on('marchands')->onDelete('set null');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('marchands', function (Blueprint $table) {
            // Supprimer d'abord les clés étrangères
            $table->dropForeign(['validateur_id']);
            $table->dropForeign(['parrain_id']);

            // Ensuite supprimer les colonnes
            $table->dropColumn([
                'pays_business',
                'ville_business',
                'type_business',
                'statut_validation',
                'etape_inscription',
                'documents_soumis',
                'documents_requis',
                'date_soumission_documents',
                'date_validation',
                'commentaires_validation',
                'validateur_id',
                'telephone_principal',
                'telephone_secondaire',
                'email_business',
                'site_web',
                'methode_paiement_preferee',
                'iban_crypte',
                'nom_titulaire_compte',
                'numero_orange_money',
                'numero_mtn_money',
                'description_business',
                'categories_produits',
                'chiffre_affaires_estime',
                'nombre_employes',
                'accepte_conditions',
                'accepte_newsletter',
                'langue_preferee',
                'notifications_preferences',
                'source_inscription',
                'code_parrainage',
                'parrain_id'
            ]);
        });
    }
}; 