<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('merchant_validation_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('validation_id')->constrained('merchant_validations')->onDelete('cascade');
            $table->enum('document_type', [
                'KBIS',
                'PIECE_IDENTITE',
                'RIB',
                'ATTESTATION_FISCALE',
                'AUTRES'
            ]);
            $table->string('file_path');
            $table->string('original_name');
            $table->string('mime_type');
            $table->integer('file_size');
            $table->enum('status', [
                'EN_ATTENTE',
                'VALIDE',
                'REJETE'
            ])->default('EN_ATTENTE');
            $table->text('rejection_reason')->nullable();
            $table->foreignId('verified_by')->nullable()->constrained('users');
            $table->timestamp('verified_at')->nullable();
            $table->timestamps();

            // Un seul document de chaque type par validation
            $table->unique(['validation_id', 'document_type']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('merchant_validation_documents');
    }
}; 