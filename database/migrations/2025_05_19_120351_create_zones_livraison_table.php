<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('zones_livraison', function (Blueprint $table) {
            $table->id();
            $table->string('nom');
            $table->enum('type', ['Pays', 'Region', 'Ville', 'Quartier']);
            $table->foreignId('parent_id')->nullable()->constrained('zones_livraison')->onDelete('cascade');
            $table->string('code')->nullable()->comment('Code postal, code pays, etc.');
            $table->boolean('actif')->default(true);
            $table->timestamps();

            // Index pour améliorer les performances des requêtes
            $table->index('type');
            $table->index('parent_id');
            $table->index('actif');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('zones_livraison');
    }
};
