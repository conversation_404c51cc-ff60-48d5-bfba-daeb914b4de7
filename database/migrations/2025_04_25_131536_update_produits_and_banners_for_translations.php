<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Doctrine\DBAL\Types\Type;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Modifier la table produits pour les champs traduisibles
        Schema::table('produits', function (Blueprint $table) {
            // Sauvegarder les données existantes
            $produits = DB::table('produits')->get();

            // Modifier les colonnes pour stocker des données JSON
            $table->json('nom')->change();
            $table->json('description')->change();

            // Restaurer les données avec le format JSON
            foreach ($produits as $produit) {
                $nomJson = json_encode(['fr' => $produit->nom]);
                $descriptionJson = json_encode(['fr' => $produit->description]);

                DB::table('produits')
                    ->where('id', $produit->id)
                    ->update([
                        'nom' => $nomJson,
                        'description' => $descriptionJson,
                    ]);
            }
        });

        // Modifier la table banners pour les champs traduisibles
        Schema::table('banners', function (Blueprint $table) {
            // Sauvegarder les données existantes
            $banners = DB::table('banners')->get();

            // Modifier les colonnes pour stocker des données JSON
            $table->json('title')->nullable()->change();
            $table->json('description')->nullable()->change();
            $table->json('button_text')->nullable()->change();

            // Restaurer les données avec le format JSON
            foreach ($banners as $banner) {
                $titleJson = $banner->title ? json_encode(['fr' => $banner->title]) : null;
                $descriptionJson = $banner->description ? json_encode(['fr' => $banner->description]) : null;
                $buttonTextJson = $banner->button_text ? json_encode(['fr' => $banner->button_text]) : null;

                DB::table('banners')
                    ->where('id', $banner->id)
                    ->update([
                        'title' => $titleJson,
                        'description' => $descriptionJson,
                        'button_text' => $buttonTextJson,
                    ]);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Cette migration est difficile à inverser car nous avons converti des données
        // Nous pourrions essayer de récupérer la première traduction (fr) et la remettre comme valeur simple
        // Mais cela pourrait entraîner une perte de données si d'autres traductions ont été ajoutées

        // Pour les produits
        Schema::table('produits', function (Blueprint $table) {
            // Sauvegarder les données existantes
            $produits = DB::table('produits')->get();

            // Modifier les colonnes pour revenir à des types simples
            $table->string('nom', 255)->change();
            $table->text('description')->change();

            // Restaurer les données avec le premier élément de la traduction (fr)
            foreach ($produits as $produit) {
                $nom = json_decode($produit->nom, true);
                $description = json_decode($produit->description, true);

                DB::table('produits')
                    ->where('id', $produit->id)
                    ->update([
                        'nom' => $nom['fr'] ?? '',
                        'description' => $description['fr'] ?? '',
                    ]);
            }
        });

        // Pour les bannières
        Schema::table('banners', function (Blueprint $table) {
            // Sauvegarder les données existantes
            $banners = DB::table('banners')->get();

            // Modifier les colonnes pour revenir à des types simples
            $table->string('title', 255)->nullable()->change();
            $table->text('description')->nullable()->change();
            $table->string('button_text', 100)->nullable()->change();

            // Restaurer les données avec le premier élément de la traduction (fr)
            foreach ($banners as $banner) {
                $title = $banner->title ? json_decode($banner->title, true) : null;
                $description = $banner->description ? json_decode($banner->description, true) : null;
                $buttonText = $banner->button_text ? json_decode($banner->button_text, true) : null;

                DB::table('banners')
                    ->where('id', $banner->id)
                    ->update([
                        'title' => $title ? ($title['fr'] ?? '') : null,
                        'description' => $description ? ($description['fr'] ?? '') : null,
                        'button_text' => $buttonText ? ($buttonText['fr'] ?? '') : null,
                    ]);
            }
        });
    }
};
