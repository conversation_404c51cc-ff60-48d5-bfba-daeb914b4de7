<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('versements', function (Blueprint $table) {
            $table->id();
            
            // Relation avec le marchand
            $table->foreignId('marchand_id')->constrained('marchands')->onDelete('cascade');
            
            // Référence unique du versement
            $table->string('reference_versement', 100)->unique();
            
            // Montants
            $table->decimal('montant_brut', 12, 2)->comment('Montant brut avant déduction des frais');
            $table->decimal('frais_transaction', 8, 2)->default(0)->comment('Frais de transaction prélevés');
            $table->decimal('montant_net', 12, 2)->comment('Montant net versé au marchand');
            $table->string('devise', 10)->default('FCFA');
            
            // Statut du versement
            $table->enum('statut', [
                'EnAttente',           // En attente de traitement
                'EnCours',             // En cours de traitement
                'Complété',            // Versement effectué avec succès
                'Échoué',              // Échec du versement
                'Annulé',              // Versement annulé
                'Suspendu',            // Versement suspendu (problème de conformité)
                'Retourné'             // Versement retourné (compte fermé, etc.)
            ])->default('EnAttente');
            
            // Méthode de versement
            $table->enum('methode_versement', [
                'virement_bancaire',
                'orange_money',
                'mtn_money',
                'paypal',
                'stripe',
                'wave',
                'autre'
            ]);
            
            // Informations de transaction
            $table->string('reference_transaction_externe', 255)->nullable()->comment('Référence de la transaction chez le prestataire');
            $table->string('prestataire_paiement', 50)->nullable()->comment('Nom du prestataire (PayPal, Stripe, etc.)');
            
            // Dates importantes
            $table->timestamp('date_demande')->useCurrent();
            $table->timestamp('date_traitement')->nullable();
            $table->timestamp('date_completion')->nullable();
            $table->timestamp('date_echec')->nullable();
            
            // Informations bancaires (cryptées)
            $table->text('details_bancaires_cryptes')->nullable()->comment('Informations bancaires cryptées');
            $table->string('nom_titulaire_compte', 255)->nullable();
            $table->string('numero_compte_masque', 50)->nullable()->comment('Numéro de compte masqué pour affichage');
            
            // Informations de traitement
            $table->text('message_erreur')->nullable()->comment('Message d\'erreur en cas d\'échec');
            $table->text('notes_admin')->nullable()->comment('Notes administratives');
            $table->foreignId('traité_par')->nullable()->constrained('users')->onDelete('set null');
            
            // Période de versement
            $table->date('periode_debut')->nullable()->comment('Début de la période couverte par ce versement');
            $table->date('periode_fin')->nullable()->comment('Fin de la période couverte par ce versement');
            
            // Type de versement
            $table->enum('type_versement', [
                'automatique',         // Versement automatique programmé
                'manuel',              // Versement manuel déclenché par admin
                'instantané',          // Versement instantané (si supporté)
                'urgence'              // Versement d'urgence
            ])->default('automatique');
            
            // Fréquence (pour les versements automatiques)
            $table->enum('frequence', [
                'quotidien',
                'hebdomadaire',
                'bimensuel',
                'mensuel',
                'ponctuel'
            ])->default('hebdomadaire');
            
            // Informations de réconciliation
            $table->integer('nombre_commandes')->default(0)->comment('Nombre de commandes incluses dans ce versement');
            $table->decimal('montant_commissions_deduites', 10, 2)->default(0);
            
            // Métadonnées
            $table->json('metadata')->nullable()->comment('Données supplémentaires (détails des commandes, etc.)');
            
            $table->timestamps();
            
            // Index pour optimiser les performances
            $table->index(['marchand_id', 'statut']);
            $table->index(['statut', 'date_demande']);
            $table->index(['methode_versement', 'statut']);
            $table->index(['date_traitement']);
            $table->index(['periode_debut', 'periode_fin']);
            $table->index(['type_versement', 'frequence']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('versements');
    }
};
