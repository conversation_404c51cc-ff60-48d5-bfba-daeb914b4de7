<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('commissions', function (Blueprint $table) {
            $table->id();
            
            // Relations principales (référence vers tables dans lorrelei/)
            $table->unsignedBigInteger('commande_principale_id')->comment('Référence vers commandes_principales dans lorrelei/');
            $table->unsignedBigInteger('sous_commande_id')->comment('Référence vers sous_commandes_vendeur dans lorrelei/');
            $table->foreignId('marchand_id')->constrained('marchands')->onDelete('cascade');
            
            // Référence unique
            $table->string('reference_commission', 100)->unique();
            
            // Type de commission
            $table->enum('type_commission', [
                'vente',               // Commission sur vente
                'abonnement',          // Commission d'abonnement
                'listing',             // Frais de mise en ligne
                'transaction',         // Frais de transaction
                'service',             // Commission sur service additionnel
                'penalite',            // Pénalité
                'bonus',               // Bonus/réduction
                'ajustement'           // Ajustement manuel
            ])->default('vente');
            
            // Calcul de la commission
            $table->decimal('montant_base', 10, 2)->comment('Montant de base pour le calcul');
            $table->decimal('taux_commission', 5, 2)->comment('Taux de commission appliqué (en %)');
            $table->decimal('montant_fixe', 8, 2)->default(0)->comment('Montant fixe additionnel');
            $table->decimal('montant_commission', 10, 2)->comment('Montant total de la commission');
            $table->string('devise', 10)->default('FCFA');
            
            // Règle de commission appliquée
            $table->string('regle_appliquee', 100)->nullable()->comment('Nom de la règle de commission appliquée');
            $table->json('details_calcul')->nullable()->comment('Détails du calcul de la commission');
            
            // Catégorie et produit (pour les commissions spécifiques)
            $table->foreignId('categorie_id')->nullable()->constrained('categories')->onDelete('set null');
            $table->unsignedBigInteger('produit_id')->nullable()->comment('Référence vers produits dans lorrelei/');
            
            // Statut de la commission
            $table->enum('statut', [
                'calculee',            // Commission calculée
                'confirmee',           // Commission confirmée
                'collectee',           // Commission collectée
                'reversee',            // Commission reversée (en cas d'annulation)
                'ajustee',             // Commission ajustée
                'litigieuse'           // Commission en litige
            ])->default('calculee');
            
            // Période de facturation
            $table->date('periode_facturation')->comment('Période de facturation de cette commission');
            $table->integer('mois_facturation')->comment('Mois de facturation (1-12)');
            $table->integer('annee_facturation')->comment('Année de facturation');
            
            // Dates importantes
            $table->timestamp('date_calcul')->useCurrent();
            $table->timestamp('date_confirmation')->nullable();
            $table->timestamp('date_collecte')->nullable();
            $table->timestamp('date_ajustement')->nullable();
            
            // Informations de traitement
            $table->foreignId('calcule_par')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('confirme_par')->nullable()->constrained('users')->onDelete('set null');
            
            // Ajustements et modifications
            $table->decimal('montant_ajustement', 10, 2)->default(0)->comment('Montant d\'ajustement appliqué');
            $table->text('motif_ajustement')->nullable()->comment('Motif de l\'ajustement');
            $table->foreignId('ajuste_par')->nullable()->constrained('users')->onDelete('set null');
            
            // Informations de versement
            $table->foreignId('versement_id')->nullable()->constrained('versements')->onDelete('set null');
            $table->boolean('incluse_dans_versement')->default(false);
            
            // Facturation
            $table->string('numero_facture', 100)->nullable()->comment('Numéro de facture associée');
            $table->boolean('facture_generee')->default(false);
            $table->timestamp('date_facture')->nullable();
            
            // Métadonnées
            $table->json('metadata')->nullable()->comment('Données supplémentaires');
            $table->text('notes')->nullable()->comment('Notes administratives');
            
            $table->timestamps();
            
            // Index pour optimiser les performances
            $table->index(['marchand_id', 'statut']);
            $table->index(['commande_principale_id']);
            $table->index(['sous_commande_id']);
            $table->index(['type_commission', 'statut']);
            $table->index(['periode_facturation', 'marchand_id']);
            $table->index(['mois_facturation', 'annee_facturation']);
            $table->index(['incluse_dans_versement', 'versement_id']);
            $table->index(['date_calcul']);
            
            // Index composé pour les rapports
            $table->index(['marchand_id', 'periode_facturation', 'type_commission'], 'idx_marchand_periode_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('commissions');
    }
};
