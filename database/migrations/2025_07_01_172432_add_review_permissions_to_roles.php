<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ajouter les nouvelles permissions aux rôles admin existants
        $this->updateAdminRoles();
        
        // Ajouter les nouvelles permissions aux rôles marchand existants
        $this->updateMarchandRoles();
    }

    /**
     * Mettre à jour les rôles admin avec les nouvelles permissions d'avis
     */
    private function updateAdminRoles(): void
    {
        // Super Administrateur - Toutes les permissions
        $superAdminRole = DB::table('admin_roles')->where('slug', 'super_admin')->first();
        if ($superAdminRole) {
            $permissions = json_decode($superAdminRole->permissions, true);
            $newPermissions = array_merge($permissions, [
                'view_reviews',
                'moderate_content', 
                'delete_reviews'
            ]);
            
            DB::table('admin_roles')
                ->where('slug', 'super_admin')
                ->update(['permissions' => json_encode(array_unique($newPermissions))]);
        }

        // Support Client - Permissions de modération
        $supportRole = DB::table('admin_roles')->where('slug', 'support_client')->first();
        if ($supportRole) {
            $permissions = json_decode($supportRole->permissions, true);
            $newPermissions = array_merge($permissions, [
                'view_reviews',
                'moderate_content'
            ]);
            
            DB::table('admin_roles')
                ->where('slug', 'support_client')
                ->update(['permissions' => json_encode(array_unique($newPermissions))]);
        }

        // Admin Finance - Voir les avis seulement
        $financeRole = DB::table('admin_roles')->where('slug', 'admin_finance')->first();
        if ($financeRole) {
            $permissions = json_decode($financeRole->permissions, true);
            $newPermissions = array_merge($permissions, [
                'view_reviews'
            ]);
            
            DB::table('admin_roles')
                ->where('slug', 'admin_finance')
                ->update(['permissions' => json_encode(array_unique($newPermissions))]);
        }

        // Merchant Manager - Voir et modérer les avis
        $merchantManagerRole = DB::table('admin_roles')->where('slug', 'merchant_manager')->first();
        if ($merchantManagerRole) {
            $permissions = json_decode($merchantManagerRole->permissions, true);
            $newPermissions = array_merge($permissions, [
                'view_reviews',
                'moderate_content'
            ]);
            
            DB::table('admin_roles')
                ->where('slug', 'merchant_manager')
                ->update(['permissions' => json_encode(array_unique($newPermissions))]);
        }
    }

    /**
     * Mettre à jour les rôles marchand avec les nouvelles permissions d'avis
     */
    private function updateMarchandRoles(): void
    {
        // Propriétaire - Toutes les permissions
        $ownerRole = DB::table('marchand_roles')->where('slug', 'owner')->first();
        if ($ownerRole) {
            $permissions = json_decode($ownerRole->permissions, true);
            $newPermissions = array_merge($permissions, [
                'view_reviews',
                'respond_to_reviews'
            ]);
            
            DB::table('marchand_roles')
                ->where('slug', 'owner')
                ->update(['permissions' => json_encode(array_unique($newPermissions))]);
        }

        // Gestionnaire - Toutes les permissions
        $managerRole = DB::table('marchand_roles')->where('slug', 'manager')->first();
        if ($managerRole) {
            $permissions = json_decode($managerRole->permissions, true);
            $newPermissions = array_merge($permissions, [
                'view_reviews',
                'respond_to_reviews'
            ]);
            
            DB::table('marchand_roles')
                ->where('slug', 'manager')
                ->update(['permissions' => json_encode(array_unique($newPermissions))]);
        }

        // Product Manager - Voir et répondre aux avis
        $productManagerRole = DB::table('marchand_roles')->where('slug', 'product_manager')->first();
        if ($productManagerRole) {
            $permissions = json_decode($productManagerRole->permissions, true);
            $newPermissions = array_merge($permissions, [
                'view_reviews',
                'respond_to_reviews'
            ]);
            
            DB::table('marchand_roles')
                ->where('slug', 'product_manager')
                ->update(['permissions' => json_encode(array_unique($newPermissions))]);
        }

        // Order Manager - Voir les avis seulement
        $orderManagerRole = DB::table('marchand_roles')->where('slug', 'order_manager')->first();
        if ($orderManagerRole) {
            $permissions = json_decode($orderManagerRole->permissions, true);
            $newPermissions = array_merge($permissions, [
                'view_reviews'
            ]);
            
            DB::table('marchand_roles')
                ->where('slug', 'order_manager')
                ->update(['permissions' => json_encode(array_unique($newPermissions))]);
        }

        // Comptable - Voir les avis seulement
        $comptableRole = DB::table('marchand_roles')->where('slug', 'comptable')->first();
        if ($comptableRole) {
            $permissions = json_decode($comptableRole->permissions, true);
            $newPermissions = array_merge($permissions, [
                'view_reviews'
            ]);
            
            DB::table('marchand_roles')
                ->where('slug', 'comptable')
                ->update(['permissions' => json_encode(array_unique($newPermissions))]);
        }

        // Employé - Voir les avis seulement
        $employeeRole = DB::table('marchand_roles')->where('slug', 'employee')->first();
        if ($employeeRole) {
            $permissions = json_decode($employeeRole->permissions, true);
            $newPermissions = array_merge($permissions, [
                'view_reviews'
            ]);
            
            DB::table('marchand_roles')
                ->where('slug', 'employee')
                ->update(['permissions' => json_encode(array_unique($newPermissions))]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Retirer les permissions d'avis des rôles admin
        $adminRoles = DB::table('admin_roles')->get();
        foreach ($adminRoles as $role) {
            $permissions = json_decode($role->permissions, true);
            $permissions = array_diff($permissions, [
                'view_reviews',
                'moderate_content',
                'delete_reviews'
            ]);
            
            DB::table('admin_roles')
                ->where('id', $role->id)
                ->update(['permissions' => json_encode(array_values($permissions))]);
        }

        // Retirer les permissions d'avis des rôles marchand
        $marchandRoles = DB::table('marchand_roles')->get();
        foreach ($marchandRoles as $role) {
            $permissions = json_decode($role->permissions, true);
            $permissions = array_diff($permissions, [
                'view_reviews',
                'respond_to_reviews'
            ]);
            
            DB::table('marchand_roles')
                ->where('id', $role->id)
                ->update(['permissions' => json_encode(array_values($permissions))]);
        }
    }
};
