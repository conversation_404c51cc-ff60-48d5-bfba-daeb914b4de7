<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('souscription_plans', function (Blueprint $table) {
            $table->id();
            
            // Informations de base du plan
            $table->json('nom')->comment('Nom du plan en plusieurs langues');
            $table->string('type')->comment('Type de plan (free, basic, premium, elite)');
            $table->string('status')->default('active')->comment('Status du plan (active, inactive)');
            
            // Informations de prix
            $table->decimal('current_price', 10, 2)->comment('Prix actuel du plan');
            $table->decimal('discount_price', 10, 2)->nullable()->comment('Prix réduit du plan');
            $table->decimal('discount_percentage', 5, 2)->nullable()->comment('Pourcentage de réduction');
            
            // Configuration du plan
            $table->boolean('is_annual')->default(false)->comment('Si le plan est annuel');
            $table->string('commission_range')->comment('Fourchette de commission (ex: 4-8%)');
            $table->json('features')->comment('Liste des fonctionnalités incluses');
            $table->boolean('popular')->default(false)->comment('Si le plan est mis en avant');
            $table->string('periode')->default('mois')->comment('Période de facturation (mois, année)');
            
            // Timestamps standards
            $table->timestamps();
            $table->softDeletes();
            
            // Index
            $table->index('type');
            $table->index('status');
            $table->index('popular');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('souscription_plans');
    }
}; 