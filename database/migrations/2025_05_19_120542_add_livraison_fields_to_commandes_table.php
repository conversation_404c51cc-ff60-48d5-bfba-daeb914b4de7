<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('commandes', function (Blueprint $table) {
            $table->decimal('frais_livraison', 10, 2)->default(0);
            $table->integer('delai_livraison_estime')->nullable()->comment('En jours');
            $table->string('statut_livraison')->default('En attente');
            $table->string('numero_suivi')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('commandes', function (Blueprint $table) {
            $table->dropColumn([
                'frais_livraison',
                'delai_livraison_estime',
                'statut_livraison',
                'numero_suivi'
            ]);
        });
    }
};
