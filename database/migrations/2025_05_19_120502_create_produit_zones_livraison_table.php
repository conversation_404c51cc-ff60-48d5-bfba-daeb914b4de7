<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('produit_zones_livraison', function (Blueprint $table) {
            $table->id();
            $table->foreignId('produit_id')->constrained('produits')->onDelete('cascade');
            $table->foreignId('marchand_zone_livraison_id')->constrained('marchand_zones_livraison')->onDelete('cascade');
            $table->decimal('frais_livraison_specifique', 10, 2)->nullable()->comment('Remplace les frais standard si défini');
            $table->boolean('actif')->default(true);
            $table->timestamps();

            // Index pour améliorer les performances des requêtes
            $table->index('produit_id');
            $table->index('marchand_zone_livraison_id');
            $table->index('actif');

            // Contrainte d'unicité pour éviter les doublons (avec un nom court)
            $table->unique(['produit_id', 'marchand_zone_livraison_id'], 'prod_zone_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('produit_zones_livraison');
    }
};
