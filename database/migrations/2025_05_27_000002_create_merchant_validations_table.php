<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('merchant_validations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('status', [
                'EN_ATTENTE_SOUMISSION',
                'INFORMATIONS_SOUMISES',
                'DOCUMENTS_SOUMIS',
                'EN_ATTENTE_VALIDATION',
                'VALIDE',
                'REJETE',
                'SUSPENDU'
            ])->default('EN_ATTENTE_SOUMISSION');
            $table->json('business_info')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->timestamp('submitted_at')->nullable();
            $table->timestamp('validated_at')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->foreignId('validated_by')->nullable()->constrained('users');
            $table->timestamps();
        });

        // Ajout de la colonne validation_id à la table marchands
        Schema::table('marchands', function (Blueprint $table) {
            $table->foreignId('validation_id')->nullable()->after('user_id')->constrained('merchant_validations');
        });
    }

    public function down()
    {
        Schema::table('marchands', function (Blueprint $table) {
            $table->dropForeign(['validation_id']);
            $table->dropColumn('validation_id');
        });

        Schema::dropIfExists('merchant_validations');
    }
}; 