<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('boutique_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('marchand_id')->constrained('marchands')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('name', 100); // Nom de l'utilisateur (pour les non-inscrits)
            $table->string('email', 255)->nullable(); // Email de l'utilisateur (pour les non-inscrits)
            $table->tinyInteger('rating')->unsigned()->comment('Note de 1 à 5'); // Note de 1 à 5
            $table->string('title', 255)->nullable()->comment('Titre de l\'avis'); // Titre de l'avis
            $table->text('comment'); // Commentaire
            $table->json('images')->nullable()->comment('Images jointes (JSON)'); // Images jointes (JSON)
            $table->integer('likes')->default(0)->comment('Nombre de likes'); // Nombre de likes
            $table->integer('dislikes')->default(0)->comment('Nombre de dislikes'); // Nombre de dislikes
            $table->string('ip_address', 45)->nullable()->comment('Adresse IP de l\'utilisateur'); // Adresse IP de l'utilisateur
            $table->boolean('is_approved')->default(false)->comment('Si l\'avis est approuvé'); // Si l'avis est approuvé
            $table->boolean('is_verified')->default(false)->comment('Si l\'avis est vérifié (achat confirmé)'); // Si l'avis est vérifié
            $table->foreignId('commande_id')->nullable()->constrained('commandes')->onDelete('set null')->comment('Commande associée pour vérification'); // Commande associée
            $table->text('marchand_response')->nullable()->comment('Réponse du marchand'); // Réponse du marchand
            $table->timestamp('marchand_response_at')->nullable()->comment('Date de réponse du marchand'); // Date de réponse
            $table->boolean('is_reported')->default(false)->comment('Si l\'avis a été signalé'); // Si l'avis a été signalé
            $table->text('report_reason')->nullable()->comment('Raison du signalement'); // Raison du signalement
            $table->timestamps();

            // Index pour les performances
            $table->index(['marchand_id', 'is_approved']);
            $table->index(['marchand_id', 'rating']);
            $table->index(['user_id', 'marchand_id']);
            $table->index('created_at');

            // Contrainte unique pour éviter les avis multiples du même utilisateur sur la même boutique
            $table->unique(['user_id', 'marchand_id'], 'unique_user_marchand_review');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('boutique_reviews');
    }
};
