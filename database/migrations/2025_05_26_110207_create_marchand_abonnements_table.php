<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marchand_abonnements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('marchand_id')->constrained('marchands')->onDelete('cascade');
            $table->enum('type_abonnement', ['gratuit', 'basique', 'premium', 'elite'])->default('gratuit');
            $table->enum('statut', ['actif', 'suspendu', 'expire', 'annule'])->default('actif');
            $table->timestamp('date_debut');
            $table->timestamp('date_fin')->nullable();
            $table->timestamp('date_prochaine_facturation')->nullable();
            $table->timestamp('date_expiration_grace')->nullable();
            $table->decimal('prix_mensuel', 10, 2)->default(0.00);
            $table->decimal('commission_taux_min', 5, 2)->default(5.00);
            $table->decimal('commission_taux_max', 5, 2)->default(10.00);
            $table->decimal('reduction_logistique', 5, 2)->default(0.00);
            $table->integer('limite_produits')->nullable();
            $table->integer('limite_commandes_mois')->nullable();
            $table->integer('limite_campagnes_mois')->nullable();
            $table->boolean('acces_analytics_avancees')->default(false);
            $table->boolean('acces_support_prioritaire')->default(false);
            $table->boolean('acces_gestionnaire_dedie')->default(false);
            $table->boolean('acces_ia_predictive')->default(false);
            $table->boolean('acces_evenements_exclusifs')->default(false);
            $table->string('type_abonnement_precedent')->nullable();
            $table->timestamp('date_changement_abonnement')->nullable();
            $table->text('raison_changement')->nullable();
            $table->enum('mode_facturation', ['mensuel', 'annuel'])->default('mensuel');
            $table->boolean('facturation_automatique')->default(true);
            $table->string('methode_paiement_abonnement')->nullable();
            $table->boolean('est_periode_essai')->default(false);
            $table->timestamp('fin_periode_essai')->nullable();
            $table->string('code_promotion')->nullable();
            $table->decimal('reduction_promotion', 5, 2)->nullable();
            $table->json('fonctionnalites_activees')->nullable();
            $table->json('limites_personnalisees')->nullable();
            $table->text('notes_admin')->nullable();
            $table->timestamps();

            // Index pour améliorer les performances
            $table->index(['marchand_id', 'statut']);
            $table->index(['type_abonnement']);
            $table->index(['date_fin']);
            $table->index(['date_prochaine_facturation']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marchand_abonnements');
    }
};
