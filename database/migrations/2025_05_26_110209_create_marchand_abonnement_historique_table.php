<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marchand_abonnement_historique', function (Blueprint $table) {
            $table->id();
            $table->foreignId('marchand_id')->constrained('marchands')->onDelete('cascade');
            $table->foreignId('abonnement_id')->constrained('marchand_abonnements')->onDelete('cascade');
            $table->string('action');
            $table->string('type_abonnement_avant')->nullable();
            $table->string('type_abonnement_apres')->nullable();
            $table->string('statut_avant')->nullable();
            $table->string('statut_apres')->nullable();
            $table->decimal('prix_avant', 10, 2)->nullable();
            $table->decimal('prix_apres', 10, 2)->nullable();
            $table->timestamp('date_action')->useCurrent();
            $table->foreignId('initie_par')->nullable()->constrained('users');
            $table->text('raison')->nullable();
            $table->decimal('montant_paye', 10, 2)->nullable();
            $table->decimal('montant_rembourse', 10, 2)->nullable();
            $table->string('reference_paiement')->nullable();
            $table->string('methode_paiement')->nullable();
            $table->json('details_changement')->nullable();
            $table->string('adresse_ip')->nullable();
            $table->string('user_agent')->nullable();
            $table->json('metadonnees')->nullable();
            $table->timestamps();

            // Index pour améliorer les performances
            $table->index(['marchand_id', 'date_action']);
            $table->index(['abonnement_id']);
            $table->index(['action']);
            $table->index(['reference_paiement']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marchand_abonnement_historique');
    }
}; 