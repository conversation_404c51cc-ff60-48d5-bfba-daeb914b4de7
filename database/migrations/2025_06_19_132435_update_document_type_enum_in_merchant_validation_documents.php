<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('merchant_validation_documents', function (Blueprint $table) {
            //
            DB::statement("ALTER TABLE merchant_validation_documents MODIFY COLUMN document_type ENUM(
                'KBIS',
                'PIECE_IDENTITE',
                'RIB_BANCAIRE',
                'ATTESTATION_FISCALE',
                'AUTRES',
                'PHOTO_AVEC_PIECE',
                'JUSTIFICATIF_DOMICILE',
                'REGISTRE_COMMERCE',
                'STATUTS_ENTREPRISE',
                'RECEPISSE_DECLARATION',
                'BILAN_COMPTABLE',
                'DECLARATION_FISCALE'
            ) NOT NULL;");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('merchant_validation_documents', function (Blueprint $table) {
            //
            // When rolling back, you might want to revert to the previous ENUM values
            // or a simplified version if the new types are no longer needed.
            // For simplicity, this example reverts to the original set.
            DB::statement("ALTER TABLE merchant_validation_documents MODIFY COLUMN document_type ENUM(
                'KBIS',
                'PIECE_IDENTITE',
                'RIB',
                'ATTESTATION_FISCALE',
                'AUTRES'
            ) NOT NULL;"); // Adjust NOT NULL/NULL as per original column definition
        });
    }
};
