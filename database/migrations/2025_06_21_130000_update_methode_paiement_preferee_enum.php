<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Mettre à jour l'enum pour inclure orange_money et mtn_money
        DB::statement("ALTER TABLE marchands MODIFY COLUMN methode_paiement_preferee ENUM(
            'bancaire',
            'mobile_money',
            'orange_money',
            'mtn_money',
            'paypal',
            'stripe'
        ) NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revenir à l'ancien enum (attention : cela peut causer des pertes de données)
        DB::statement("ALTER TABLE marchands MODIFY COLUMN methode_paiement_preferee ENUM(
            'bancaire',
            'mobile_money',
            'paypal',
            'stripe'
        ) NULL");
    }
};
