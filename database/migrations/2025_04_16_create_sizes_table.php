<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sizes', function (Blueprint $table) {
            $table->id();
            $table->string('name_fr')->comment('Nom de la taille en français');
            $table->string('name_en')->comment('Nom de la taille en anglais');
            $table->string('code')->unique()->comment('Code unique de la taille (ex: XL, M, 42, etc.)');
            $table->string('category')->nullable()->comment('Catégorie de taille (vêtements, chaussures, etc.)');
            $table->json('measurements')->nullable()->comment('Mesures correspondantes en JSON (tour de poitrine, taille, etc.)');
            $table->json('equivalents')->nullable()->comment('Équivalences dans différents systèmes (EU, UK, US, etc.)');
            $table->text('description')->nullable()->comment('Description ou détails supplémentaires');
            $table->boolean('is_active')->default(true)->comment('Indique si la taille est active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sizes');
    }
};
