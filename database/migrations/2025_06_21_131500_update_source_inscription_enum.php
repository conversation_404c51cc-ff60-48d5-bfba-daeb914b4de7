<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Mettre à jour l'enum pour inclure seller_platform
        DB::statement("ALTER TABLE marchands MODIFY COLUMN source_inscription ENUM(
            'direct',
            'parrainage',
            'publicite_facebook',
            'publicite_google',
            'seller_platform',
            'autre'
        ) NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revenir à l'ancien enum (attention : cela peut causer des pertes de données)
        DB::statement("ALTER TABLE marchands MODIFY COLUMN source_inscription ENUM(
            'direct',
            'parrainage',
            'publicite_facebook',
            'publicite_google',
            'autre'
        ) NULL");
    }
};
