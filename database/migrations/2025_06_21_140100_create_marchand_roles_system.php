<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Table des rôles marchand
        Schema::create('marchand_roles', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // 'Propriétaire', 'Gestionnaire', 'Employé'
            $table->string('slug')->unique(); // 'owner', 'manager', 'employee'
            $table->text('description')->nullable();
            $table->json('permissions'); // Permissions par défaut du rôle
            $table->boolean('is_system_role')->default(false); // Rôles non supprimables
            $table->integer('priority')->default(0); // Ordre d'affichage
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Table des utilisateurs marchand (équipe)
        Schema::create('marchand_users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('marchand_id')->constrained('marchands')->onDelete('cascade');
            $table->foreignId('role_id')->constrained('marchand_roles')->onDelete('restrict');
            $table->json('permissions')->nullable(); // Permissions spécifiques
            $table->enum('access_level', ['owner', 'manager', 'employee', 'viewer'])->default('employee');
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_login_at')->nullable();
            $table->foreignId('invited_by')->nullable()->constrained('users')->onDelete('set null');
            $table->string('invitation_token')->nullable();
            $table->timestamp('invitation_expires_at')->nullable();
            $table->text('notes')->nullable(); // Notes internes
            $table->timestamps();

            $table->unique(['user_id', 'marchand_id']); // Un utilisateur par marchand
        });

        // Insérer les rôles système par défaut
        DB::table('marchand_roles')->insert([
            [
                'name' => 'Propriétaire',
                'slug' => 'owner',
                'description' => 'Propriétaire du compte marchand avec tous les droits',
                'permissions' => json_encode([
                    'manage_products', 'manage_orders', 'manage_finances', 'manage_subscriptions',
                    'manage_team', 'invite_users', 'manage_settings', 'view_analytics',
                    'manage_disputes', 'delete_account'
                ]),
                'is_system_role' => true,
                'priority' => 100,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Gestionnaire',
                'slug' => 'manager',
                'description' => 'Gestionnaire avec accès à la plupart des fonctionnalités',
                'permissions' => json_encode([
                    'manage_products', 'manage_orders', 'view_finances',
                    'invite_users', 'view_analytics', 'manage_disputes'
                ]),
                'is_system_role' => true,
                'priority' => 90,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Gestionnaire Produits',
                'slug' => 'product_manager',
                'description' => 'Spécialisé dans la gestion des produits et du catalogue',
                'permissions' => json_encode([
                    'manage_products', 'view_orders', 'view_analytics'
                ]),
                'is_system_role' => false,
                'priority' => 80,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Gestionnaire Commandes',
                'slug' => 'order_manager',
                'description' => 'Spécialisé dans la gestion des commandes et expéditions',
                'permissions' => json_encode([
                    'view_products', 'manage_orders', 'manage_shipping', 'manage_disputes'
                ]),
                'is_system_role' => false,
                'priority' => 70,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Comptable',
                'slug' => 'accountant',
                'description' => 'Accès aux données financières et comptables',
                'permissions' => json_encode([
                    'view_products', 'view_orders', 'view_finances', 'view_analytics'
                ]),
                'is_system_role' => false,
                'priority' => 60,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Employé',
                'slug' => 'employee',
                'description' => 'Accès de base pour les employés',
                'permissions' => json_encode([
                    'view_products', 'view_orders'
                ]),
                'is_system_role' => true,
                'priority' => 50,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marchand_users');
        Schema::dropIfExists('marchand_roles');
    }
};
