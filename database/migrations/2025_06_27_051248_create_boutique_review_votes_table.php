<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('boutique_review_votes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('boutique_review_id')->constrained('boutique_reviews')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->string('ip_address', 45)->comment('Adresse IP (pour les utilisateurs non connectés)');
            $table->enum('vote_type', ['like', 'dislike'])->comment('Type de vote');
            $table->timestamps();

            // Contrainte d'unicité pour éviter les votes multiples
            $table->unique(['boutique_review_id', 'ip_address'], 'unique_boutique_review_vote');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('boutique_review_votes');
    }
};
