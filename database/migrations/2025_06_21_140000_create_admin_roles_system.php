<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Table des rôles admin
        Schema::create('admin_roles', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // 'Super Admin', 'Admin Finance', 'Support Client'
            $table->string('slug')->unique(); // 'super_admin', 'admin_finance', 'support_client'
            $table->text('description')->nullable();
            $table->json('permissions'); // Permissions par défaut du rôle
            $table->boolean('is_system_role')->default(false); // Rôles non supprimables
            $table->integer('priority')->default(0); // Ordre d'affichage
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Table des utilisateurs admin
        Schema::create('admin_users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('role_id')->constrained('admin_roles')->onDelete('restrict');
            $table->json('permissions')->nullable(); // Permissions spécifiques
            $table->enum('department', [
                'management', 'finance', 'support', 'marketing', 
                'tech', 'operations', 'legal', 'hr'
            ])->nullable();
            $table->enum('access_level', ['read', 'write', 'full', 'super_admin'])->default('read');
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_login_at')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('notes')->nullable(); // Notes internes
            $table->timestamps();

            $table->unique(['user_id']); // Un utilisateur = un seul rôle admin
        });

        // Insérer les rôles système par défaut
        DB::table('admin_roles')->insert([
            [
                'name' => 'Super Administrateur',
                'slug' => 'super_admin',
                'description' => 'Accès complet à toutes les fonctionnalités du système',
                'permissions' => json_encode([
                    'manage_users', 'manage_roles', 'manage_merchants', 'validate_merchants',
                    'view_finances', 'manage_payments', 'view_banking_info', 'manage_disputes',
                    'manage_settings', 'view_logs', 'delete_admins', 'manage_system'
                ]),
                'is_system_role' => true,
                'priority' => 100,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Administrateur',
                'slug' => 'admin',
                'description' => 'Accès administratif standard sans gestion des utilisateurs',
                'permissions' => json_encode([
                    'view_merchants', 'edit_merchants', 'validate_merchants',
                    'view_finances', 'manage_payments', 'manage_disputes',
                    'view_logs'
                ]),
                'is_system_role' => true,
                'priority' => 90,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Gestionnaire Finance',
                'slug' => 'finance_manager',
                'description' => 'Gestion des aspects financiers et des paiements',
                'permissions' => json_encode([
                    'view_merchants', 'view_finances', 'manage_payments', 
                    'view_banking_info', 'manage_subscriptions'
                ]),
                'is_system_role' => false,
                'priority' => 80,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Support Client',
                'slug' => 'support_client',
                'description' => 'Gestion du support client et des litiges',
                'permissions' => json_encode([
                    'view_merchants', 'manage_disputes', 'view_support',
                    'manage_tickets', 'view_orders'
                ]),
                'is_system_role' => false,
                'priority' => 70,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Gestionnaire Marchands',
                'slug' => 'merchant_manager',
                'description' => 'Validation et gestion des marchands',
                'permissions' => json_encode([
                    'view_merchants', 'create_merchants', 'edit_merchants',
                    'validate_merchants', 'manage_merchant_documents'
                ]),
                'is_system_role' => false,
                'priority' => 60,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_users');
        Schema::dropIfExists('admin_roles');
    }
};
