<?php

namespace Database\Seeders;

use App\Models\Categorie;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UpdateCategoryPathsSeeder extends Seeder
{
    /**
     * Exécute le seeder pour mettre à jour les chemins de catégories.
     */
    public function run(): void
    {
        $this->command->info('Mise à jour des niveaux et chemins de catégories...');
        
        // Désactiver les événements pour éviter les boucles infinies
        Categorie::withoutEvents(function () {
            // Réinitialiser tous les niveaux et chemins
            DB::table('categories')->update([
                'niveau' => 1,
                'category_path' => null,
            ]);
            
            // Mettre à jour les catégories racines (niveau 1)
            $rootCategories = Categorie::whereNull('categorie_parent_id')->get();
            foreach ($rootCategories as $category) {
                $category->niveau = 1;
                $category->category_path = (string) $category->id;
                $category->save();
                
                $this->command->info("Catégorie racine mise à jour: {$category->nom} (ID: {$category->id})");
                
                // Mettre à jour récursivement les sous-catégories
                $this->updateChildCategories($category);
            }
        });
        
        $this->command->info('Mise à jour des niveaux et chemins de catégories terminée.');
    }
    
    /**
     * Met à jour récursivement les sous-catégories.
     */
    private function updateChildCategories(Categorie $parent, int $level = 2): void
    {
        $children = Categorie::where('categorie_parent_id', $parent->id)->get();
        
        foreach ($children as $child) {
            $child->niveau = $level;
            $child->category_path = $parent->category_path . '/' . $child->id;
            $child->save();
            
            $this->command->info("Sous-catégorie mise à jour: {$child->nom} (ID: {$child->id}, Niveau: {$level})");
            
            // Mettre à jour récursivement les sous-catégories de cette sous-catégorie
            $this->updateChildCategories($child, $level + 1);
        }
    }
}
