<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currencies = [
            [
                'code' => 'FCFA',
                'name' => 'Franc CFA',
                'symbol' => 'FCFA',
                'is_default' => true,
                'is_active' => true,
                'exchange_rate' => 1.0000,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'EUR',
                'name' => 'Euro',
                'symbol' => '€',
                'is_default' => false,
                'is_active' => true,
                'exchange_rate' => 0.0015, // 1 FCFA = 0.0015 EUR
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'USD',
                'name' => 'US Dollar',
                'symbol' => '$',
                'is_default' => false,
                'is_active' => true,
                'exchange_rate' => 0.0017, // 1 FCFA = 0.0017 USD
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'GBP',
                'name' => 'British Pound',
                'symbol' => '£',
                'is_default' => false,
                'is_active' => true,
                'exchange_rate' => 0.0013, // 1 FCFA = 0.0013 GBP
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'XAF',
                'name' => 'CFA Franc BEAC',
                'symbol' => 'FCFA',
                'is_default' => false,
                'is_active' => true,
                'exchange_rate' => 1.0000, // 1 FCFA = 1 XAF
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'XOF',
                'name' => 'CFA Franc BCEAO',
                'symbol' => 'FCFA',
                'is_default' => false,
                'is_active' => true,
                'exchange_rate' => 1.0000, // 1 FCFA = 1 XOF
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('currencies')->insert($currencies);
    }
}
