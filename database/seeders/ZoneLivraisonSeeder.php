<?php

namespace Database\Seeders;

use App\Models\ZoneLivraison;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;

class ZoneLivraisonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Charger les données du fichier JSON
        $jsonPath = database_path('data/zones_livraison_cameroun.json');
        $data = json_decode(File::get($jsonPath), true);

        if (!$data) {
            $this->command->error('Impossible de charger les données des zones de livraison.');
            return;
        }

        // Créer le pays
        $pays = $data['pays'];
        $paysId = $this->createZone($pays['nom'], 'Pays', null, $pays['code']);

        // Créer les régions
        foreach ($pays['regions'] as $region) {
            $regionId = $this->createZone($region['nom'], 'Region', $paysId, $region['code']);

            // Créer les villes
            foreach ($region['villes'] as $ville) {
                $villeId = $this->createZone($ville['nom'], 'Ville', $regionId, $ville['code']);

                // Créer les quartiers
                foreach ($ville['quartiers'] as $quartier) {
                    $this->createZone($quartier['nom'], 'Quartier', $villeId, $quartier['code']);
                }
            }
        }

        $this->command->info('Zones de livraison créées avec succès.');
    }

    /**
     * Créer une zone de livraison.
     *
     * @param string $nom
     * @param string $type
     * @param int|null $parentId
     * @param string|null $code
     * @return int
     */
    private function createZone(string $nom, string $type, ?int $parentId, ?string $code): int
    {
        // Vérifier si la zone existe déjà
        $zone = ZoneLivraison::where('nom', $nom)
            ->where('type', $type)
            ->where('parent_id', $parentId)
            ->first();

        if ($zone) {
            return $zone->id;
        }

        // Créer la zone
        $zone = ZoneLivraison::create([
            'nom' => $nom,
            'type' => $type,
            'parent_id' => $parentId,
            'code' => $code,
            'actif' => true,
        ]);

        return $zone->id;
    }
}
