<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Client;
use App\Models\Marchand;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Création du super admin (depuis les variables d'environnement)
        $adminEmail = env('email', '<EMAIL>');
        $superAdmin = User::firstOrCreate(
            ['email' => $adminEmail],
            [
                'name' => 'Franck Elysee',
                'password' => Hash::make(env('pass', '4321Franck')),
                'role' => 'super_admin', // Super admin
                'is_active' => true,
                'is_admin' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );

    }
}
