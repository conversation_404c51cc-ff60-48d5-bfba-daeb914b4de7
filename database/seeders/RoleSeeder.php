<?php

namespace Database\Seeders;

use App\Models\AdminRole;
use App\Models\MarchandRole;
use App\Enums\AdminPermission;
use App\Enums\MarchandPermission;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->createAdminRoles();
        $this->createMarchandRoles();
    }

    private function createAdminRoles(): void
    {
        // Super Administrateur
        AdminRole::updateOrCreate(
            ['slug' => 'super_admin'],
            [
                'name' => 'Super Administrateur',
                'description' => 'Accès complet à toutes les fonctionnalités du système',
                'permissions' => collect(AdminPermission::cases())->pluck('value')->toArray(),
                'is_system_role' => true,
                'priority' => 100,
                'is_active' => true,
            ]
        );

        // Administrateur
        AdminRole::updateOrCreate(
            ['slug' => 'admin'],
            [
                'name' => 'Administrateur',
                'description' => 'Accès étendu aux fonctionnalités principales',
                'permissions' => [
                    AdminPermission::MANAGE_USERS->value,
                    AdminPermission::VIEW_MERCHANTS->value,
                    AdminPermission::EDIT_MERCHANTS->value,
                    AdminPermission::VALIDATE_MERCHANTS->value,
                    AdminPermission::VIEW_FINANCES->value,
                    AdminPermission::MANAGE_PAYMENTS->value,
                    AdminPermission::VIEW_SUPPORT->value,
                    AdminPermission::MANAGE_TICKETS->value,
                    AdminPermission::VIEW_PRODUCTS->value,
                    AdminPermission::MANAGE_PRODUCTS->value,
                    AdminPermission::VIEW_ORDERS->value,
                    AdminPermission::MANAGE_ORDERS->value,
                    AdminPermission::VIEW_ANALYTICS->value,
                ],
                'is_system_role' => true,
                'priority' => 80,
                'is_active' => true,
            ]
        );

        // Gestionnaire Finance
        AdminRole::updateOrCreate(
            ['slug' => 'finance_manager'],
            [
                'name' => 'Gestionnaire Finance',
                'description' => 'Gestion des finances, paiements et abonnements',
                'permissions' => [
                    AdminPermission::VIEW_FINANCES->value,
                    AdminPermission::MANAGE_PAYMENTS->value,
                    AdminPermission::VIEW_BANKING_INFO->value,
                    AdminPermission::MANAGE_SUBSCRIPTIONS->value,
                    AdminPermission::MANAGE_COMMISSIONS->value,
                    AdminPermission::VIEW_FINANCIAL_REPORTS->value,
                    AdminPermission::MANAGE_REFUNDS->value,
                    AdminPermission::VIEW_MERCHANTS->value,
                    AdminPermission::VIEW_ANALYTICS->value,
                ],
                'is_system_role' => true,
                'priority' => 70,
                'is_active' => true,
            ]
        );

        // Support Client
        AdminRole::updateOrCreate(
            ['slug' => 'support_client'],
            [
                'name' => 'Support Client',
                'description' => 'Gestion du support client et des litiges',
                'permissions' => [
                    AdminPermission::VIEW_SUPPORT->value,
                    AdminPermission::MANAGE_TICKETS->value,
                    AdminPermission::MANAGE_DISPUTES->value,
                    AdminPermission::VIEW_CUSTOMER_DATA->value,
                    AdminPermission::MANAGE_REVIEWS->value,
                    AdminPermission::VIEW_MERCHANTS->value,
                    AdminPermission::VIEW_ORDERS->value,
                    AdminPermission::VIEW_ORDER_DETAILS->value,
                ],
                'is_system_role' => true,
                'priority' => 60,
                'is_active' => true,
            ]
        );

        // Gestionnaire Marchands
        AdminRole::updateOrCreate(
            ['slug' => 'merchant_manager'],
            [
                'name' => 'Gestionnaire Marchands',
                'description' => 'Gestion et validation des marchands',
                'permissions' => [
                    AdminPermission::VIEW_MERCHANTS->value,
                    AdminPermission::CREATE_MERCHANTS->value,
                    AdminPermission::EDIT_MERCHANTS->value,
                    AdminPermission::VALIDATE_MERCHANTS->value,
                    AdminPermission::SUSPEND_MERCHANTS->value,
                    AdminPermission::MANAGE_MERCHANT_DOCUMENTS->value,
                    AdminPermission::VIEW_SUPPORT->value,
                    AdminPermission::MANAGE_TICKETS->value,
                    AdminPermission::VIEW_ANALYTICS->value,
                ],
                'is_system_role' => true,
                'priority' => 50,
                'is_active' => true,
            ]
        );
    }

    private function createMarchandRoles(): void
    {
        // Propriétaire
        MarchandRole::updateOrCreate(
            ['slug' => 'owner'],
            [
                'name' => 'Propriétaire',
                'description' => 'Accès complet à toutes les fonctionnalités de la boutique',
                'permissions' => collect(MarchandPermission::cases())->pluck('value')->toArray(),
                'is_system_role' => true,
                'priority' => 100,
                'is_active' => true,
            ]
        );

        // Gestionnaire
        MarchandRole::updateOrCreate(
            ['slug' => 'manager'],
            [
                'name' => 'Gestionnaire',
                'description' => 'Gestion complète de la boutique sauf suppression du compte',
                'permissions' => [
                    MarchandPermission::VIEW_PRODUCTS->value,
                    MarchandPermission::CREATE_PRODUCTS->value,
                    MarchandPermission::EDIT_PRODUCTS->value,
                    MarchandPermission::DELETE_PRODUCTS->value,
                    MarchandPermission::MANAGE_PRODUCTS->value,
                    MarchandPermission::MANAGE_INVENTORY->value,
                    MarchandPermission::MANAGE_CATEGORIES->value,
                    MarchandPermission::VIEW_ORDERS->value,
                    MarchandPermission::MANAGE_ORDERS->value,
                    MarchandPermission::PROCESS_ORDERS->value,
                    MarchandPermission::MANAGE_SHIPPING->value,
                    MarchandPermission::VIEW_ORDER_DETAILS->value,
                    MarchandPermission::CANCEL_ORDERS->value,
                    MarchandPermission::VIEW_FINANCES->value,
                    MarchandPermission::MANAGE_FINANCES->value,
                    MarchandPermission::VIEW_PAYMENTS->value,
                    MarchandPermission::MANAGE_SUBSCRIPTIONS->value,
                    MarchandPermission::VIEW_COMMISSIONS->value,
                    MarchandPermission::MANAGE_PRICING->value,
                    MarchandPermission::VIEW_TEAM->value,
                    MarchandPermission::MANAGE_TEAM->value,
                    MarchandPermission::INVITE_USERS->value,
                    MarchandPermission::REMOVE_USERS->value,
                    MarchandPermission::MANAGE_ROLES->value,
                    MarchandPermission::VIEW_ANALYTICS->value,
                    MarchandPermission::VIEW_REPORTS->value,
                    MarchandPermission::EXPORT_DATA->value,
                    MarchandPermission::VIEW_PERFORMANCE->value,
                    MarchandPermission::VIEW_SUPPORT->value,
                    MarchandPermission::MANAGE_DISPUTES->value,
                    MarchandPermission::MANAGE_REVIEWS->value,
                    MarchandPermission::CONTACT_SUPPORT->value,
                    MarchandPermission::MANAGE_SETTINGS->value,
                    MarchandPermission::MANAGE_PROFILE->value,
                    MarchandPermission::MANAGE_NOTIFICATIONS->value,
                    MarchandPermission::VIEW_LOGS->value,
                    MarchandPermission::MANAGE_PROMOTIONS->value,
                    MarchandPermission::MANAGE_COUPONS->value,
                    MarchandPermission::VIEW_MARKETING->value,
                    MarchandPermission::MANAGE_BILLING->value,
                    MarchandPermission::ACCESS_API->value,
                ],
                'is_system_role' => true,
                'priority' => 80,
                'is_active' => true,
            ]
        );

        // Gestionnaire Produits
        MarchandRole::updateOrCreate(
            ['slug' => 'product_manager'],
            [
                'name' => 'Gestionnaire Produits',
                'description' => 'Gestion complète des produits et du catalogue',
                'permissions' => [
                    MarchandPermission::VIEW_PRODUCTS->value,
                    MarchandPermission::CREATE_PRODUCTS->value,
                    MarchandPermission::EDIT_PRODUCTS->value,
                    MarchandPermission::DELETE_PRODUCTS->value,
                    MarchandPermission::MANAGE_PRODUCTS->value,
                    MarchandPermission::MANAGE_INVENTORY->value,
                    MarchandPermission::MANAGE_CATEGORIES->value,
                    MarchandPermission::VIEW_ANALYTICS->value,
                    MarchandPermission::VIEW_REPORTS->value,
                    MarchandPermission::EXPORT_DATA->value,
                    MarchandPermission::MANAGE_PROMOTIONS->value,
                    MarchandPermission::MANAGE_COUPONS->value,
                    MarchandPermission::VIEW_MARKETING->value,
                ],
                'is_system_role' => true,
                'priority' => 70,
                'is_active' => true,
            ]
        );

        // Gestionnaire Commandes
        MarchandRole::updateOrCreate(
            ['slug' => 'order_manager'],
            [
                'name' => 'Gestionnaire Commandes',
                'description' => 'Gestion des commandes et expéditions',
                'permissions' => [
                    MarchandPermission::VIEW_ORDERS->value,
                    MarchandPermission::MANAGE_ORDERS->value,
                    MarchandPermission::PROCESS_ORDERS->value,
                    MarchandPermission::MANAGE_SHIPPING->value,
                    MarchandPermission::VIEW_ORDER_DETAILS->value,
                    MarchandPermission::CANCEL_ORDERS->value,
                    MarchandPermission::VIEW_PRODUCTS->value,
                    MarchandPermission::MANAGE_INVENTORY->value,
                    MarchandPermission::VIEW_ANALYTICS->value,
                    MarchandPermission::VIEW_REPORTS->value,
                    MarchandPermission::EXPORT_DATA->value,
                    MarchandPermission::VIEW_SUPPORT->value,
                    MarchandPermission::MANAGE_DISPUTES->value,
                    MarchandPermission::CONTACT_SUPPORT->value,
                ],
                'is_system_role' => true,
                'priority' => 60,
                'is_active' => true,
            ]
        );

        // Comptable
        MarchandRole::updateOrCreate(
            ['slug' => 'accountant'],
            [
                'name' => 'Comptable',
                'description' => 'Gestion des finances et de la comptabilité',
                'permissions' => [
                    MarchandPermission::VIEW_FINANCES->value,
                    MarchandPermission::MANAGE_FINANCES->value,
                    MarchandPermission::VIEW_PAYMENTS->value,
                    MarchandPermission::VIEW_COMMISSIONS->value,
                    MarchandPermission::MANAGE_PRICING->value,
                    MarchandPermission::VIEW_ANALYTICS->value,
                    MarchandPermission::VIEW_REPORTS->value,
                    MarchandPermission::EXPORT_DATA->value,
                    MarchandPermission::VIEW_PERFORMANCE->value,
                    MarchandPermission::MANAGE_BILLING->value,
                    MarchandPermission::VIEW_ORDERS->value,
                    MarchandPermission::VIEW_ORDER_DETAILS->value,
                ],
                'is_system_role' => true,
                'priority' => 50,
                'is_active' => true,
            ]
        );

        // Employé
        MarchandRole::updateOrCreate(
            ['slug' => 'employee'],
            [
                'name' => 'Employé',
                'description' => 'Accès de base aux fonctionnalités essentielles',
                'permissions' => [
                    MarchandPermission::VIEW_PRODUCTS->value,
                    MarchandPermission::VIEW_ORDERS->value,
                    MarchandPermission::VIEW_ORDER_DETAILS->value,
                    MarchandPermission::PROCESS_ORDERS->value,
                    MarchandPermission::VIEW_SUPPORT->value,
                    MarchandPermission::CONTACT_SUPPORT->value,
                    MarchandPermission::MANAGE_PROFILE->value,
                    MarchandPermission::VIEW_ANALYTICS->value,
                ],
                'is_system_role' => true,
                'priority' => 30,
                'is_active' => true,
            ]
        );
    }
}
