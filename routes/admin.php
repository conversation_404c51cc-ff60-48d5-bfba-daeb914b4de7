<?php

use App\Http\Controllers\Admin\ImportController;
use App\Http\Controllers\Admin\MerchantValidationController;
use Illuminate\Support\Facades\Route;

// Routes d'administration protégées par le middleware auth et admin
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Routes d'importation
    Route::get('/import', [ImportController::class, 'index'])->name('import.index');
    Route::post('/import/categories', [ImportController::class, 'importCategories'])->name('import.categories');
    Route::get('/import/categories/preview', [ImportController::class, 'previewCategories'])->name('import.preview-categories');
    Route::post('/import/categories/confirm', [ImportController::class, 'confirmImportCategories'])->name('import.confirm-categories');
    Route::post('/import/products', [ImportController::class, 'importProducts'])->name('import.products');
    Route::get('/import/categories/template', [ImportController::class, 'downloadCategoriesTemplate'])->name('import.categories.template');
    Route::get('/import/products/template', [ImportController::class, 'downloadProductsTemplate'])->name('import.products.template');

    // Routes pour les documents de validation marchand
    Route::get('/merchant-validation/{validation}/document/{document}/view', [MerchantValidationController::class, 'viewDocument'])
        ->name('merchant-validation.view-document');
    Route::get('/merchant-validation/{validation}/document/{document}/download', [MerchantValidationController::class, 'downloadDocument'])
        ->name('merchant-validation.download-document');
    Route::get('/merchant-validation/{validation}/document/{document}/preview', [MerchantValidationController::class, 'previewDocument'])
        ->name('merchant-validation.preview-document');
    Route::get('/merchant-validation/{validation}/documents/download-all', [MerchantValidationController::class, 'downloadAllDocuments'])
        ->name('merchant-validation.download-all-documents');
});

Route::get('/admin/importations/download-categories-template', [ImportController::class, 'downloadCategoriesTemplate'])
    ->name('filament.admin.resources.importations.download-categories-template')
    ->middleware(['web', 'auth']);

Route::get('/admin/importations/download-products-template', [ImportController::class, 'downloadProductsTemplate'])
    ->name('filament.admin.resources.importations.download-products-template')
    ->middleware(['web', 'auth']);

Route::get('/merchant/importations/download-products-template', [ImportController::class, 'downloadProductsTemplate'])
    ->name('filament.marchand.resources.importations.download-products-template')
    ->middleware(['web', 'auth']);