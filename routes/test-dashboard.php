<?php

use App\Services\DashboardStatsService;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Routes de Test pour Dashboard Multi-Marchands
|--------------------------------------------------------------------------
|
| Routes temporaires pour tester le nouveau système de dashboard
| À supprimer en production
|
*/

Route::prefix('test-dashboard')->group(function () {
    
    // Test des statistiques marchand
    Route::get('/marchand/{id}', function ($id) {
        try {
            $dashboardService = new DashboardStatsService();
            $stats = $dashboardService->getStatistiquesMarchand($id);
            
            return response()->json([
                'success' => true,
                'marchand_id' => $id,
                'stats' => $stats,
                'timestamp' => now()->toISOString()
            ], 200, [], JSON_PRETTY_PRINT);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => config('app.debug') ? $e->getTraceAsString() : null
            ], 500);
        }
    })->name('test.dashboard.marchand');
    
    // Test des statistiques admin
    Route::get('/admin', function () {
        try {
            $dashboardService = new DashboardStatsService();
            $stats = $dashboardService->getStatistiquesAdmin();
            
            return response()->json([
                'success' => true,
                'stats' => $stats,
                'timestamp' => now()->toISOString()
            ], 200, [], JSON_PRETTY_PRINT);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => config('app.debug') ? $e->getTraceAsString() : null
            ], 500);
        }
    })->name('test.dashboard.admin');
    
    // Test de la connexion cross-database
    Route::get('/database-test', function () {
        try {
            $results = [];
            
            // Test connexion principale
            $results['main_db'] = [
                'connection' => 'mysql',
                'marchands_count' => \App\Models\Marchand::count(),
                'commandes_count' => \App\Models\Commande::count(),
                'versements_count' => \App\Models\Versement::count(),
                'commissions_count' => \App\Models\Commission::count(),
            ];
            
            // Test connexion lorrelei
            try {
                $results['lorrelei_db'] = [
                    'connection' => 'lorrelei',
                    'commandes_principales_count' => \DB::connection('lorrelei')->table('commandes_principales')->count(),
                    'sous_commandes_count' => \DB::connection('lorrelei')->table('sous_commandes_vendeur')->count(),
                    'connection_status' => 'OK'
                ];
            } catch (\Exception $e) {
                $results['lorrelei_db'] = [
                    'connection' => 'lorrelei',
                    'connection_status' => 'ERROR',
                    'error' => $e->getMessage()
                ];
            }
            
            return response()->json([
                'success' => true,
                'database_tests' => $results,
                'timestamp' => now()->toISOString()
            ], 200, [], JSON_PRETTY_PRINT);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => config('app.debug') ? $e->getTraceAsString() : null
            ], 500);
        }
    })->name('test.dashboard.database');
    
    // Test du top marchands
    Route::get('/top-marchands', function () {
        try {
            $dashboardService = new DashboardStatsService();
            $topMarchands = $dashboardService->getTopMarchands(
                now()->startOfMonth(),
                now()->endOfMonth(),
                10
            );
            
            return response()->json([
                'success' => true,
                'periode' => [
                    'debut' => now()->startOfMonth()->toDateString(),
                    'fin' => now()->endOfMonth()->toDateString()
                ],
                'top_marchands' => $topMarchands,
                'count' => count($topMarchands),
                'timestamp' => now()->toISOString()
            ], 200, [], JSON_PRETTY_PRINT);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => config('app.debug') ? $e->getTraceAsString() : null
            ], 500);
        }
    })->name('test.dashboard.top-marchands');
    
    // Page de test avec liens
    Route::get('/', function () {
        $baseUrl = url('/test-dashboard');
        
        return response(
            '<!DOCTYPE html>
            <html>
            <head>
                <title>Test Dashboard Multi-Marchands</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .test-link { display: block; margin: 10px 0; padding: 10px; background: #f0f0f0; text-decoration: none; border-radius: 5px; }
                    .test-link:hover { background: #e0e0e0; }
                    .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
                </style>
            </head>
            <body>
                <h1>🧪 Test Dashboard Multi-Marchands</h1>
                
                <div class="warning">
                    <strong>⚠️ Routes de test temporaires</strong><br>
                    Ces routes sont destinées au développement et doivent être supprimées en production.
                </div>
                
                <h2>Tests disponibles :</h2>
                
                <a href="' . $baseUrl . '/database-test" class="test-link">
                    🗄️ Test des connexions base de données
                </a>
                
                <a href="' . $baseUrl . '/admin" class="test-link">
                    👑 Statistiques Admin
                </a>
                
                <a href="' . $baseUrl . '/marchand/1" class="test-link">
                    🏪 Statistiques Marchand (ID: 1)
                </a>
                
                <a href="' . $baseUrl . '/top-marchands" class="test-link">
                    🏆 Top Marchands ce mois
                </a>
                
                <h2>Instructions :</h2>
                <ol>
                    <li>Cliquez sur les liens ci-dessus pour tester chaque fonctionnalité</li>
                    <li>Vérifiez que les données sont cohérentes</li>
                    <li>Testez avec différents IDs de marchands</li>
                    <li>Vérifiez les performances (temps de réponse)</li>
                </ol>
                
                <p><em>Généré le ' . now()->format('d/m/Y H:i:s') . '</em></p>
            </body>
            </html>'
        )->header('Content-Type', 'text/html');
    })->name('test.dashboard.index');
});
