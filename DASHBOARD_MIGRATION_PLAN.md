# 🔄 Plan de Migration des Dashboards vers le Nouveau Système de Commandes

## 📊 **PROBLÈME IDENTIFIÉ**

Les dashboards utilisent encore l'ancien modèle `Commande` alors que nous avons maintenant :
- **`CommandePrincipale`** : Commande globale du client
- **`SousCommandeVendeur`** : Sous-commandes par marchand
- **`ArticleCommande`** : Articles liés aux sous-commandes

## 🎯 **STRUCTURE DU NOUVEAU SYSTÈME**

### **CommandePrincipale**
```php
// Table: commandes_principales
- id, client_id, numero_commande
- montant_total_ht, montant_total_ttc, montant_commission_plateforme
- statut_global (EnAttente, PayementConfirme, EnTraitement, PartielExpedié, TotalementExpedié, TotalementLivré, Terminé)
- statut_paiement (EnAttente, EnCours, Complété, Écho<PERSON>, Remboursé)
- date_commande, date_livraison_souhaitee, date_paiement
- nombre_articles_total, nombre_marchands, nombre_sous_commandes
```

### **SousCommandeVendeur**
```php
// Table: sous_commandes_vendeur
- id, commande_principale_id, marchand_id, numero_sous_commande
- montant_ht, montant_ttc, montant_commission, montant_versement_marchand
- statut (EnAttente, Confirmé, EnPreparation, PrêtExpédition, Expédié, Livré, Terminé, Annulé)
- frais_livraison, transporteur, numero_suivi
- date_creation, date_confirmation_marchand, date_expedition_reelle, date_livraison_reelle
- versement_effectué, date_versement, versement_id
```

## 🔧 **RESOURCES À MIGRER**

### **Dashboard Admin**
1. **`CommandeResource`** → Utiliser `CommandePrincipale`
2. **`GlobalOrdersWidget`** → Utiliser `CommandePrincipale`
3. **`StatsOverview`** → Migrer les statistiques vers le nouveau système
4. **`PayoutsManagementWidget`** → Utiliser `SousCommandeVendeur` pour les versements

### **Dashboard Marchand**
1. **`CommandeResource`** → Utiliser `SousCommandeVendeur` pour le marchand connecté
2. **`LatestOrders`** → Utiliser `SousCommandeVendeur`
3. **`MarchandStatsOverview`** → Migrer vers `SousCommandeVendeur`
4. **`VersementsWidget`** → Déjà correct (utilise `Versement`)

## 📋 **PLAN D'ACTION**

### **Étape 1 : Analyser les Requêtes Actuelles**
```bash
# Identifier toutes les utilisations du modèle Commande
grep -r "Commande::" app/Filament/
grep -r "use App\Models\Commande" app/Filament/
```

### **Étape 2 : Créer des Adapters/Services**
```php
// app/Services/CommandeAdapterService.php
class CommandeAdapterService 
{
    // Méthodes pour convertir les anciennes requêtes vers le nouveau système
    public function getCommandesForAdmin()
    public function getCommandesForMarchand($marchandId)
    public function getStatistiquesCommandes()
}
```

### **Étape 3 : Migrer les Resources**

#### **CommandeResource (Admin)**
```php
// Avant (ancien système)
protected static ?string $model = Commande::class;

// Après (nouveau système)
protected static ?string $model = CommandePrincipale::class;

// Adapter les colonnes et relations
Tables\Columns\TextColumn::make('numero_commande')
Tables\Columns\TextColumn::make('client.nom')
Tables\Columns\TextColumn::make('statut_global')
Tables\Columns\TextColumn::make('montant_total_ttc')
Tables\Columns\TextColumn::make('nombre_marchands')
Tables\Columns\TextColumn::make('sousCommandes_count')->counts('sousCommandes')
```

#### **CommandeResource (Marchand)**
```php
// Avant (ancien système)
protected static ?string $model = Commande::class;

// Après (nouveau système)
protected static ?string $model = SousCommandeVendeur::class;

// Filtrer par marchand connecté
public static function getEloquentQuery(): Builder
{
    $marchandId = auth()->user()->marchand?->id;
    return parent::getEloquentQuery()
        ->where('marchand_id', $marchandId)
        ->with(['commandePrincipale.client']);
}
```

### **Étape 4 : Migrer les Widgets**

#### **StatsOverview (Admin)**
```php
// Remplacer les requêtes Commande par CommandePrincipale
$totalCommandes = CommandePrincipale::count();
$commandesMois = CommandePrincipale::whereMonth('date_commande', now()->month)->count();
$revenusMois = CommandePrincipale::whereMonth('date_commande', now()->month)
    ->sum('montant_total_ttc');
```

#### **MarchandStatsOverview**
```php
// Utiliser SousCommandeVendeur pour les stats marchand
$marchandId = auth()->user()->marchand?->id;
$commandesMois = SousCommandeVendeur::where('marchand_id', $marchandId)
    ->whereMonth('date_creation', now()->month)->count();
$revenusMois = SousCommandeVendeur::where('marchand_id', $marchandId)
    ->whereMonth('date_creation', now()->month)
    ->sum('montant_versement_marchand');
```

## 🔄 **STRATÉGIE DE MIGRATION**

### **Phase 1 : Préparation (Immédiate)**
1. ✅ Analyser la structure actuelle
2. ⏳ Créer des services d'adaptation
3. ⏳ Identifier toutes les dépendances

### **Phase 2 : Migration Progressive**
1. ⏳ Migrer les widgets de statistiques
2. ⏳ Migrer les resources de commandes
3. ⏳ Tester avec des données réelles

### **Phase 3 : Validation et Nettoyage**
1. ⏳ Valider que toutes les fonctionnalités marchent
2. ⏳ Nettoyer l'ancien code
3. ⏳ Mettre à jour la documentation

## 🚨 **POINTS D'ATTENTION**

### **Compatibilité Descendante**
- Garder l'ancien modèle `Commande` pendant la transition
- Utiliser des flags pour basculer entre ancien/nouveau système
- Tester avec des données de production

### **Relations Complexes**
- `CommandePrincipale` → `SousCommandeVendeur` (1:N)
- `SousCommandeVendeur` → `ArticleCommande` (1:N)
- `SousCommandeVendeur` → `Versement` (N:1)

### **Statuts et Logique Métier**
- Mapper les anciens statuts vers les nouveaux
- Adapter la logique de calcul des commissions
- Gérer les transitions d'état

## 📊 **MÉTRIQUES DE SUCCÈS**

### **Fonctionnalité**
- ✅ Toutes les statistiques affichent les bonnes données
- ✅ Les marchands voient leurs sous-commandes
- ✅ Les admins voient les commandes principales
- ✅ Les calculs de commissions sont corrects

### **Performance**
- ✅ Temps de chargement des dashboards maintenu
- ✅ Requêtes optimisées avec les bonnes relations
- ✅ Pas de N+1 queries

### **Sécurité**
- ✅ Les marchands ne voient que leurs sous-commandes
- ✅ Les permissions sont respectées
- ✅ Pas de fuite de données entre marchands

## 🎯 **PROCHAINES ACTIONS IMMÉDIATES**

1. **Créer le service d'adaptation** pour les requêtes
2. **Migrer StatsOverview** vers le nouveau système
3. **Tester les widgets** avec des données réelles
4. **Migrer CommandeResource** (Admin puis Marchand)
5. **Valider les calculs** de commissions et versements

Cette migration est **critique** pour la cohérence du système et doit être **prioritaire** avant de continuer la sécurisation des resources restantes.
