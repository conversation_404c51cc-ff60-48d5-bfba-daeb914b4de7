<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SousCommandeVendeur extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'sous_commandes_vendeur';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'commande_principale_id',
        'marchand_id',
        'numero_sous_commande',
        'montant_ht',
        'montant_ttc',
        'montant_taxes',
        'montant_commission',
        'montant_versement_marchand',
        'taux_commission',
        'statut',
        'frais_livraison',
        'transporteur',
        'numero_suivi',
        'url_suivi',
        'date_creation',
        'date_confirmation_marchand',
        'date_expedition_prevue',
        'date_expedition_reelle',
        'date_livraison_prevue',
        'date_livraison_reelle',
        'delai_preparation_jours',
        'instructions_marchand',
        'message_client',
        'notes_internes',
        'nombre_articles',
        'poids_total',
        'versement_effectué',
        'date_versement',
        'versement_id',
        'zone_livraison_id',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'montant_ht' => 'decimal:2',
        'montant_ttc' => 'decimal:2',
        'montant_taxes' => 'decimal:2',
        'montant_commission' => 'decimal:2',
        'montant_versement_marchand' => 'decimal:2',
        'taux_commission' => 'decimal:2',
        'frais_livraison' => 'decimal:2',
        'poids_total' => 'decimal:2',
        'date_creation' => 'datetime',
        'date_confirmation_marchand' => 'datetime',
        'date_expedition_prevue' => 'date',
        'date_expedition_reelle' => 'datetime',
        'date_livraison_prevue' => 'date',
        'date_livraison_reelle' => 'datetime',
        'date_versement' => 'datetime',
        'delai_preparation_jours' => 'integer',
        'nombre_articles' => 'integer',
        'versement_effectué' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * Relation avec la commande principale
     */
    public function commandePrincipale(): BelongsTo
    {
        return $this->belongsTo(CommandePrincipale::class);
    }

    /**
     * Relation avec le marchand
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Relation avec la zone de livraison
     */
    public function zoneLivraison(): BelongsTo
    {
        return $this->belongsTo(ZoneLivraison::class);
    }

    /**
     * Relation avec les articles de commande
     */
    public function articles(): HasMany
    {
        return $this->hasMany(ArticleCommande::class, 'sous_commande_id');
    }

    /**
     * Relation avec les remboursements
     */
    public function remboursements(): HasMany
    {
        return $this->hasMany(Remboursement::class, 'sous_commande_id');
    }

    /**
     * Scopes pour les requêtes courantes
     */
    public function scopePourMarchand($query, $marchandId)
    {
        return $query->where('marchand_id', $marchandId);
    }

    public function scopeEnAttente($query)
    {
        return $query->where('statut', 'EnAttente');
    }

    public function scopeExpediee($query)
    {
        return $query->where('statut', 'Expédié');
    }

    public function scopeLivree($query)
    {
        return $query->where('statut', 'Livré');
    }

    public function scopeVersementEnAttente($query)
    {
        return $query->where('versement_effectué', false)
                    ->whereIn('statut', ['Livré', 'Terminé']);
    }

    /**
     * Méthodes utilitaires
     */
    
    /**
     * Vérifie si la sous-commande peut être expédiée
     */
    public function peutÊtreExpédiée(): bool
    {
        return in_array($this->statut, ['Confirmé', 'EnPreparation', 'PrêtExpédition']);
    }

    /**
     * Vérifie si la sous-commande peut être annulée
     */
    public function peutÊtreAnnulée(): bool
    {
        return in_array($this->statut, ['EnAttente', 'Confirmé', 'EnPreparation']);
    }

    /**
     * Vérifie si un versement est dû
     */
    public function versementEstDû(): bool
    {
        return !$this->versement_effectué && 
               in_array($this->statut, ['Livré']) &&
               $this->montant_versement_marchand > 0;
    }

    /**
     * Calcule le délai de livraison estimé
     */
    public function getDelaiLivraisonEstime(): ?int
    {
        if (!$this->delai_preparation_jours || !$this->zoneLivraison) {
            return null;
        }

        // Logique basée sur la zone de livraison
        $delaiTransport = match($this->zoneLivraison->type) {
            'Quartier' => 1,
            'Ville' => 2,
            'Region' => 3,
            'Pays' => 5,
            default => 3
        };

        return $this->delai_preparation_jours + $delaiTransport;
    }

    /**
     * Génère un numéro de sous-commande unique
     */
    public static function genererNumeroSousCommande(int $commandePrincipaleId, int $marchandId): string
    {
        $prefix = 'SC';
        $cmdId = str_pad($commandePrincipaleId, 6, '0', STR_PAD_LEFT);
        $marchId = str_pad($marchandId, 4, '0', STR_PAD_LEFT);
        
        return $prefix . $cmdId . $marchId;
    }

    /**
     * Met à jour le statut et notifie la commande principale
     */
    public function changerStatut(string $nouveauStatut, ?string $notes = null): void
    {
        $ancienStatut = $this->statut;
        $this->statut = $nouveauStatut;
        
        if ($notes) {
            $this->notes_internes = ($this->notes_internes ?? '') . "\n" . now()->format('Y-m-d H:i:s') . ": $notes";
        }

        // Mettre à jour les dates selon le statut
        match($nouveauStatut) {
            'Confirmé' => $this->date_confirmation_marchand = now(),
            'Expédié' => $this->date_expedition_reelle = now(),
            'Livré' => $this->date_livraison_reelle = now(),
            default => null
        };

        $this->save();

        // Mettre à jour le statut de la commande principale
        $this->commandePrincipale->mettreAJourStatutGlobal();

        // Déclencher des événements ou notifications si nécessaire
        // event(new SousCommandeStatutChanged($this, $ancienStatut, $nouveauStatut));
    }

    /**
     * Calcule le montant net pour le marchand après commission
     */
    public function calculerMontantVersement(): void
    {
        $this->montant_versement_marchand = $this->montant_ttc - $this->montant_commission - $this->frais_livraison;
        $this->save();
    }
}
