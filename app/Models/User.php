<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Traits\HasAdminRoles;
use App\Traits\HasMarchandRoles;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable implements FilamentUser
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable, HasAdminRoles, HasMarchandRoles;

    /**
     * The relationships that should be eager loaded.
     *
     * @var array
     */
    protected $with = ['client', 'marchand', 'adresses'];

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'avatar',
        'password',
        'role',
        'is_active',
        'is_admin',
        'email_verified_at',
        'last_login_at',
        'email_verification_token',
        'password_reset_token',
        'password_reset_expires_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_admin' => 'boolean',
            'created_at' => 'timestamp',
            'last_login_at' => 'timestamp',
            'is_active' => 'boolean',
            'password_reset_expires_at' => 'datetime',
        ];
    }

    /**
     * Relations
     */

    public function clients(): HasMany
    {
        return $this->hasMany(Client::class);
    }

    public function client(): HasOne
    {
        return $this->hasOne(Client::class);
    }

    public function marchands(): HasMany
    {
        return $this->hasMany(Marchand::class);
    }

    public function marchand(): HasOne
    {
        return $this->hasOne(Marchand::class);
    }

    public function adresses(): HasMany
    {
        return $this->hasMany(Adresse::class);
    }

    public function merchantValidation(): HasOne
    {
        return $this->hasOne(MerchantValidation::class);
    }

    /**
     * Vérifie si l'utilisateur est un administrateur
     */
    public function isAdmin(): bool
    {
        return $this->is_admin === true;
    }

    /**
     * Vérifie si l'utilisateur est un marchand
     */
    public function isMarchand(): bool
    {
        return $this->marchand()->exists();
    }

    /**
     * Vérifie si l'utilisateur est un marchand validé
     */
    public function isMarchandValide(): bool
    {
        $marchand = $this->marchand;
        return $marchand && $marchand->statut_validation === 'valide';
    }

    public function canAccessPanel(Panel $panel): bool
    {
        if ($panel->getId() === 'admin') {
            return ($this->role === 'Admin' || $this->role === 'super_admin' || $this->is_admin) ;
        }

        if ($panel->getId() === 'marchand') {
            return $this->role === 'Marchand';
        }

        if ($panel->getId() === 'admin') {
            // Nouveau système : vérifier si l'utilisateur a un AdminUser actif
            return $this->is_admin &&
                ($this->adminUser?->is_active ?? false || $this->isSuperAdmin());
        }

        if ($panel->getId() === 'marchand') {
            // Nouveau système : vérifier si l'utilisateur a un MarchandUser actif ou est propriétaire
            return $this->role === 'Marchand' &&
                ($this->marchandUsers()->where('is_active', true)->exists() ||
                    $this->marchand?->user_id === $this->id);
        }

        return false;
    }

    public function isSuperAdmin(): bool
    {
        return $this->role === 'super_admin';
    }

    public function getName(): string
    {
        // Si le champ name est rempli, on l'utilise
        if ($this->name) {
            return $this->name;
        }

        // Si l'utilisateur est un client ou un marchand, on récupère son nom
        if ($this->role === 'Client' && $this->clients->first()) {
            return $this->clients->first()->prenom . ' ' . $this->clients->first()->nom;
        }

        if ($this->role === 'Marchand' && $this->marchands->first()) {
            return $this->marchands->first()->nomEntreprise;
        }

        // Sinon, on utilise l'email
        return $this->email;
    }
}
