<?php

namespace App\Models;

use App\Enums\MarchandPermission;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class MarchandUser extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'marchand_id',
        'role_id',
        'permissions',
        'access_level',
        'is_active',
        'last_login_at',
        'invited_by',
        'invitation_token',
        'invitation_expires_at',
        'notes',
    ];

    protected $casts = [
        'permissions' => 'array',
        'is_active' => 'boolean',
        'last_login_at' => 'datetime',
        'invitation_expires_at' => 'datetime',
    ];

    /**
     * Relation avec l'utilisateur
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relation avec le marchand
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Relation avec le rôle marchand
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(MarchandRole::class, 'role_id');
    }

    /**
     * Relation avec l'utilisateur qui a invité
     */
    public function inviter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'invited_by');
    }

    /**
     * Vérifier si l'utilisateur marchand a une permission spécifique
     */
    public function hasPermission(MarchandPermission|string $permission): bool
    {
        $permissionValue = $permission instanceof MarchandPermission ? $permission->value : $permission;
        
        // Vérifier les permissions spécifiques de l'utilisateur
        if (in_array($permissionValue, $this->permissions ?? [])) {
            return true;
        }
        
        // Vérifier les permissions du rôle
        return $this->role?->hasPermission($permissionValue) ?? false;
    }

    /**
     * Vérifier si l'utilisateur est propriétaire
     */
    public function isOwner(): bool
    {
        return $this->access_level === 'owner' || 
               $this->role?->slug === 'owner';
    }

    /**
     * Vérifier si l'utilisateur est gestionnaire ou propriétaire
     */
    public function isManager(): bool
    {
        return in_array($this->access_level, ['owner', 'manager']) ||
               $this->role?->isManagerRole();
    }

    /**
     * Vérifier si l'utilisateur peut inviter d'autres utilisateurs
     */
    public function canInviteUsers(): bool
    {
        return $this->hasPermission(MarchandPermission::INVITE_USERS);
    }

    /**
     * Vérifier si l'utilisateur peut supprimer d'autres utilisateurs
     */
    public function canRemoveUsers(): bool
    {
        return $this->hasPermission(MarchandPermission::REMOVE_USERS);
    }

    /**
     * Obtenir toutes les permissions (rôle + spécifiques)
     */
    public function getAllPermissions(): array
    {
        $rolePermissions = $this->role?->permissions ?? [];
        $userPermissions = $this->permissions ?? [];
        
        return array_unique(array_merge($rolePermissions, $userPermissions));
    }

    /**
     * Ajouter une permission spécifique à l'utilisateur
     */
    public function addPermission(MarchandPermission|string $permission): void
    {
        $permissionValue = $permission instanceof MarchandPermission ? $permission->value : $permission;
        $permissions = $this->permissions ?? [];
        
        if (!in_array($permissionValue, $permissions)) {
            $permissions[] = $permissionValue;
            $this->update(['permissions' => $permissions]);
        }
    }

    /**
     * Retirer une permission spécifique de l'utilisateur
     */
    public function removePermission(MarchandPermission|string $permission): void
    {
        $permissionValue = $permission instanceof MarchandPermission ? $permission->value : $permission;
        $permissions = $this->permissions ?? [];
        
        $permissions = array_filter($permissions, fn($p) => $p !== $permissionValue);
        $this->update(['permissions' => array_values($permissions)]);
    }

    /**
     * Générer un token d'invitation
     */
    public function generateInvitationToken(): string
    {
        $token = Str::random(64);
        $this->update([
            'invitation_token' => $token,
            'invitation_expires_at' => now()->addDays(7), // Expire dans 7 jours
        ]);
        
        return $token;
    }

    /**
     * Vérifier si l'invitation est valide
     */
    public function isInvitationValid(): bool
    {
        return $this->invitation_token && 
               $this->invitation_expires_at && 
               $this->invitation_expires_at->isFuture();
    }

    /**
     * Accepter l'invitation
     */
    public function acceptInvitation(): void
    {
        $this->update([
            'invitation_token' => null,
            'invitation_expires_at' => null,
            'is_active' => true,
        ]);
    }

    /**
     * Mettre à jour la dernière connexion
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    /**
     * Scope pour les utilisateurs actifs
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope pour un marchand spécifique
     */
    public function scopeForMarchand($query, int $marchandId)
    {
        return $query->where('marchand_id', $marchandId);
    }

    /**
     * Scope pour un niveau d'accès spécifique
     */
    public function scopeByAccessLevel($query, string $accessLevel)
    {
        return $query->where('access_level', $accessLevel);
    }

    /**
     * Scope pour les invitations en attente
     */
    public function scopePendingInvitations($query)
    {
        return $query->whereNotNull('invitation_token')
                    ->where('invitation_expires_at', '>', now());
    }

    /**
     * Obtenir le nom complet de l'utilisateur
     */
    public function getFullNameAttribute(): string
    {
        return $this->user?->name ?? 'Utilisateur inconnu';
    }

    /**
     * Obtenir l'email de l'utilisateur
     */
    public function getEmailAttribute(): string
    {
        return $this->user?->email ?? '';
    }

    /**
     * Obtenir le label du niveau d'accès
     */
    public function getAccessLevelLabelAttribute(): string
    {
        return match($this->access_level) {
            'owner' => 'Propriétaire',
            'manager' => 'Gestionnaire',
            'employee' => 'Employé',
            'viewer' => 'Observateur',
            default => ucfirst($this->access_level ?? 'Non défini'),
        };
    }

    /**
     * Obtenir le statut de l'invitation
     */
    public function getInvitationStatusAttribute(): string
    {
        if (!$this->invitation_token) {
            return 'Acceptée';
        }
        
        if ($this->invitation_expires_at && $this->invitation_expires_at->isPast()) {
            return 'Expirée';
        }
        
        return 'En attente';
    }
}
