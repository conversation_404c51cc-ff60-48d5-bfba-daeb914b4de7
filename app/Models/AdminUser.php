<?php

namespace App\Models;

use App\Enums\AdminPermission;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdminUser extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'role_id',
        'permissions',
        'department',
        'access_level',
        'is_active',
        'last_login_at',
        'created_by',
        'notes',
    ];

    protected $casts = [
        'permissions' => 'array',
        'is_active' => 'boolean',
        'last_login_at' => 'datetime',
    ];

    /**
     * Relation avec l'utilisateur
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relation avec le rôle admin
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(AdminRole::class, 'role_id');
    }

    /**
     * Relation avec l'utilisateur qui a créé cet admin
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Vérifier si l'utilisateur admin a une permission spécifique
     */
    public function hasPermission(AdminPermission|string $permission): bool
    {
        $permissionValue = $permission instanceof AdminPermission ? $permission->value : $permission;
        
        // Vérifier les permissions spécifiques de l'utilisateur
        if (in_array($permissionValue, $this->permissions ?? [])) {
            return true;
        }
        
        // Vérifier les permissions du rôle
        return $this->role?->hasPermission($permissionValue) ?? false;
    }

    /**
     * Vérifier si l'utilisateur est super admin
     */
    public function isSuperAdmin(): bool
    {
        return $this->access_level === 'super_admin' || 
               $this->role?->slug === 'super_admin';
    }

    /**
     * Vérifier si l'utilisateur peut supprimer d'autres admins
     */
    public function canDeleteAdmins(): bool
    {
        return $this->isSuperAdmin() && $this->hasPermission(AdminPermission::DELETE_ADMINS);
    }

    /**
     * Obtenir toutes les permissions (rôle + spécifiques)
     */
    public function getAllPermissions(): array
    {
        $rolePermissions = $this->role?->permissions ?? [];
        $userPermissions = $this->permissions ?? [];
        
        return array_unique(array_merge($rolePermissions, $userPermissions));
    }

    /**
     * Ajouter une permission spécifique à l'utilisateur
     */
    public function addPermission(AdminPermission|string $permission): void
    {
        $permissionValue = $permission instanceof AdminPermission ? $permission->value : $permission;
        $permissions = $this->permissions ?? [];
        
        if (!in_array($permissionValue, $permissions)) {
            $permissions[] = $permissionValue;
            $this->update(['permissions' => $permissions]);
        }
    }

    /**
     * Retirer une permission spécifique de l'utilisateur
     */
    public function removePermission(AdminPermission|string $permission): void
    {
        $permissionValue = $permission instanceof AdminPermission ? $permission->value : $permission;
        $permissions = $this->permissions ?? [];
        
        $permissions = array_filter($permissions, fn($p) => $p !== $permissionValue);
        $this->update(['permissions' => array_values($permissions)]);
    }

    /**
     * Mettre à jour la dernière connexion
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    /**
     * Scope pour les utilisateurs actifs
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope pour un département spécifique
     */
    public function scopeByDepartment($query, string $department)
    {
        return $query->where('department', $department);
    }

    /**
     * Scope pour un niveau d'accès spécifique
     */
    public function scopeByAccessLevel($query, string $accessLevel)
    {
        return $query->where('access_level', $accessLevel);
    }

    /**
     * Obtenir le nom complet de l'utilisateur
     */
    public function getFullNameAttribute(): string
    {
        return $this->user?->name ?? 'Utilisateur inconnu';
    }

    /**
     * Obtenir l'email de l'utilisateur
     */
    public function getEmailAttribute(): string
    {
        return $this->user?->email ?? '';
    }

    /**
     * Obtenir le nom du département formaté
     */
    public function getDepartmentLabelAttribute(): string
    {
        return match($this->department) {
            'management' => 'Direction',
            'finance' => 'Finance',
            'support' => 'Support',
            'marketing' => 'Marketing',
            'tech' => 'Technique',
            'operations' => 'Opérations',
            'legal' => 'Juridique',
            'hr' => 'Ressources Humaines',
            default => ucfirst($this->department ?? 'Non défini'),
        };
    }

    /**
     * Obtenir le label du niveau d'accès
     */
    public function getAccessLevelLabelAttribute(): string
    {
        return match($this->access_level) {
            'read' => 'Lecture seule',
            'write' => 'Lecture/Écriture',
            'full' => 'Accès complet',
            'super_admin' => 'Super Administrateur',
            default => ucfirst($this->access_level ?? 'Non défini'),
        };
    }
}
