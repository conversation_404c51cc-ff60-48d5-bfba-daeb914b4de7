<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class MarchandAbonnement extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'marchand_abonnements';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'marchand_id',
        'type_abonnement',
        'statut',
        'date_debut',
        'date_fin',
        'date_prochaine_facturation',
        'date_expiration_grace',
        'prix_mensuel',
        'commission_taux_min',
        'commission_taux_max',
        'reduction_logistique',
        'limite_produits',
        'limite_commandes_mois',
        'limite_campagnes_mois',
        'acces_analytics_avancees',
        'acces_support_prioritaire',
        'acces_gestionnaire_dedie',
        'acces_ia_predictive',
        'acces_evenements_exclusifs',
        'type_abonnement_precedent',
        'date_changement_abonnement',
        'raison_changement',
        'mode_facturation',
        'facturation_automatique',
        'methode_paiement_abonnement',
        'est_periode_essai',
        'fin_periode_essai',
        'code_promotion',
        'reduction_promotion',
        'fonctionnalites_activees',
        'limites_personnalisees',
        'notes_admin',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'date_debut' => 'datetime',
        'date_fin' => 'datetime',
        'date_prochaine_facturation' => 'datetime',
        'date_expiration_grace' => 'datetime',
        'date_changement_abonnement' => 'datetime',
        'fin_periode_essai' => 'datetime',
        'prix_mensuel' => 'decimal:2',
        'commission_taux_min' => 'decimal:2',
        'commission_taux_max' => 'decimal:2',
        'reduction_logistique' => 'decimal:2',
        'reduction_promotion' => 'decimal:2',
        'limite_produits' => 'integer',
        'limite_commandes_mois' => 'integer',
        'limite_campagnes_mois' => 'integer',
        'facturation_automatique' => 'boolean',
        'acces_analytics_avancees' => 'boolean',
        'acces_support_prioritaire' => 'boolean',
        'acces_gestionnaire_dedie' => 'boolean',
        'acces_ia_predictive' => 'boolean',
        'acces_evenements_exclusifs' => 'boolean',
        'est_periode_essai' => 'boolean',
        'fonctionnalites_activees' => 'array',
        'limites_personnalisees' => 'array',
    ];

    /**
     * Relations
     */

    /**
     * Marchand propriétaire de l'abonnement
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Historique des changements d'abonnement
     */
    public function historique(): HasMany
    {
        return $this->hasMany(MarchandAbonnementHistorique::class, 'abonnement_id');
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Vérifie si l'abonnement est actif
     */
    public function estActif(): bool
    {
        return $this->statut === 'actif' &&
               ($this->date_fin === null || $this->date_fin->isFuture());
    }

    /**
     * Vérifie si l'abonnement va expirer bientôt
     */
    public function vaExpirer(int $jours = 7): bool
    {
        if (!$this->date_fin) {
            return false;
        }

        return $this->date_fin->diffInDays(now()) <= $jours;
    }

    /**
     * Vérifie si c'est un abonnement trial actif
     */
    public function isTrialActive(): bool
    {
        return $this->type_abonnement === 'trial' &&
               $this->est_periode_essai &&
               $this->fin_periode_essai > now() &&
               $this->statut === 'actif';
    }

    /**
     * Obtient le nombre de jours restants pour le trial
     */
    public function getTrialDaysRemaining(): int
    {
        if (!$this->isTrialActive()) {
            return 0;
        }

        return max(0, now()->diffInDays($this->fin_periode_essai, false));
    }

    /**
     * Vérifie si le trial expire bientôt (dans les 3 derniers jours)
     */
    public function isTrialExpiringSoon(): bool
    {
        return $this->isTrialActive() && $this->getTrialDaysRemaining() <= 3;
    }

    /**
     * Calcule le taux de commission pour un montant donné
     */
    public function calculerCommission(float $montant): float
    {
        $taux = $this->commission_taux_min / 100;
        return $montant * $taux;
    }

    /**
     * Vérifie si une limite est atteinte
     */
    public function limiteAtteinte(string $typeLimite, int $valeurActuelle): bool
    {
        $limite = $this->{'limite_' . $typeLimite};
        
        if ($limite === null) {
            return false; // Pas de limite
        }

        return $valeurActuelle >= $limite;
    }

    /**
     * Obtient la configuration par défaut pour un type d'abonnement
     */
    public static function getConfigurationDefaut(string $typeAbonnement): array
    {
        $configurations = [
            'gratuit' => [
                'prix_mensuel' => 0.00,
                'commission_taux_min' => 5.00,
                'commission_taux_max' => 10.00,
                'reduction_logistique' => 0.00,
                'limite_produits' => 50,
                'limite_commandes_mois' => 100,
                'limite_campagnes_mois' => 0,
                'acces_analytics_avancees' => false,
                'acces_support_prioritaire' => false,
                'acces_gestionnaire_dedie' => false,
                'acces_ia_predictive' => false,
                'acces_evenements_exclusifs' => false,
            ],
            'basique' => [
                'prix_mensuel' => 32797.85,
                'commission_taux_min' => 4.00,
                'commission_taux_max' => 8.00,
                'reduction_logistique' => 5.00,
                'limite_produits' => null,
                'limite_commandes_mois' => null,
                'limite_campagnes_mois' => 0,
                'acces_analytics_avancees' => false,
                'acces_support_prioritaire' => true,
                'acces_gestionnaire_dedie' => false,
                'acces_ia_predictive' => false,
                'acces_evenements_exclusifs' => false,
            ],
            'premium' => [
                'prix_mensuel' => 65595.70,
                'commission_taux_min' => 3.00,
                'commission_taux_max' => 6.00,
                'reduction_logistique' => 10.00,
                'limite_produits' => null,
                'limite_commandes_mois' => null,
                'limite_campagnes_mois' => 1,
                'acces_analytics_avancees' => true,
                'acces_support_prioritaire' => true,
                'acces_gestionnaire_dedie' => true,
                'acces_ia_predictive' => false,
                'acces_evenements_exclusifs' => false,
            ],
            'elite' => [
                'prix_mensuel' => 131191.40,
                'commission_taux_min' => 2.00,
                'commission_taux_max' => 4.00,
                'reduction_logistique' => 15.00,
                'limite_produits' => null,
                'limite_commandes_mois' => null,
                'limite_campagnes_mois' => 3,
                'acces_analytics_avancees' => true,
                'acces_support_prioritaire' => true,
                'acces_gestionnaire_dedie' => true,
                'acces_ia_predictive' => true,
                'acces_evenements_exclusifs' => true,
            ],
        ];

        return $configurations[$typeAbonnement] ?? $configurations['gratuit'];
    }

    /**
     * Crée un nouvel abonnement pour un marchand
     */
    public static function creerAbonnement(int $marchandId, string $typeAbonnement, array $options = []): self
    {
        $abonnement = new self();
        $abonnement->marchand_id = $marchandId;
        $abonnement->type_abonnement = $typeAbonnement;
        $abonnement->statut = 'actif';

        // Définir les valeurs par défaut selon le type d'abonnement
        switch ($typeAbonnement) {
            case 'gratuit':
                $abonnement->prix_mensuel = 0;
                $abonnement->commission_taux_min = 10;
                $abonnement->commission_taux_max = 15;
                $abonnement->limite_produits = 10;
                break;
            case 'trial':
                $abonnement->prix_mensuel = 0;
                $abonnement->commission_taux_min = 4;
                $abonnement->commission_taux_max = 8;
                $abonnement->reduction_logistique = 5.00;
                $abonnement->limite_produits = null;
                $abonnement->acces_support_prioritaire = true;
                $abonnement->est_periode_essai = true;
                $abonnement->fin_periode_essai = now()->addDays(14);
                break;
            case 'basique':
                $abonnement->prix_mensuel = 9900;
                $abonnement->commission_taux_min = 8;
                $abonnement->commission_taux_max = 12;
                $abonnement->limite_produits = 50;
                break;
            case 'premium':
                $abonnement->prix_mensuel = 19900;
                $abonnement->commission_taux_min = 6;
                $abonnement->commission_taux_max = 10;
                $abonnement->limite_produits = 200;
                $abonnement->acces_analytics_avancees = true;
                $abonnement->acces_support_prioritaire = true;
                break;
            case 'elite':
                $abonnement->prix_mensuel = 49900;
                $abonnement->commission_taux_min = 5;
                $abonnement->commission_taux_max = 8;
                $abonnement->limite_produits = null;
                $abonnement->acces_analytics_avancees = true;
                $abonnement->acces_support_prioritaire = true;
                $abonnement->acces_gestionnaire_dedie = true;
                $abonnement->acces_ia_predictive = true;
                $abonnement->acces_evenements_exclusifs = true;
                break;
        }

        // Appliquer les options fournies
        foreach ($options as $key => $value) {
            if (in_array($key, $abonnement->fillable)) {
                $abonnement->$key = $value;
            }
        }

        // Définir les dates si non fournies
        if (!isset($options['date_debut'])) {
            $abonnement->date_debut = now();
        }
        if (!isset($options['date_fin'])) {
            $abonnement->date_fin = $typeAbonnement === 'gratuit' ? null : Carbon::parse($abonnement->date_debut)->addMonth();
        }
        if (!isset($options['date_prochaine_facturation']) && $typeAbonnement !== 'gratuit') {
            $abonnement->date_prochaine_facturation = Carbon::parse($abonnement->date_debut)->addMonth();
        }

        $abonnement->save();

        return $abonnement;
    }
}
