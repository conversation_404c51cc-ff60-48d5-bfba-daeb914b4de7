<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CommandePrincipale extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'commandes_principales';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'client_id',
        'numero_commande',
        'montant_total_ht',
        'montant_total_ttc',
        'montant_commission_plateforme',
        'montant_taxes',
        'statut_global',
        'statut_paiement',
        'adresse_livraison_id',
        'adresse_facturation_id',
        'methode_paiement',
        'date_commande',
        'date_livraison_souhaitee',
        'date_paiement',
        'instructions_speciales',
        'notes_internes',
        'devise',
        'nombre_articles_total',
        'nombre_marchands',
        'nombre_sous_commandes',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'montant_total_ht' => 'decimal:2',
        'montant_total_ttc' => 'decimal:2',
        'montant_commission_plateforme' => 'decimal:2',
        'montant_taxes' => 'decimal:2',
        'date_commande' => 'datetime',
        'date_livraison_souhaitee' => 'date',
        'date_paiement' => 'datetime',
        'date_confirmation' => 'datetime',
        'date_premiere_expedition' => 'datetime',
        'date_derniere_expedition' => 'datetime',
        'date_livraison_complete' => 'datetime',
        'nombre_articles_total' => 'integer',
        'nombre_marchands' => 'integer',
        'nombre_sous_commandes' => 'integer',
        'metadata' => 'array',
    ];

    /**
     * Relations
     */

    /**
     * Client qui a passé la commande
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Relation avec les sous-commandes vendeur
     */
    public function sousCommandes(): HasMany
    {
        return $this->hasMany(SousCommandeVendeur::class, 'commande_principale_id');
    }

    /**
     * Conversations liées à cette commande
     */
    public function conversations(): HasMany
    {
        return $this->hasMany(ClientMarchandConversation::class);
    }

    /**
     * Méthodes utilitaires pour la compatibilité
     */

    /**
     * Calcule le pourcentage de progression de la commande
     */
    public function getPourcentageProgression(): int
    {
        $statuts = [
            'EnAttente' => 0,
            'PayementConfirme' => 20,
            'EnTraitement' => 40,
            'PartielExpedié' => 60,
            'TotalementExpedié' => 80,
            'TotalementLivré' => 100,
            'Terminé' => 100,
        ];

        return $statuts[$this->statut_global] ?? 0;
    }
}
