<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BoutiqueReview extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     * Note: Cette table existe dans admin_marchand_lorrelei
     */
    protected $table = 'boutique_reviews';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'marchand_id',
        'user_id',
        'name',
        'email',
        'rating',
        'title',
        'comment',
        'images',
        'likes',
        'dislikes',
        'ip_address',
        'is_approved',
        'is_verified',
        'commande_id',
        'marchand_response',
        'marchand_response_at',
        'is_reported',
        'report_reason',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'rating' => 'integer',
        'images' => 'array',
        'likes' => 'integer',
        'dislikes' => 'integer',
        'is_approved' => 'boolean',
        'is_verified' => 'boolean',
        'is_reported' => 'boolean',
        'marchand_response_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['image_urls', 'thumbnail_urls', 'formatted_date'];

    /**
     * Get the marchand that owns the review.
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Get the user that owns the review.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the commande associated with the review (for verification).
     */
    public function commande(): BelongsTo
    {
        return $this->belongsTo(Commande::class);
    }

    /**
     * Get the votes for the review.
     */
    public function votes(): HasMany
    {
        return $this->hasMany(BoutiqueReviewVote::class);
    }

    /**
     * Get the formatted date for humans (e.g., "il y a 2 jours").
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get the image URLs for the review.
     * Les images sont stockées dans admin_marchand_lorrelei
     */
    public function getImageUrlsAttribute(): array
    {
        if (!$this->images || empty($this->images)) {
            return [];
        }

        $adminBaseUrl = config('app.admin_base_url', 'http://127.0.0.1:8001');
        $urls = [];

        foreach ($this->images as $image) {
            if (isset($image['name']) && isset($image['folder'])) {
                $urls[] = rtrim($adminBaseUrl, '/') . "/images/boutique-reviews/{$image['folder']}/{$image['name']}";
            }
        }

        return $urls;
    }

    /**
     * Get the thumbnail URLs for the review images.
     */
    public function getThumbnailUrlsAttribute(): array
    {
        if (!$this->images || empty($this->images)) {
            return [];
        }

        $adminBaseUrl = config('app.admin_base_url', 'http://127.0.0.1:8001');
        $thumbnails = [];

        foreach ($this->images as $image) {
            if (isset($image['name']) && isset($image['folder'])) {
                $filename = pathinfo($image['name'], PATHINFO_FILENAME);
                $extension = pathinfo($image['name'], PATHINFO_EXTENSION);
                
                $thumbnails[] = [
                    'small' => rtrim($adminBaseUrl, '/') . "/images/boutique-reviews/{$image['folder']}/thumbnails/small_{$filename}.{$extension}",
                    'medium' => rtrim($adminBaseUrl, '/') . "/images/boutique-reviews/{$image['folder']}/thumbnails/medium_{$filename}.{$extension}",
                    'large' => rtrim($adminBaseUrl, '/') . "/images/boutique-reviews/{$image['folder']}/thumbnails/large_{$filename}.{$extension}",
                ];
            }
        }

        return $thumbnails;
    }

    /**
     * Scope pour les avis approuvés
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope pour les avis vérifiés (avec achat)
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope pour filtrer par note
     */
    public function scopeWithRating($query, int $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope pour les avis avec images
     */
    public function scopeWithImages($query)
    {
        return $query->whereNotNull('images')->where('images', '!=', '[]');
    }

    /**
     * Scope pour les avis récents
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Vérifie si l'utilisateur peut modifier cet avis
     */
    public function canBeEditedBy(?User $user): bool
    {
        if (!$user) {
            return false;
        }

        // L'utilisateur peut modifier son propre avis dans les 24h
        return $this->user_id === $user->id && 
               $this->created_at->diffInHours(now()) <= 24;
    }

    /**
     * Vérifie si l'avis peut être signalé
     */
    public function canBeReported(): bool
    {
        return !$this->is_reported && $this->is_approved;
    }

    /**
     * Marque l'avis comme signalé
     */
    public function markAsReported(string $reason): void
    {
        $this->update([
            'is_reported' => true,
            'report_reason' => $reason,
        ]);
    }

    /**
     * Ajoute une réponse du marchand
     */
    public function addMarchandResponse(string $response): void
    {
        $this->update([
            'marchand_response' => $response,
            'marchand_response_at' => now(),
        ]);
    }
}
