<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Commission extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'commissions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'commande_principale_id',
        'sous_commande_id',
        'marchand_id',
        'reference_commission',
        'type_commission',
        'montant_base',
        'taux_commission',
        'montant_fixe',
        'montant_commission',
        'devise',
        'regle_appliquee',
        'details_calcul',
        'categorie_id',
        'produit_id',
        'statut',
        'periode_facturation',
        'mois_facturation',
        'annee_facturation',
        'date_calcul',
        'date_confirmation',
        'date_collecte',
        'date_ajustement',
        'calcule_par',
        'confirme_par',
        'montant_ajustement',
        'motif_ajustement',
        'ajuste_par',
        'versement_id',
        'incluse_dans_versement',
        'numero_facture',
        'facture_generee',
        'date_facture',
        'metadata',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'montant_base' => 'decimal:2',
        'taux_commission' => 'decimal:2',
        'montant_fixe' => 'decimal:2',
        'montant_commission' => 'decimal:2',
        'montant_ajustement' => 'decimal:2',
        'date_calcul' => 'datetime',
        'date_confirmation' => 'datetime',
        'date_collecte' => 'datetime',
        'date_ajustement' => 'datetime',
        'date_facture' => 'datetime',
        'periode_facturation' => 'date',
        'mois_facturation' => 'integer',
        'annee_facturation' => 'integer',
        'incluse_dans_versement' => 'boolean',
        'facture_generee' => 'boolean',
        'details_calcul' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Relation avec le marchand
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Relation avec la catégorie
     */
    public function categorie(): BelongsTo
    {
        return $this->belongsTo(Categorie::class);
    }

    /**
     * Relation avec le versement
     */
    public function versement(): BelongsTo
    {
        return $this->belongsTo(Versement::class);
    }

    /**
     * Relation avec l'utilisateur qui a calculé la commission
     */
    public function calculateur(): BelongsTo
    {
        return $this->belongsTo(User::class, 'calcule_par');
    }

    /**
     * Relation avec l'utilisateur qui a confirmé la commission
     */
    public function confirmateur(): BelongsTo
    {
        return $this->belongsTo(User::class, 'confirme_par');
    }

    /**
     * Relation avec l'utilisateur qui a ajusté la commission
     */
    public function ajusteur(): BelongsTo
    {
        return $this->belongsTo(User::class, 'ajuste_par');
    }

    /**
     * Scopes pour les requêtes courantes
     */
    public function scopePourMarchand($query, $marchandId)
    {
        return $query->where('marchand_id', $marchandId);
    }

    public function scopePourPeriode($query, $mois, $annee)
    {
        return $query->where('mois_facturation', $mois)
                    ->where('annee_facturation', $annee);
    }

    public function scopeCalculee($query)
    {
        return $query->where('statut', 'calculee');
    }

    public function scopeConfirmee($query)
    {
        return $query->where('statut', 'confirmee');
    }

    public function scopeNonIncluse($query)
    {
        return $query->where('incluse_dans_versement', false);
    }

    public function scopeParType($query, $type)
    {
        return $query->where('type_commission', $type);
    }

    /**
     * Méthodes utilitaires
     */
    
    /**
     * Vérifie si la commission peut être confirmée
     */
    public function peutÊtreConfirmée(): bool
    {
        return $this->statut === 'calculee';
    }

    /**
     * Vérifie si la commission peut être ajustée
     */
    public function peutÊtreAjustée(): bool
    {
        return in_array($this->statut, ['calculee', 'confirmee']);
    }

    /**
     * Génère une référence unique pour la commission
     */
    public static function genererReferenceCommission(int $marchandId, string $type = 'vente'): string
    {
        $prefix = match($type) {
            'vente' => 'COM',
            'abonnement' => 'ABO',
            'listing' => 'LST',
            'transaction' => 'TXN',
            'service' => 'SRV',
            'penalite' => 'PEN',
            'bonus' => 'BON',
            'ajustement' => 'ADJ',
            default => 'COM'
        };
        
        $date = now()->format('Ymd');
        $marchId = str_pad($marchandId, 4, '0', STR_PAD_LEFT);
        $random = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
        
        $reference = $prefix . $date . $marchId . $random;
        
        // Vérifier l'unicité
        while (self::where('reference_commission', $reference)->exists()) {
            $random = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
            $reference = $prefix . $date . $marchId . $random;
        }
        
        return $reference;
    }

    /**
     * Calcule la commission basée sur les règles
     */
    public function calculerCommission(array $regles = []): void
    {
        $commission = 0;
        
        // Calcul basé sur le taux
        if ($this->taux_commission > 0) {
            $commission += ($this->montant_base * $this->taux_commission) / 100;
        }
        
        // Ajouter le montant fixe
        $commission += $this->montant_fixe;
        
        // Appliquer des règles spécifiques selon le type
        $commission = $this->appliquerReglesSpecifiques($commission, $regles);
        
        $this->montant_commission = round($commission, 2);
        $this->details_calcul = [
            'montant_base' => $this->montant_base,
            'taux_applique' => $this->taux_commission,
            'montant_taux' => ($this->montant_base * $this->taux_commission) / 100,
            'montant_fixe' => $this->montant_fixe,
            'montant_final' => $this->montant_commission,
            'regles_appliquees' => $regles,
            'date_calcul' => now()->toISOString()
        ];
        
        $this->save();
    }

    /**
     * Applique des règles spécifiques selon le type de commission
     */
    private function appliquerReglesSpecifiques(float $commission, array $regles): float
    {
        return match($this->type_commission) {
            'vente' => $this->appliquerReglesVente($commission, $regles),
            'abonnement' => $this->appliquerReglesAbonnement($commission, $regles),
            'listing' => $this->appliquerReglesListing($commission, $regles),
            default => $commission
        };
    }

    /**
     * Applique les règles spécifiques aux commissions de vente
     */
    private function appliquerReglesVente(float $commission, array $regles): float
    {
        // Exemple : réduction pour gros volumes
        if (isset($regles['reduction_volume']) && $this->montant_base > $regles['seuil_volume']) {
            $commission *= (1 - $regles['reduction_volume']);
        }
        
        return $commission;
    }

    /**
     * Applique les règles spécifiques aux commissions d'abonnement
     */
    private function appliquerReglesAbonnement(float $commission, array $regles): float
    {
        // Logique spécifique aux abonnements
        return $commission;
    }

    /**
     * Applique les règles spécifiques aux frais de listing
     */
    private function appliquerReglesListing(float $commission, array $regles): float
    {
        // Logique spécifique aux listings
        return $commission;
    }

    /**
     * Confirme la commission
     */
    public function confirmer(?int $confirmateurId = null): void
    {
        $this->statut = 'confirmee';
        $this->date_confirmation = now();
        $this->confirme_par = $confirmateurId;

        $this->save();
    }

    /**
     * Ajuste la commission
     */
    public function ajuster(float $montantAjustement, string $motif, ?int $ajusteurId = null): void
    {
        $this->montant_ajustement = $montantAjustement;
        $this->montant_commission += $montantAjustement;
        $this->motif_ajustement = $motif;
        $this->statut = 'ajustee';
        $this->date_ajustement = now();
        $this->ajuste_par = $ajusteurId;

        $this->save();
    }

    /**
     * Marque la commission comme collectée
     */
    public function marquerCollectee(?int $versementId = null): void
    {
        $this->statut = 'collectee';
        $this->date_collecte = now();
        
        if ($versementId) {
            $this->versement_id = $versementId;
            $this->incluse_dans_versement = true;
        }

        $this->save();
    }

    /**
     * Calcule le total des commissions pour un marchand sur une période
     */
    public static function getTotalPourMarchand(int $marchandId, $dateDebut, $dateFin): float
    {
        return self::where('marchand_id', $marchandId)
                  ->where('statut', 'collectee')
                  ->whereBetween('date_collecte', [$dateDebut, $dateFin])
                  ->sum('montant_commission');
    }
}
