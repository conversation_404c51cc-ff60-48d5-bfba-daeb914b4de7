<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coupon extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'code',
        'type',
        'valeur',
        'date_debut',
        'date_fin',
        'utilisation_max',
        'utilisation_compteur',
        'est_actif',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'valeur' => 'decimal:2',
            'date_debut' => 'timestamp',
            'date_fin' => 'timestamp',
            'est_actif' => 'boolean',
        ];
    }
}
