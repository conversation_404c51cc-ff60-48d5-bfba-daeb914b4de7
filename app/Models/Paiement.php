<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Paiement extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'commande_id',
        'marchand_id',
        'montant',
        'methode',
        'statut',
        'reference_transaction',
        'date_paiement',
        'details_paiement',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'montant' => 'decimal:2',
        'date_paiement' => 'datetime',
        'details_paiement' => 'array',
    ];

    /**
     * Relations
     */

    /**
     * Commande associée au paiement
     */
    public function commande(): BelongsTo
    {
        return $this->belongsTo(Commande::class);
    }

    /**
     * Marchand qui reçoit le paiement
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Obtient le montant formaté
     */
    public function getMontantFormate(): string
    {
        return number_format($this->montant, 2) . ' FCFA';
    }

    /**
     * Vérifie si le paiement est réussi
     */
    public function estReussi(): bool
    {
        return $this->statut === 'reussi';
    }

    /**
     * Vérifie si le paiement est en attente
     */
    public function estEnAttente(): bool
    {
        return $this->statut === 'en_attente';
    }

    /**
     * Vérifie si le paiement a échoué
     */
    public function aEchoue(): bool
    {
        return $this->statut === 'echec';
    }
}
