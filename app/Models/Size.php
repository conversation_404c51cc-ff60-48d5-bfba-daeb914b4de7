<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Size extends Model
{
    use HasFactory;

    /**
     * Les attributs qui sont mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name_fr',
        'name_en',
        'code',
        'category',
        'measurements',
        'equivalents',
        'description',
        'is_active',
        'order',
        'foot_length_cm',
    ];

    /**
     * Les attributs qui doivent être castés.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'measurements' => 'array',
        'equivalents' => 'array',
        'order' => 'integer',
        'foot_length_cm' => 'float',
    ];

    /**
     * Obtient le nom de la taille en fonction de la langue actuelle.
     *
     * @return string
     */
    public function getLocalizedNameAttribute(): string
    {
        $locale = app()->getLocale();
        $name = $locale === 'fr' ? $this->name_fr : $this->name_en;

        // Fallback si le nom dans la langue actuelle est null
        if (empty($name)) {
            $name = $this->name_fr ?: $this->name_en ?: 'Sans nom';
        }

        return $name;
    }

    /**
     * Obtient le nom complet de la taille (code + nom).
     *
     * @return string
     */
    public function getFullNameAttribute(): string
    {
        $code = $this->code ?: 'N/A';
        $localizedName = $this->getLocalizedNameAttribute();

        return "{$code} - {$localizedName}";
    }

    /**
     * Relation avec les produits.
     *
     * @return BelongsToMany
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Produit::class, 'produit_size', 'size_id', 'produit_id')
            ->withTimestamps();
    }

    /**
     * Alias de la relation products() pour maintenir la cohérence avec les autres modèles.
     *
     * @return BelongsToMany
     */
    public function produits(): BelongsToMany
    {
        return $this->products();
    }

    /**
     * Récupère toutes les catégories de tailles distinctes avec leurs labels traduits.
     *
     * @return array
     */
    public static function getDistinctCategories(): array
    {
        $categories = self::select('category')
            ->distinct()
            ->whereNotNull('category')
            ->where('is_active', true)
            ->pluck('category')
            ->toArray();

        $categoryLabels = [];
        foreach ($categories as $category) {
            $label = match ($category) {
                'clothing' => 'Vêtements (Clothing)',
                'shoes' => 'Chaussures (Shoes)',
                'accessories' => 'Accessoires (Accessories)',
                'other' => 'Autre (Other)',
                default => ucfirst($category),
            };
            $categoryLabels[$category] = $label;
        }

        // Ajouter 'other' s'il n'existe pas déjà
        if (!in_array('other', $categories)) {
            $categoryLabels['other'] = 'Autre (Other)';
        }

        return $categoryLabels;
    }
}
