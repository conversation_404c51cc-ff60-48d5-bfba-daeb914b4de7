<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MerchantValidationDocument extends Model
{
    protected $fillable = [
        'validation_id',
        'document_type',
        'file_path',
        'original_name',
        'mime_type',
        'file_size',
        'status',
        'rejection_reason',
        'verified_by',
        'verified_at',
    ];

    protected $casts = [
        'verified_at' => 'datetime',
    ];

    // Constantes pour les types de documents
    const TYPE_KBIS = 'KBIS';
    const TYPE_PIECE_IDENTITE = 'PIECE_IDENTITE';
    const TYPE_PHOTO_AVEC_PIECE = 'PHOTO_AVEC_PIECE';
    const TYPE_STATUTS_ENTREPRISE = 'STATUTS_ENTREPRISE';
    const TYPE_RIB = 'RIB_BANCAIRE';
    const TYPE_ATTESTATION_FISCALE = 'ATTESTATION_FISCALE';
    const TYPE_REGISTRE_COMMERCE = 'REGISTRE_COMMERCE';
    const TYPE_AUTRES = 'AUTRES';

    // Constantes pour les statuts
    const STATUS_EN_ATTENTE = 'EN_ATTENTE';
    const STATUS_VALIDE = 'VALIDE';
    const STATUS_REJETE = 'REJETE';

    // Relations
    public function validation(): BelongsTo
    {
        return $this->belongsTo(MerchantValidation::class, 'validation_id');
    }

    public function verifier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    // Méthodes utilitaires
    public function isVerified(): bool
    {
        return $this->status === self::STATUS_VALIDE;
    }

    public function isRejected(): bool
    {
        return $this->status === self::STATUS_REJETE;
    }

    public function isPending(): bool
    {
        return $this->status === self::STATUS_EN_ATTENTE;
    }

    public function getReadableType(): string
    {
        return match($this->document_type) {
            self::TYPE_KBIS => 'Extrait Kbis',
            self::TYPE_PIECE_IDENTITE => 'Pièce d\'identité',
            self::TYPE_PHOTO_AVEC_PIECE => 'Photo avec pièce d\'identité',
            self::TYPE_STATUTS_ENTREPRISE => 'Statuts d\'entreprise',
            self::TYPE_RIB => 'RIB bancaire',
            self::TYPE_ATTESTATION_FISCALE => 'Attestation fiscale',
            self::TYPE_REGISTRE_COMMERCE => 'Registre commerce',
            self::TYPE_AUTRES => 'Autre document',
            default => $this->document_type,
        };
    }

    public function getReadableFileSize(): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $this->file_size;
        $unit = 0;

        while ($size >= 1024 && $unit < count($units) - 1) {
            $size /= 1024;
            $unit++;
        }

        return round($size, 2) . ' ' . $units[$unit];
    }
} 