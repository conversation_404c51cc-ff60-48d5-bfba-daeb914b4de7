<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BoutiqueReviewVote extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     * Note: Cette table existe dans admin_marchand_lorrelei
     */
    protected $table = 'boutique_review_votes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'boutique_review_id',
        'user_id',
        'ip_address',
        'vote_type',
    ];

    /**
     * Get the boutique review that owns the vote.
     */
    public function boutiqueReview(): BelongsTo
    {
        return $this->belongsTo(BoutiqueReview::class);
    }

    /**
     * Get the user that owns the vote.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
