<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class ConversationMessage extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'conversation_id',
        'auteur_type',
        'auteur_client_id',
        'auteur_marchand_id',
        'auteur_nom',
        'message',
        'type_message',
        'pieces_jointes',
        'lu_par_client',
        'lu_par_marchand',
        'date_lecture_client',
        'date_lecture_marchand',
        'reponse_a_message_id',
        'important',
        'notification_envoyee',
        'archive',
        'metadata',
        'modere',
        'raison_moderation',
        'date_moderation',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'pieces_jointes' => 'array',
        'lu_par_client' => 'boolean',
        'lu_par_marchand' => 'boolean',
        'date_lecture_client' => 'datetime',
        'date_lecture_marchand' => 'datetime',
        'important' => 'boolean',
        'notification_envoyee' => 'boolean',
        'archive' => 'boolean',
        'metadata' => 'array',
        'modere' => 'boolean',
        'date_moderation' => 'datetime',
    ];

    /**
     * Relations
     */

    /**
     * Conversation à laquelle appartient le message
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(ClientMarchandConversation::class, 'conversation_id');
    }

    /**
     * Client auteur du message
     */
    public function auteurClient(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'auteur_client_id');
    }

    /**
     * Marchand auteur du message
     */
    public function auteurMarchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class, 'auteur_marchand_id');
    }

    /**
     * Scopes
     */
    public function scopeNonLus($query)
    {
        return $query->where('lu', false);
    }

    public function scopeParMarchand($query)
    {
        return $query->where('auteur_type', 'marchand');
    }

    public function scopeParClient($query)
    {
        return $query->where('auteur_type', 'client');
    }

    /**
     * Méthodes utilitaires
     */
    public function marquerCommeLu(string $typeUtilisateur = 'marchand'): void
    {
        $champ = match($typeUtilisateur) {
            'client' => 'lu_par_client',
            'marchand' => 'lu_par_marchand',
            default => 'lu_par_marchand'
        };

        $this->update([$champ => true]);
    }

    public function estDeMarchand(): bool
    {
        return $this->auteur_type === 'marchand';
    }

    public function estDeClient(): bool
    {
        return $this->auteur_type === 'client';
    }

    public function aDesFichiersJoints(): bool
    {
        return !empty($this->fichiers_joints);
    }
}
