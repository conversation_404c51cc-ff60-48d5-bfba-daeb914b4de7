<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProduitZoneLivraison extends Model
{
    use HasFactory;

    /**
     * La table associée au modèle.
     *
     * @var string
     */
    protected $table = 'produit_zones_livraison';

    /**
     * Les attributs qui sont assignables en masse.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'produit_id',
        'marchand_zone_livraison_id',
        'frais_livraison_specifique',
        'actif',
    ];

    /**
     * Les attributs à caster.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'frais_livraison_specifique' => 'decimal:2',
        'actif' => 'boolean',
    ];

    /**
     * Les attributs accesseurs à ajouter au tableau.
     *
     * @var array
     */
    protected $appends = [
        'frais_livraison',
        'delai_livraison',
    ];

    /**
     * Obtenir le produit associé.
     */
    public function produit(): BelongsTo
    {
        return $this->belongsTo(Produit::class, 'produit_id');
    }

    /**
     * Obtenir la zone de livraison du marchand associée.
     */
    public function marchandZoneLivraison(): BelongsTo
    {
        return $this->belongsTo(MarchandZoneLivraison::class, 'marchand_zone_livraison_id');
    }

    /**
     * Obtenir les frais de livraison effectifs (spécifiques ou standard).
     *
     * @return float
     */
    public function getFraisLivraisonAttribute(): float
    {
        if ($this->frais_livraison_specifique !== null) {
            return (float) $this->frais_livraison_specifique;
        }

        // Charger la relation si elle n'est pas déjà chargée
        if (!$this->relationLoaded('marchandZoneLivraison')) {
            $this->load('marchandZoneLivraison');
        }

        // Vérifier si la relation existe
        if (!$this->marchandZoneLivraison) {
            return 0;
        }

        return (float) $this->marchandZoneLivraison->frais_livraison;
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::retrieved(function ($produitZoneLivraison) {
            // Précharger automatiquement les relations nécessaires
            if (!$produitZoneLivraison->relationLoaded('marchandZoneLivraison')) {
                $produitZoneLivraison->load('marchandZoneLivraison');
            }
        });
    }

    /**
     * Obtenir le délai de livraison formaté.
     *
     * @return string
     */
    public function getDelaiLivraisonAttribute(): string
    {
        // Charger la relation si elle n'est pas déjà chargée
        if (!$this->relationLoaded('marchandZoneLivraison')) {
            $this->load('marchandZoneLivraison');
        }

        // Vérifier si la relation existe
        if (!$this->marchandZoneLivraison) {
            return 'Non défini';
        }

        $min = $this->marchandZoneLivraison->delai_livraison_min;
        $max = $this->marchandZoneLivraison->delai_livraison_max;

        if (!isset($min) || !isset($max)) {
            return 'Non défini';
        }

        if ($min === $max) {
            return "{$min} jours";
        }

        return "{$min} à {$max} jours";
    }
}
