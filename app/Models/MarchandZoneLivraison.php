<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MarchandZoneLivraison extends Model
{
    use HasFactory;

    /**
     * La table associée au modèle.
     *
     * @var string
     */
    protected $table = 'marchand_zones_livraison';

    /**
     * Les attributs qui sont assignables en masse.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'marchand_id',
        'zone_livraison_id',
        'frais_livraison',
        'delai_livraison_min',
        'delai_livraison_max',
        'actif',
    ];

    /**
     * Les attributs à caster.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'frais_livraison' => 'decimal:2',
        'delai_livraison_min' => 'integer',
        'delai_livraison_max' => 'integer',
        'actif' => 'boolean',
    ];


    /**
     * Les attributs accesseurs à ajouter au tableau.
     *
     * @var array
     */
    protected $appends = [
        'delai_livraison',
    ];

    /**
     * Obtenir le marchand associé.
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(User::class, 'marchand_id');
    }

    /**
     * Obtenir la zone de livraison associée.
     */
    public function zoneLivraison(): BelongsTo
    {
        return $this->belongsTo(ZoneLivraison::class, 'zone_livraison_id');
    }
    public function getDelaiLivraisonAttribute(): string
    {


        $min = $this->delai_livraison_min;
        $max = $this->delai_livraison_max;

        if (!isset($min) || !isset($max)) {
            return 'Non défini';
        }

        if ($min === $max) {
            return "{$min} jours";
        }

        return "{$min} à {$max} jours";
    }
    /**
     * Obtenir les produits associés à cette zone de livraison du marchand.
     */
    public function produits()
    {
        return $this->belongsToMany(Produit::class, 'produit_zones_livraison', 'marchand_zone_livraison_id', 'produit_id')
            ->withPivot(['frais_livraison_specifique', 'actif'])
            ->withTimestamps();
    }

    /**
     * Obtenir les associations produit-zone de livraison.
     */
    public function produitZonesLivraison(): HasMany
    {
        return $this->hasMany(ProduitZoneLivraison::class, 'marchand_zone_livraison_id');
    }


}
