<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Versement extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'versements';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'marchand_id',
        'reference_versement',
        'montant_brut',
        'frais_transaction',
        'montant_net',
        'devise',
        'statut',
        'methode_versement',
        'reference_transaction_externe',
        'prestataire_paiement',
        'date_demande',
        'date_traitement',
        'date_completion',
        'date_echec',
        'details_bancaires_cryptes',
        'nom_titulaire_compte',
        'numero_compte_masque',
        'message_erreur',
        'notes_admin',
        'traité_par',
        'periode_debut',
        'periode_fin',
        'type_versement',
        'frequence',
        'nombre_commandes',
        'montant_commissions_deduites',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'montant_brut' => 'decimal:2',
        'frais_transaction' => 'decimal:2',
        'montant_net' => 'decimal:2',
        'montant_commissions_deduites' => 'decimal:2',
        'date_demande' => 'datetime',
        'date_traitement' => 'datetime',
        'date_completion' => 'datetime',
        'date_echec' => 'datetime',
        'periode_debut' => 'date',
        'periode_fin' => 'date',
        'nombre_commandes' => 'integer',
        'metadata' => 'array',
    ];

    /**
     * Relation avec le marchand
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Relation avec l'utilisateur qui a traité le versement
     */
    public function traiteur(): BelongsTo
    {
        return $this->belongsTo(User::class, 'traité_par');
    }

    /**
     * Relation avec les commissions incluses dans ce versement
     */
    public function commissions(): HasMany
    {
        return $this->hasMany(Commission::class);
    }

    /**
     * Scopes pour les requêtes courantes
     */
    public function scopePourMarchand($query, $marchandId)
    {
        return $query->where('marchand_id', $marchandId);
    }

    public function scopeEnAttente($query)
    {
        return $query->where('statut', 'EnAttente');
    }

    public function scopeComplete($query)
    {
        return $query->where('statut', 'Complété');
    }

    public function scopeEchec($query)
    {
        return $query->where('statut', 'Échoué');
    }

    public function scopeAutomatique($query)
    {
        return $query->where('type_versement', 'automatique');
    }

    public function scopePourPeriode($query, $debut, $fin)
    {
        return $query->whereBetween('periode_debut', [$debut, $fin])
                    ->orWhereBetween('periode_fin', [$debut, $fin]);
    }

    /**
     * Méthodes utilitaires
     */
    
    /**
     * Vérifie si le versement peut être traité
     */
    public function peutÊtreTraité(): bool
    {
        return $this->statut === 'EnAttente';
    }

    /**
     * Vérifie si le versement peut être annulé
     */
    public function peutÊtreAnnulé(): bool
    {
        return in_array($this->statut, ['EnAttente', 'EnCours']);
    }

    /**
     * Génère une référence unique pour le versement
     */
    public static function genererReferenceVersement(int $marchandId): string
    {
        $prefix = 'VER';
        $date = now()->format('Ymd');
        $marchId = str_pad($marchandId, 4, '0', STR_PAD_LEFT);
        $random = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
        
        $reference = $prefix . $date . $marchId . $random;
        
        // Vérifier l'unicité
        while (self::where('reference_versement', $reference)->exists()) {
            $random = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
            $reference = $prefix . $date . $marchId . $random;
        }
        
        return $reference;
    }

    /**
     * Démarre le traitement du versement
     */
    public function demarrerTraitement(?int $traiteurId = null): void
    {
        $this->statut = 'EnCours';
        $this->date_traitement = now();
        $this->traité_par = $traiteurId;

        $this->save();

        // Déclencher le processus de versement
        // event(new VersementDemarre($this));
    }

    /**
     * Marque le versement comme complété
     */
    public function marquerComplete(string $referenceExterne, ?string $prestataire = null): void
    {
        $this->statut = 'Complété';
        $this->date_completion = now();
        $this->reference_transaction_externe = $referenceExterne;
        $this->prestataire_paiement = $prestataire;

        $this->save();

        // Notifier le marchand
        // event(new VersementComplete($this));
    }

    /**
     * Marque le versement comme échoué
     */
    public function marquerEchec(string $messageErreur): void
    {
        $this->statut = 'Échoué';
        $this->date_echec = now();
        $this->message_erreur = $messageErreur;

        $this->save();

        // Notifier les administrateurs
        // event(new VersementEchec($this));
    }

    /**
     * Calcule les frais de transaction
     */
    public function calculerFraisTransaction(): void
    {
        $frais = match($this->methode_versement) {
            'virement_bancaire' => $this->montant_brut * 0.01, // 1%
            'orange_money' => min($this->montant_brut * 0.02, 5000), // 2% max 5000 FCFA
            'mtn_money' => min($this->montant_brut * 0.02, 5000), // 2% max 5000 FCFA
            'paypal' => $this->montant_brut * 0.029 + 350, // 2.9% + 350 FCFA
            'stripe' => $this->montant_brut * 0.029 + 350, // 2.9% + 350 FCFA
            'wave' => $this->montant_brut * 0.01, // 1%
            default => 0
        };

        $this->frais_transaction = round($frais, 2);
        $this->montant_net = $this->montant_brut - $this->frais_transaction;
        
        $this->save();
    }

    /**
     * Ajoute des notes administratives
     */
    public function ajouterNotes(string $notes, ?int $adminId = null): void
    {
        $timestamp = now()->format('Y-m-d H:i:s');
        $adminInfo = $adminId ? " (Admin ID: $adminId)" : "";
        $nouvelleNote = "$timestamp$adminInfo: $notes";
        
        $this->notes_admin = ($this->notes_admin ?? '') . "\n" . $nouvelleNote;
        $this->save();
    }

    /**
     * Récupère le montant total des versements pour un marchand sur une période
     */
    public static function getMontantTotalPourMarchand(int $marchandId, $dateDebut, $dateFin): float
    {
        return self::where('marchand_id', $marchandId)
                  ->where('statut', 'Complété')
                  ->whereBetween('date_completion', [$dateDebut, $dateFin])
                  ->sum('montant_net');
    }
}
