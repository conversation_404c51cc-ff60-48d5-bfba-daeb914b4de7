<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class SouscriptionPlan extends Model
{
    use HasFactory, SoftDeletes, HasTranslations;

    /**
     * La table associée au modèle.
     *
     * @var string
     */
    protected $table = 'souscription_plans';

    /**
     * Les attributs qui sont traduisibles.
     *
     * @var array
     */
    public $translatable = ['nom', 'features'];

    /**
     * Les attributs qui sont assignables en masse.
     *
     * @var array
     */
    protected $fillable = [
        'nom',
        'type',
        'status',
        'current_price',
        'discount_price',
        'discount_percentage',
        'is_annual',
        'commission_range',
        'features',
        'popular',
        'periode',
    ];

    /**
     * Les attributs qui doivent être castés.
     *
     * @var array
     */
    protected $casts = [
        'current_price' => 'decimal:2',
        'discount_price' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'is_annual' => 'boolean',
        'popular' => 'boolean',
        'features' => 'array',
    ];

    /**
     * Les types de plans disponibles.
     *
     * @var array
     */
    public const TYPES = [
        'free' => 'Gratuit',
        'trial' => 'Essai (14 jours)',
        'basic' => 'Basique',
        'premium' => 'Premium',
        'elite' => 'Elite',
    ];

    /**
     * Les statuts disponibles.
     *
     * @var array
     */
    public const STATUSES = [
        'active' => 'Actif',
        'inactive' => 'Inactif',
    ];

    /**
     * Les périodes disponibles.
     *
     * @var array
     */
    public const PERIODES = [
        'mois' => 'Mensuel',
        'annee' => 'Annuel',
    ];

    /**
     * Obtient le prix final après réduction.
     *
     * @return float
     */
    public function getPrixFinalAttribute(): float
    {
        if ($this->discount_price) {
            return (float) $this->discount_price;
        }

        return (float) $this->current_price;
    }

    /**
     * Vérifie si le plan a une réduction active.
     *
     * @return bool
     */
    public function hasDiscount(): bool
    {
        return $this->discount_price !== null && $this->discount_price < $this->current_price;
    }

    /**
     * Calcule le montant économisé.
     *
     * @return float
     */
    public function getMontantEconomiseAttribute(): float
    {
        if ($this->hasDiscount()) {
            return (float) ($this->current_price - $this->discount_price);
        }

        return 0.0;
    }

    /**
     * Obtient le nom traduit du plan.
     *
     * @return string
     */
    public function getTranslatedName(): string
    {
        $locale = app()->getLocale();

        try {
            return $this->getTranslation('nom', $locale);
        } catch (\Exception) {
            return $this->nom;
        }
    }

    /**
     * Obtient les fonctionnalités traduites.
     *
     * @return array
     */
    public function getTranslatedFeatures(): array
    {
        $locale = app()->getLocale();

        try {
            return $this->getTranslation('features', $locale);
        } catch (\Exception) {
            return $this->features;
        }
    }
} 