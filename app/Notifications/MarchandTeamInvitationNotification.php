<?php

namespace App\Notifications;

use App\Models\MarchandUser;
use App\Models\Marchand;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class MarchandTeamInvitationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private MarchandUser $marchandUser,
        private Marchand $marchand,
        private string $activationUrl,
        private ?User $inviter = null
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $roleName = $this->marchandUser->role?->name ?? 'Membre de l\'équipe';
        $inviterName = $this->inviter?->name ?? 'L\'équipe de ' . $this->marchand->nomEntreprise;
        $storeName = $this->marchand->nomEntreprise;

        return (new MailMessage)
            ->subject('Invitation à rejoindre l\'équipe de ' . $storeName)
            ->greeting('Bonjour ' . $notifiable->name . ' !')
            ->line($inviterName . ' vous invite à rejoindre l\'équipe de **' . $storeName . '** sur la plateforme Lorelei.')
            ->line('**Votre rôle :** ' . $roleName)
            ->line('**Niveau d\'accès :** ' . $this->getAccessLevelLabel())
            ->line('**Boutique :** ' . $storeName)
            ->line('En tant que membre de cette équipe, vous aurez accès au dashboard marchand avec les permissions suivantes :')
            ->line($this->getPermissionsSummary())
            ->line('Pour accepter cette invitation et configurer votre accès, cliquez sur le bouton ci-dessous :')
            ->action('Rejoindre l\'équipe', $this->activationUrl)
            ->line('Ce lien d\'invitation expire dans 7 jours.')
            ->line('Si vous n\'avez pas demandé cette invitation, vous pouvez ignorer cet email.')
            ->line('Bienvenue dans l\'équipe !')
            ->salutation('L\'équipe Lorelei');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'marchand_team_invitation',
            'marchand_user_id' => $this->marchandUser->id,
            'marchand_id' => $this->marchand->id,
            'marchand_name' => $this->marchand->nomEntreprise,
            'role_name' => $this->marchandUser->role?->name,
            'access_level' => $this->marchandUser->access_level,
            'inviter_name' => $this->inviter?->name,
            'activation_url' => $this->activationUrl,
        ];
    }

    private function getAccessLevelLabel(): string
    {
        return match($this->marchandUser->access_level) {
            'owner' => 'Propriétaire',
            'manager' => 'Gestionnaire',
            'employee' => 'Employé',
            'read' => 'Lecture seule',
            'write' => 'Lecture/Écriture',
            default => $this->marchandUser->access_level,
        };
    }

    private function getPermissionsSummary(): string
    {
        $role = $this->marchandUser->role;
        if (!$role || empty($role->permissions)) {
            return '• Accès de base au dashboard';
        }

        $permissions = collect($role->permissions)->take(5)->map(function ($permission) {
            return '• ' . $this->getPermissionLabel($permission);
        })->join("\n");

        if (count($role->permissions) > 5) {
            $permissions .= "\n• Et " . (count($role->permissions) - 5) . " autres permissions...";
        }

        return $permissions;
    }

    private function getPermissionLabel(string $permission): string
    {
        return match($permission) {
            'view_products' => 'Voir les produits',
            'manage_products' => 'Gérer les produits',
            'view_orders' => 'Voir les commandes',
            'manage_orders' => 'Gérer les commandes',
            'view_finances' => 'Voir les finances',
            'manage_team' => 'Gérer l\'équipe',
            'view_analytics' => 'Voir les statistiques',
            'manage_settings' => 'Gérer les paramètres',
            default => ucfirst(str_replace('_', ' ', $permission)),
        };
    }
}
