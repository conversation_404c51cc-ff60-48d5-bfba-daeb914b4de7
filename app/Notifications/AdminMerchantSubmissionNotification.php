<?php

namespace App\Notifications;

use App\Models\MerchantValidation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdminMerchantSubmissionNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public MerchantValidation $validation;

    /**
     * Create a new notification instance.
     */
    public function __construct(MerchantValidation $validation)
    {
        $this->validation = $validation;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $businessInfo = $this->validation->business_info ?? [];
        $userName = $this->validation->user->name ?? 'Utilisateur inconnu';
        $userEmail = $this->validation->user->email ?? 'Email inconnu';
        $businessName = $businessInfo['nomEntreprise'] ?? 'Entreprise non spécifiée';

        return (new MailMessage)
            ->subject('🔔 Nouvelle soumission marchand à valider - Lorrelei')
            ->view('emails.admin.merchant-submission', [
                'admin_name' => $notifiable->name,
                'user_name' => $userName,
                'user_email' => $userEmail,
                'business_name' => $businessName,
                'business_type' => $businessInfo['type_business'] ?? 'Non spécifié',
                'submitted_at' => $this->validation->submitted_at?->toISOString(),
                'action_url' => route('filament.admin.resources.merchant-validations.view', $this->validation->id),
                'validation' => $this->validation,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $businessInfo = $this->validation->business_info ?? [];
        
        return [
            'type' => 'merchant_submission',
            'title' => 'Nouvelle soumission marchand',
            'message' => 'Une nouvelle soumission de ' . ($this->validation->user->name ?? 'marchand') . ' nécessite votre validation.',
            'validation_id' => $this->validation->id,
            'user_name' => $this->validation->user->name ?? 'Utilisateur inconnu',
            'user_email' => $this->validation->user->email ?? 'Email inconnu',
            'business_name' => $businessInfo['nomEntreprise'] ?? 'Entreprise non spécifiée',
            'submitted_at' => $this->validation->submitted_at?->toISOString(),
            'action_url' => route('filament.admin.resources.merchant-validations.view', $this->validation->id),
        ];
    }
}
