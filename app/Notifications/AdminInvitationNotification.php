<?php

namespace App\Notifications;

use App\Models\AdminUser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdminInvitationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private AdminUser $adminUser,
        private string $activationUrl
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $roleName = $this->adminUser->role?->name ?? 'Administrateur';
        $inviterName = $this->adminUser->inviter?->name ?? 'L\'équipe Lorelei';

        return (new MailMessage)
            ->subject('Invitation à rejoindre l\'équipe admin Lorelei')
            ->greeting('Bonjour ' . $notifiable->name . ' !')
            ->line($inviterName . ' vous invite à rejoindre l\'équipe d\'administration de Lorelei.')
            ->line('**Votre rôle :** ' . $roleName)
            ->line('**Département :** ' . $this->getDepartmentLabel())
            ->line('**Niveau d\'accès :** ' . $this->getAccessLevelLabel())
            ->line('Pour accepter cette invitation et configurer votre mot de passe, cliquez sur le bouton ci-dessous :')
            ->action('Accepter l\'invitation', $this->activationUrl)
            ->line('Ce lien d\'activation expire dans 24 heures.')
            ->line('Si vous n\'avez pas demandé cette invitation, vous pouvez ignorer cet email.')
            ->line('Merci de rejoindre notre équipe !')
            ->salutation('L\'équipe Lorelei');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'admin_invitation',
            'admin_user_id' => $this->adminUser->id,
            'role_name' => $this->adminUser->role?->name,
            'department' => $this->adminUser->department,
            'access_level' => $this->adminUser->access_level,
            'activation_url' => $this->activationUrl,
        ];
    }

    private function getDepartmentLabel(): string
    {
        return match($this->adminUser->department) {
            'management' => 'Direction',
            'finance' => 'Finance',
            'support' => 'Support Client',
            'marketing' => 'Marketing',
            'tech' => 'Technique',
            'operations' => 'Opérations',
            'legal' => 'Juridique',
            'hr' => 'Ressources Humaines',
            default => $this->adminUser->department ?? 'Non défini',
        };
    }

    private function getAccessLevelLabel(): string
    {
        return match($this->adminUser->access_level) {
            'read' => 'Lecture seule',
            'write' => 'Lecture/Écriture',
            'full' => 'Accès complet',
            'super_admin' => 'Super Administrateur',
            default => $this->adminUser->access_level,
        };
    }
}
