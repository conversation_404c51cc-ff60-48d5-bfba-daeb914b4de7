<?php

namespace App\Notifications;

use App\Models\MerchantValidation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class MerchantSubmissionReceivedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public MerchantValidation $validation;

    /**
     * Create a new notification instance.
     */
    public function __construct(MerchantValidation $validation)
    {
        $this->validation = $validation;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $businessInfo = $this->validation->business_info ?? [];
        $businessName = $businessInfo['nomEntreprise'] ?? 'votre boutique';

        return (new MailMessage)
            ->subject('✅ Soumission reçue - Votre demande est en cours de traitement - Lorrelei')
            ->view('emails.merchant.submission-received', [
                'user_name' => $notifiable->name,
                'business_name' => $businessName,
                'validation' => $this->validation,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $businessInfo = $this->validation->business_info ?? [];
        
        return [
            'type' => 'merchant_submission_received',
            'title' => 'Soumission reçue',
            'message' => 'Votre demande d\'inscription marchand a été reçue et est en cours de traitement.',
            'validation_id' => $this->validation->id,
            'business_name' => $businessInfo['nomEntreprise'] ?? 'Votre boutique',
            'submitted_at' => $this->validation->submitted_at?->toISOString(),
            'estimated_processing_time' => '2-3 jours ouvrables',
        ];
    }
}
