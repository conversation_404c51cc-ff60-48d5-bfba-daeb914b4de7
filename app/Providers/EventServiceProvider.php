<?php

namespace App\Providers;

use App\Events\MerchantSubmissionReceived;
use App\Events\MerchantApproved;
use App\Events\MerchantRejected;
use App\Events\MerchantValidationStarted;
use App\Listeners\SendAdminNotification;
use App\Listeners\SendMerchantConfirmation;
use App\Listeners\GrantMerchantAccess;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        // Événements de validation des marchands
        MerchantSubmissionReceived::class => [
            SendAdminNotification::class,
            \App\Listeners\SendMerchantSubmissionConfirmation::class,
        ],

        MerchantApproved::class => [
            SendMerchantConfirmation::class . '@handleApproved',
            GrantMerchantAccess::class,
        ],

        MerchantRejected::class => [
            SendMerchantConfirmation::class . '@handleRejected',
        ],

        MerchantValidationStarted::class => [
            // Ajouter des listeners si nécessaire
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
