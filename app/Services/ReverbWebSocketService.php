<?php

namespace App\Services;

use App\Events\MessageSent;
use App\Events\DisputeMessageSent;
use App\Events\UserTyping;
use App\Models\ConversationMessage;
use App\Models\DisputeMessage;
use App\Models\ClientMarchandConversation;
use App\Models\Dispute;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ReverbWebSocketService
{
    /**
     * Diffuser un nouveau message de conversation
     */
    public function broadcastMessage(ConversationMessage $message): void
    {
        try {
            broadcast(new MessageSent($message))->toOthers();

            Log::info('Message broadcasted via Reverb', [
                'message_id' => $message->id,
                'conversation_id' => $message->conversation_id,
                'auteur_type' => $message->auteur_type,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to broadcast message via Reverb', [
                'message_id' => $message->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Diffuser un nouveau message de litige
     */
    public function broadcastDisputeMessage(DisputeMessage $message): void
    {
        try {
            broadcast(new DisputeMessageSent($message))->toOthers();

            Log::info('Dispute message broadcasted via Reverb', [
                'message_id' => $message->id,
                'dispute_id' => $message->dispute_id,
                'auteur_type' => $message->auteur_type,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to broadcast dispute message via Reverb', [
                'message_id' => $message->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Diffuser l'état "en train d'écrire"
     */
    public function broadcastTyping(
        string $channelType,
        string $channelId,
        bool $isTyping = true
    ): void {
        $user = Auth::user();

        if (!$user) {
            return;
        }

        $userType = $this->getUserType($user);

        try {
            broadcast(new UserTyping(
                $channelType,
                $channelId,
                $user->id,
                $user->name ?? 'Utilisateur',
                $userType,
                $isTyping
            ))->toOthers();
        } catch (\Exception $e) {
            Log::error('Failed to broadcast typing status via Reverb', [
                'channel_type' => $channelType,
                'channel_id' => $channelId,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Marquer les messages comme lus et diffuser
     */
    public function markConversationAsRead(string $conversationId): void
    {
        $user = Auth::user();
        $userType = $this->getUserType($user);

        try {
            $conversation = ClientMarchandConversation::find($conversationId);

            if (!$conversation) {
                return;
            }

            // Marquer comme lu selon le type d'utilisateur
            if ($userType === 'client') {
                $conversation->update(['messages_non_lus_client' => 0]);
            } elseif ($userType === 'marchand') {
                $conversation->update(['messages_non_lus_marchand' => 0]);
            }

            Log::info('Conversation marked as read', [
                'conversation_id' => $conversationId,
                'user_type' => $userType,
                'user_id' => $user->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to mark conversation as read', [
                'conversation_id' => $conversationId,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Marquer les messages de litige comme lus
     */
    public function markDisputeAsRead(string $disputeId): void
    {
        $user = Auth::user();
        $userType = $this->getUserType($user);

        try {
            $dispute = Dispute::find($disputeId);

            if (!$dispute) {
                return;
            }

            // Marquer les messages comme lus selon le type d'utilisateur
            $dispute->messages()
                ->where('lu_par_' . $userType, false)
                ->update(['lu_par_' . $userType => true]);

            Log::info('Dispute marked as read', [
                'dispute_id' => $disputeId,
                'user_type' => $userType,
                'user_id' => $user->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to mark dispute as read', [
                'dispute_id' => $disputeId,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Déterminer le type d'utilisateur
     */
    private function getUserType($user): string
    {
        if ($user->is_admin) {
            return 'admin';
        } elseif ($user->marchand) {
            return 'marchand';
        } elseif ($user->client) {
            return 'client';
        }

        return 'unknown';
    }

    /**
     * Vérifier la connexion Reverb
     */
    public function checkConnection(): bool
    {
        try {
            // Test simple de diffusion
            broadcast(new \App\Events\ConnectionTest())->toOthers();
            return true;
        } catch (\Exception $e) {
            Log::error('Reverb connection test failed', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}
