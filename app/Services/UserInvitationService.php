<?php

namespace App\Services;

use App\Models\User;
use App\Models\AdminUser;
use App\Models\MarchandUser;
use App\Models\Marchand;
use App\Notifications\AdminInvitationNotification;
use App\Notifications\MarchandTeamInvitationNotification;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use Carbon\Carbon;

class UserInvitationService
{
    /**
     * Envoyer une invitation à un utilisateur admin
     */
    public function sendAdminInvitation(User $user, AdminUser $adminUser): void
    {
        // Générer un token de réinitialisation de mot de passe
        $token = Str::random(60);
        $user->update([
            'password_reset_token' => $token,
            'password_reset_expires_at' => Carbon::now('UTC')->addHours(24),
        ]);

        // Générer l'URL d'activation
        $activationUrl = URL::temporarySignedRoute(
            'admin.invitation.show',
            Carbon::now('UTC')->addHours(24),
            [
                'user' => $user->id,
                'token' => $token,
            ]
        );

        // Envoyer la notification
        $user->notify(new AdminInvitationNotification($adminUser, $activationUrl));
    }

    /**
     * Envoyer une invitation à un membre d'équipe marchand
     */
    public function sendMarchandTeamInvitation(
        User $user, 
        MarchandUser $marchandUser, 
        Marchand $marchand,
        ?User $inviter = null
    ): void {
        // Générer un token d'invitation
        $token = Str::random(60);
        $marchandUser->update([
            'invitation_token' => $token,
            'invitation_expires_at' => Carbon::now('UTC')->addDays(7),
        ]);

        // Générer l'URL d'activation
        $activationUrl = URL::temporarySignedRoute(
            'marchand.invitation.show',
            Carbon::now('UTC')->addDays(7),
            [
                'user' => $user->id,
                'marchand' => $marchand->id,
                'token' => $token,
            ]
        );

        // Envoyer la notification
        $user->notify(new MarchandTeamInvitationNotification(
            $marchandUser, 
            $marchand, 
            $activationUrl,
            $inviter
        ));
    }

    /**
     * Accepter une invitation admin
     */
    public function acceptAdminInvitation(User $user, string $token): bool
    {
        // Vérifier le token
        if ($user->password_reset_token !== $token ||
            $user->password_reset_expires_at < Carbon::now('UTC')) {
            return false;
        }

        // Marquer l'email comme vérifié et nettoyer les tokens
        $user->update([
            'email_verified_at' => Carbon::now('UTC'),
            'password_reset_token' => null,
            'password_reset_expires_at' => null,
        ]);

        // Activer l'AdminUser si nécessaire
        $adminUser = $user->adminUser;
        if ($adminUser && !$adminUser->is_active) {
            $adminUser->update(['is_active' => true]);
        }

        return true;
    }

    /**
     * Accepter une invitation d'équipe marchand
     */
    public function acceptMarchandInvitation(
        User $user, 
        Marchand $marchand, 
        string $token
    ): bool {
        // Trouver l'invitation
        $marchandUser = $user->marchandUsers()
            ->where('marchand_id', $marchand->id)
            ->where('invitation_token', $token)
            ->where('invitation_expires_at', '>', Carbon::now('UTC'))
            ->first();

        if (!$marchandUser) {
            return false;
        }

        // Marquer l'email comme vérifié et nettoyer les tokens
        $user->update([
            'email_verified_at' => Carbon::now('UTC'),
        ]);

        // Activer l'utilisateur marchand et nettoyer l'invitation
        $marchandUser->update([
            'is_active' => true,
            'invitation_token' => null,
            'invitation_expires_at' => null,
        ]);

        return true;
    }

    /**
     * Renvoyer une invitation admin
     */
    public function resendAdminInvitation(AdminUser $adminUser): void
    {
        $user = $adminUser->user;
        
        // Générer un nouveau token
        $token = Str::random(60);
        $user->update([
            'password_reset_token' => $token,
            'password_reset_expires_at' => Carbon::now('UTC')->addHours(24),
        ]);

        // Générer l'URL d'activation
        $activationUrl = URL::temporarySignedRoute(
            'admin.invitation.show',
            Carbon::now('UTC')->addHours(24),
            [
                'user' => $user->id,
                'token' => $token,
            ]
        );

        // Envoyer la notification
        $user->notify(new AdminInvitationNotification($adminUser, $activationUrl));
    }

    /**
     * Renvoyer une invitation d'équipe marchand
     */
    public function resendMarchandInvitation(MarchandUser $marchandUser): void
    {
        $user = $marchandUser->user;
        $marchand = $marchandUser->marchand;
        
        // Générer un nouveau token
        $token = Str::random(60);
        $marchandUser->update([
            'invitation_token' => $token,
            'invitation_expires_at' => Carbon::now('UTC')->addDays(7),
        ]);

        // Générer l'URL d'activation
        $activationUrl = URL::temporarySignedRoute(
            'marchand.invitation.show',
            Carbon::now('UTC')->addDays(7),
            [
                'user' => $user->id,
                'marchand' => $marchand->id,
                'token' => $token,
            ]
        );

        // Envoyer la notification
        $user->notify(new MarchandTeamInvitationNotification(
            $marchandUser, 
            $marchand, 
            $activationUrl,
            auth()->user()
        ));
    }

    /**
     * Annuler une invitation
     */
    public function cancelInvitation(User $user, string $type = 'admin'): bool
    {
        if ($type === 'admin') {
            $user->update([
                'password_reset_token' => null,
                'password_reset_expires_at' => null,
            ]);
            
            // Désactiver l'AdminUser
            $adminUser = $user->adminUser;
            if ($adminUser) {
                $adminUser->update(['is_active' => false]);
            }
        } else {
            // Annuler toutes les invitations marchand en attente
            $user->marchandUsers()
                ->whereNotNull('invitation_token')
                ->update([
                    'invitation_token' => null,
                    'invitation_expires_at' => null,
                    'is_active' => false,
                ]);
        }

        return true;
    }

    /**
     * Vérifier si une invitation est expirée
     */
    public function isInvitationExpired(User $user, string $type = 'admin'): bool
    {
        if ($type === 'admin') {
            return $user->password_reset_expires_at && 
                   $user->password_reset_expires_at < Carbon::now();
        }

        // Pour les invitations marchand, vérifier s'il y a des invitations expirées
        return $user->marchandUsers()
            ->whereNotNull('invitation_token')
            ->where('invitation_expires_at', '<', Carbon::now())
            ->exists();
    }

    /**
     * Nettoyer les invitations expirées
     */
    public function cleanupExpiredInvitations(): int
    {
        $cleaned = 0;

        // Nettoyer les invitations admin expirées
        $expiredAdminUsers = User::whereNotNull('password_reset_token')
            ->where('password_reset_expires_at', '<', Carbon::now())
            ->get();

        foreach ($expiredAdminUsers as $user) {
            $user->update([
                'password_reset_token' => null,
                'password_reset_expires_at' => null,
            ]);
            $cleaned++;
        }

        // Nettoyer les invitations marchand expirées
        $expiredMarchandUsers = MarchandUser::whereNotNull('invitation_token')
            ->where('invitation_expires_at', '<', Carbon::now())
            ->get();

        foreach ($expiredMarchandUsers as $marchandUser) {
            $marchandUser->update([
                'invitation_token' => null,
                'invitation_expires_at' => null,
                'is_active' => false,
            ]);
            $cleaned++;
        }

        return $cleaned;
    }
}
