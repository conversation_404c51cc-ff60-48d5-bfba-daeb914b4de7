<?php

namespace App\Services;

use App\Models\Marchand;
use App\Models\Commande;
use App\Models\Commission;
use App\Models\Versement;
use App\Models\Produit;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardStatsService
{
    /**
     * Récupère les statistiques pour le dashboard marchand
     * Intègre le nouveau système multi-marchands avec sous-commandes
     */
    public function getStatistiquesMarchand(int $marchandId): array
    {
        $maintenant = Carbon::now();
        $debutMois = $maintenant->copy()->startOfMonth();
        $finMois = $maintenant->copy()->endOfMonth();
        $debutMoisPrecedent = $debutMois->copy()->subMonth();
        $finMoisPrecedent = $debutMois->copy()->subDay();

        // Statistiques des sous-commandes (nouveau système)
        $sousCommandesMois = $this->getSousCommandesMois($marchandId, $debutMois, $finMois);
        $sousCommandesMoisPrecedent = $this->getSousCommandesMois($marchandId, $debutMoisPrecedent, $finMoisPrecedent);

        // Statistiques des commandes legacy (ancien système)
        $commandesLegacyMois = $this->getCommandesLegacyMois($marchandId, $debutMois, $finMois);
        $commandesLegacyMoisPrecedent = $this->getCommandesLegacyMois($marchandId, $debutMoisPrecedent, $finMoisPrecedent);

        // Combiner les deux systèmes
        $totalCommandesMois = $sousCommandesMois['total'] + $commandesLegacyMois['total'];
        $totalCommandesMoisPrecedent = $sousCommandesMoisPrecedent['total'] + $commandesLegacyMoisPrecedent['total'];

        $revenusMois = $sousCommandesMois['revenus'] + $commandesLegacyMois['revenus'];
        $revenusMoisPrecedent = $sousCommandesMoisPrecedent['revenus'] + $commandesLegacyMoisPrecedent['revenus'];

        $commandesEnAttente = $sousCommandesMois['en_attente'] + $commandesLegacyMois['en_attente'];
        $commandesLivrees = $sousCommandesMois['livrees'] + $commandesLegacyMois['livrees'];

        // Statistiques des produits
        $produitsStats = $this->getProduitsStats($marchandId);

        // Statistiques des versements
        $versementsStats = $this->getVersementsStats($marchandId, $debutMois, $finMois);

        // Statistiques des commissions
        $commissionsStats = $this->getCommissionsStats($marchandId, $debutMois, $finMois);

        return [
            'commandesMois' => $totalCommandesMois,
            'revenusMois' => $revenusMois,
            'commandesEnAttente' => $commandesEnAttente,
            'commandesLivrees' => $commandesLivrees,
            'produitsActifs' => $produitsStats['actifs'],
            'produitsEnRupture' => $produitsStats['en_rupture'],
            'versementsEnAttente' => $versementsStats['en_attente'],
            'versementsCompletes' => $versementsStats['completes'],
            'montantVersementsEnAttente' => $versementsStats['montant_en_attente'],
            'commissionsCollectees' => $commissionsStats['collectees'],
            'montantCommissions' => $commissionsStats['montant'],
            'evolutionCommandes' => $this->calculerEvolution($totalCommandesMois, $totalCommandesMoisPrecedent),
            'evolutionRevenus' => $this->calculerEvolution($revenusMois, $revenusMoisPrecedent),
            'breakdown' => [
                'nouveau_systeme' => [
                    'commandes' => $sousCommandesMois['total'],
                    'revenus' => $sousCommandesMois['revenus']
                ],
                'ancien_systeme' => [
                    'commandes' => $commandesLegacyMois['total'],
                    'revenus' => $commandesLegacyMois['revenus']
                ]
            ]
        ];
    }

    /**
     * Récupère les statistiques globales pour le dashboard admin
     */
    public function getStatistiquesAdmin(): array
    {
        $maintenant = Carbon::now();
        $debutMois = $maintenant->copy()->startOfMonth();
        $finMois = $maintenant->copy()->endOfMonth();

        // Statistiques globales des commandes principales
        $commandesPrincipales = $this->getCommandesPrincipalesStats($debutMois, $finMois);

        // Statistiques des marchands
        $marchandsStats = $this->getMarchandsStats();

        // Statistiques des versements globaux
        $versementsGlobaux = $this->getVersementsGlobaux($debutMois, $finMois);

        // Statistiques des commissions globales
        $commissionsGlobales = $this->getCommissionsGlobales($debutMois, $finMois);

        return [
            'commandesPrincipalesMois' => $commandesPrincipales['total'],
            'revenusTotauxMois' => $commandesPrincipales['revenus'],
            'commissionsPlateformeMois' => $commissionsGlobales['montant_plateforme'],
            'marchandsActifs' => $marchandsStats['actifs'],
            'marchandsEnAttente' => $marchandsStats['en_attente'],
            'versementsEnCours' => $versementsGlobaux['en_cours'],
            'montantVersementsEnCours' => $versementsGlobaux['montant_en_cours'],
            'topMarchands' => $this->getTopMarchands($debutMois, $finMois),
        ];
    }

    /**
     * Récupère les statistiques des sous-commandes pour un marchand
     */
    private function getSousCommandesMois(int $marchandId, Carbon $debut, Carbon $fin): array
    {
        // Utiliser la base de données lorrelei pour les sous-commandes
        $sousCommandes = DB::connection('lorrelei')
            ->table('sous_commandes_vendeur')
            ->where('marchand_id', $marchandId)
            ->whereBetween('created_at', [$debut, $fin]);

        $total = $sousCommandes->count();
        $revenus = $sousCommandes->sum('montant_versement_marchand');
        $enAttente = $sousCommandes->whereIn('statut', ['EnAttente', 'EnTraitement'])->count();
        $livrees = $sousCommandes->where('statut', 'Livré')->count();

        return [
            'total' => $total,
            'revenus' => $revenus,
            'en_attente' => $enAttente,
            'livrees' => $livrees
        ];
    }

    /**
     * Récupère les statistiques des commandes legacy pour un marchand
     */
    private function getCommandesLegacyMois(int $marchandId, Carbon $debut, Carbon $fin): array
    {
        $commandes = Commande::where('marchand_id', $marchandId)
            ->where('type_commande', 'legacy')
            ->whereBetween('created_at', [$debut, $fin]);

        $total = $commandes->count();
        $revenus = $commandes->sum('montantTotal');
        $enAttente = $commandes->whereIn('statut', ['EnAttente', 'EnCoursDeTraitement'])->count();
        $livrees = $commandes->where('statut', 'Livré')->count();

        return [
            'total' => $total,
            'revenus' => $revenus,
            'en_attente' => $enAttente,
            'livrees' => $livrees
        ];
    }

    /**
     * Récupère les statistiques des produits pour un marchand
     */
    private function getProduitsStats(int $marchandId): array
    {
        // Considérer les produits avec stock > 0 comme actifs
        $produitsActifs = Produit::where('marchand_id', $marchandId)
            ->where('stock', '>', 0)
            ->count();

        $produitsEnRupture = Produit::where('marchand_id', $marchandId)
            ->where('stock', 0)
            ->count();

        return [
            'actifs' => $produitsActifs,
            'en_rupture' => $produitsEnRupture
        ];
    }

    /**
     * Récupère les statistiques des versements pour un marchand
     */
    private function getVersementsStats(int $marchandId, Carbon $debut, Carbon $fin): array
    {
        $versementsEnAttente = Versement::where('marchand_id', $marchandId)
            ->where('statut', 'EnAttente')
            ->count();

        $versementsCompletes = Versement::where('marchand_id', $marchandId)
            ->where('statut', 'Complété')
            ->whereBetween('date_completion', [$debut, $fin])
            ->count();

        $montantEnAttente = Versement::where('marchand_id', $marchandId)
            ->where('statut', 'EnAttente')
            ->sum('montant_net');

        return [
            'en_attente' => $versementsEnAttente,
            'completes' => $versementsCompletes,
            'montant_en_attente' => $montantEnAttente
        ];
    }

    /**
     * Récupère les statistiques des commissions pour un marchand
     */
    private function getCommissionsStats(int $marchandId, Carbon $debut, Carbon $fin): array
    {
        $commissionsCollectees = Commission::where('marchand_id', $marchandId)
            ->where('statut', 'collectee')
            ->whereBetween('date_collecte', [$debut, $fin])
            ->count();

        $montantCommissions = Commission::where('marchand_id', $marchandId)
            ->where('statut', 'collectee')
            ->whereBetween('date_collecte', [$debut, $fin])
            ->sum('montant_commission');

        return [
            'collectees' => $commissionsCollectees,
            'montant' => $montantCommissions
        ];
    }

    /**
     * Calcule l'évolution en pourcentage
     */
    private function calculerEvolution(float $actuel, float $precedent): float
    {
        if ($precedent == 0) {
            return $actuel > 0 ? 100 : 0;
        }

        return round((($actuel - $precedent) / $precedent) * 100, 2);
    }

    /**
     * Récupère les statistiques des commandes principales
     */
    private function getCommandesPrincipalesStats(Carbon $debut, Carbon $fin): array
    {
        $stats = DB::connection('lorrelei')
            ->table('commandes_principales')
            ->whereBetween('created_at', [$debut, $fin])
            ->selectRaw('
                COUNT(*) as total,
                SUM(montant_total_ttc) as revenus
            ')
            ->first();

        return [
            'total' => $stats->total ?? 0,
            'revenus' => $stats->revenus ?? 0
        ];
    }

    /**
     * Récupère les statistiques des marchands
     */
    private function getMarchandsStats(): array
    {
        $actifs = Marchand::where('statut_validation', 'validé')->count();
        $enAttente = Marchand::where('statut_validation', 'en_attente')->count();

        return [
            'actifs' => $actifs,
            'en_attente' => $enAttente
        ];
    }

    /**
     * Récupère les statistiques globales des versements
     */
    private function getVersementsGlobaux(Carbon $debut, Carbon $fin): array
    {
        $enCours = Versement::whereIn('statut', ['EnAttente', 'EnCours'])->count();
        $montantEnCours = Versement::whereIn('statut', ['EnAttente', 'EnCours'])->sum('montant_net');

        return [
            'en_cours' => $enCours,
            'montant_en_cours' => $montantEnCours
        ];
    }

    /**
     * Récupère les statistiques globales des commissions
     */
    private function getCommissionsGlobales(Carbon $debut, Carbon $fin): array
    {
        $montantPlateforme = Commission::where('statut', 'collectee')
            ->whereBetween('date_collecte', [$debut, $fin])
            ->sum('montant_commission');

        return [
            'montant_plateforme' => $montantPlateforme
        ];
    }

    /**
     * Récupère le top des marchands par revenus
     */
    public function getTopMarchands(Carbon $debut, Carbon $fin, int $limit = 5): array
    {
        return DB::connection('lorrelei')
            ->table('sous_commandes_vendeur as scv')
            ->join('marchands as m', 'scv.marchand_id', '=', 'm.id')
            ->whereBetween('scv.created_at', [$debut, $fin])
            ->groupBy('scv.marchand_id', 'm.nomEntreprise')
            ->selectRaw('
                scv.marchand_id,
                m.nomEntreprise,
                COUNT(*) as nombre_commandes,
                SUM(scv.montant_versement_marchand) as revenus_total
            ')
            ->orderByDesc('revenus_total')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Obtenir les commandes récentes d'un marchand (pour widgets Filament)
     */
    public function getCommandesRecentesMarchand(int $marchandId, int $limit = 10): \Illuminate\Support\Collection
    {
        // Récupérer les sous-commandes du nouveau système
        $sousCommandes = DB::connection('lorrelei')
            ->table('sous_commandes_vendeur as scv')
            ->join('commandes_principales as cp', 'scv.commande_principale_id', '=', 'cp.id')
            ->join('clients as c', 'cp.client_id', '=', 'c.id')
            ->join('users as u', 'c.user_id', '=', 'u.id')
            ->where('scv.marchand_id', $marchandId)
            ->select([
                'scv.id',
                'scv.numero_sous_commande',
                'scv.montant_ttc as montantTotal',
                'scv.statut',
                'scv.created_at',
                'scv.date_livraison_prevue',
                'cp.numero_commande as commande_principale',
                DB::raw("CONCAT(c.prenom, ' ', c.nom) as client_nom"),
                'u.email as client_email',
                'scv.frais_livraison',
                DB::raw("'multi_marchand' as type_commande"),
                DB::raw("'sous_commande' as source_type")
            ])
            ->orderByDesc('scv.created_at')
            ->limit($limit)
            ->get();

        // Récupérer les commandes legacy
        $commandesLegacy = \App\Models\Commande::where('marchand_id', $marchandId)
            ->where('type_commande', 'legacy')
            ->with(['client.user'])
            ->select([
                'id',
                'montantTotal',
                'statut',
                'created_at',
                'dateLivraisonPrevue as date_livraison_prevue',
                'codeSuivi',
                'client_id',
                DB::raw("'legacy' as type_commande"),
                DB::raw("'commande' as source_type")
            ])
            ->orderByDesc('created_at')
            ->limit($limit)
            ->get()
            ->map(function ($commande) {
                return (object) [
                    'id' => $commande->id,
                    'numero_sous_commande' => null,
                    'montantTotal' => $commande->montantTotal,
                    'statut' => $commande->statut,
                    'created_at' => $commande->created_at,
                    'date_livraison_prevue' => $commande->date_livraison_prevue,
                    'commande_principale' => null,
                    'client_nom' => $commande->client?->user?->name ?? 'Client inconnu',
                    'client_email' => $commande->client?->user?->email ?? '',
                    'frais_livraison' => 0,
                    'type_commande' => 'legacy',
                    'source_type' => 'commande'
                ];
            });

        // Fusionner et trier par date
        $toutesCommandes = collect($sousCommandes)->merge($commandesLegacy)
            ->sortByDesc('created_at')
            ->take($limit);

        return $toutesCommandes;
    }

    public function getCommandesGlobales(Carbon $debut, Carbon $fin): array
    {
        return DB::connection('lorrelei')
        ->table('commandes_principales as cp')
        ->join('clients as c', 'cp.client_id', '=', 'c.id')
        ->join('users as u', 'c.user_id', '=', 'u.id')
        ->select([
            'cp.id',
            'cp.numero_commande',
            'cp.montant_total_ttc', // Nom correct de la colonne
            'cp.montant_total_ht',
            'cp.statut_global',
            'cp.created_at',
            'cp.date_livraison_souhaitee', // Nom correct de la colonne
            DB::raw("CONCAT(c.prenom, ' ', c.nom) as client_nom"),
            'u.email as client_email',
            'cp.nombre_marchands',
            'cp.montant_commission_plateforme' // Remplace frais_livraison_total
        ])
            ->whereBetween('cp.created_at', [$debut, $fin])
            ->orderByDesc('cp.created_at')
            ->limit(20)
            ->get()
            ->toArray();
    }
}
