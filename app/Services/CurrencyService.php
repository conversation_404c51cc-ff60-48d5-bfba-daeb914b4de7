<?php

namespace App\Services;

use App\Models\Currency;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;

class CurrencyService
{
    /**
     * Get the current currency.
     *
     * @return Currency
     */
    public static function getCurrentCurrency(): Currency
    {
        $currencyCode = Session::get('currency', null);
        
        if (!$currencyCode) {
            $currency = self::getDefaultCurrency();
            Session::put('currency', $currency->code);
            return $currency;
        }
        
        $currency = self::getCurrencyByCode($currencyCode);
        
        if (!$currency) {
            $currency = self::getDefaultCurrency();
            Session::put('currency', $currency->code);
        }
        
        return $currency;
    }
    
    /**
     * Set the current currency.
     *
     * @param string $currencyCode
     * @return Currency|null
     */
    public static function setCurrentCurrency(string $currencyCode): ?Currency
    {
        $currency = self::getCurrencyByCode($currencyCode);
        
        if ($currency) {
            Session::put('currency', $currency->code);
            return $currency;
        }
        
        return null;
    }
    
    /**
     * Get the default currency.
     *
     * @return Currency
     */
    public static function getDefaultCurrency(): Currency
    {
        return Cache::remember('default_currency', 60 * 24, function () {
            return Currency::where('is_default', true)->first() ?? Currency::where('code', 'FCFA')->first() ?? Currency::first();
        });
    }
    
    /**
     * Get a currency by its code.
     *
     * @param string $code
     * @return Currency|null
     */
    public static function getCurrencyByCode(string $code): ?Currency
    {
        return Cache::remember('currency_' . $code, 60 * 24, function () use ($code) {
            return Currency::where('code', $code)->where('is_active', true)->first();
        });
    }
    
    /**
     * Get all active currencies.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getActiveCurrencies()
    {
        return Cache::remember('active_currencies', 60 * 24, function () {
            return Currency::where('is_active', true)->get();
        });
    }
    
    /**
     * Format a price with the current currency.
     *
     * @param float $price
     * @param string|null $currencyCode
     * @return string
     */
    public static function formatPrice($price, ?string $currencyCode = null): string
    {
        $currency = $currencyCode 
            ? self::getCurrencyByCode($currencyCode) 
            : self::getCurrentCurrency();
        
        if (!$currency) {
            $currency = self::getDefaultCurrency();
        }
        
        return number_format($price, 0, ',', ' ') . ' ' . $currency->symbol;
    }
    
    /**
     * Convert a price from one currency to another.
     *
     * @param float $price
     * @param string $fromCurrencyCode
     * @param string $toCurrencyCode
     * @return float
     */
    public static function convertPrice($price, string $fromCurrencyCode, string $toCurrencyCode): float
    {
        $fromCurrency = self::getCurrencyByCode($fromCurrencyCode);
        $toCurrency = self::getCurrencyByCode($toCurrencyCode);
        
        if (!$fromCurrency || !$toCurrency) {
            return $price;
        }
        
        // Convertir d'abord en devise par défaut
        $defaultCurrency = self::getDefaultCurrency();
        $priceInDefaultCurrency = $price;
        
        if ($fromCurrency->code !== $defaultCurrency->code) {
            $priceInDefaultCurrency = $price / $fromCurrency->exchange_rate;
        }
        
        // Puis convertir de la devise par défaut à la devise cible
        if ($toCurrency->code !== $defaultCurrency->code) {
            return $priceInDefaultCurrency * $toCurrency->exchange_rate;
        }
        
        return $priceInDefaultCurrency;
    }
    
    /**
     * Clear the currency cache.
     *
     * @return void
     */
    public static function clearCache(): void
    {
        Cache::forget('default_currency');
        Cache::forget('active_currencies');
        
        foreach (Currency::all() as $currency) {
            Cache::forget('currency_' . $currency->code);
        }
    }
}
