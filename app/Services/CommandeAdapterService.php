<?php

namespace App\Services;

use App\Models\CommandePrincipale;
use App\Models\SousCommandeVendeur;
use App\Models\Commande;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * Service d'adaptation pour migrer les requêtes de l'ancien système Commande
 * vers le nouveau système CommandePrincipale + SousCommandeVendeur
 */
class CommandeAdapterService
{
    /**
     * Obtenir les commandes principales pour l'admin dashboard
     */
    public function getCommandesPrincipalesForAdmin(): Builder
    {
        return CommandePrincipale::query()
            ->with([
                'client',
                'sousCommandes.marchand'
            ])
            ->withCount('sousCommandes')
            ->latest('date_commande');
    }

    /**
     * Obtenir les sous-commandes pour un marchand spécifique
     */
    public function getSousCommandesForMarchand(int $marchandId): Builder
    {
        return SousCommandeVendeur::query()
            ->where('marchand_id', $marchandId)
            ->with([
                'commandePrincipale.client',
                'marchand',
                'articles.produit',
                'zoneLivraison'
            ])
            ->latest('date_creation');
    }

    /**
     * Statistiques des commandes pour l'admin
     */
    public function getStatistiquesAdmin(): array
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        return [
            'total_commandes' => CommandePrincipale::count(),
            'commandes_aujourd_hui' => CommandePrincipale::whereDate('date_commande', $today)->count(),
            'commandes_ce_mois' => CommandePrincipale::where('date_commande', '>=', $thisMonth)->count(),
            'commandes_mois_dernier' => CommandePrincipale::whereBetween('date_commande', [
                $lastMonth,
                $lastMonth->copy()->endOfMonth()
            ])->count(),

            'revenus_ce_mois' => CommandePrincipale::where('date_commande', '>=', $thisMonth)
                ->sum('montant_total_ttc'),
            'revenus_mois_dernier' => CommandePrincipale::whereBetween('date_commande', [
                $lastMonth,
                $lastMonth->copy()->endOfMonth()
            ])->sum('montant_total_ttc'),

            'commandes_en_attente' => CommandePrincipale::where('statut_global', 'EnAttente')->count(),
            'commandes_en_traitement' => CommandePrincipale::where('statut_global', 'EnTraitement')->count(),
            'commandes_expediees' => CommandePrincipale::whereIn('statut_global', [
                'PartielExpedié',
                'TotalementExpedié'
            ])->count(),

            'commission_plateforme_mois' => CommandePrincipale::where('date_commande', '>=', $thisMonth)
                ->sum('montant_commission_plateforme'),
        ];
    }

    /**
     * Statistiques des sous-commandes pour un marchand
     */
    public function getStatistiquesMarchand(int $marchandId): array
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        $baseQuery = SousCommandeVendeur::where('marchand_id', $marchandId);

        return [
            'total_commandes' => $baseQuery->count(),
            'commandes_aujourd_hui' => $baseQuery->whereDate('date_creation', $today)->count(),
            'commandes_ce_mois' => $baseQuery->where('date_creation', '>=', $thisMonth)->count(),
            'commandes_mois_dernier' => $baseQuery->whereBetween('date_creation', [
                $lastMonth,
                $lastMonth->copy()->endOfMonth()
            ])->count(),

            'revenus_ce_mois' => $baseQuery->where('date_creation', '>=', $thisMonth)
                ->sum('montant_versement_marchand'),
            'revenus_mois_dernier' => $baseQuery->whereBetween('date_creation', [
                $lastMonth,
                $lastMonth->copy()->endOfMonth()
            ])->sum('montant_versement_marchand'),

            'commandes_en_attente' => $baseQuery->where('statut', 'EnAttente')->count(),
            'commandes_confirmees' => $baseQuery->where('statut', 'Confirmé')->count(),
            'commandes_en_preparation' => $baseQuery->where('statut', 'EnPreparation')->count(),
            'commandes_expediees' => $baseQuery->where('statut', 'Expédié')->count(),
            'commandes_livrees' => $baseQuery->where('statut', 'Livré')->count(),

            'versements_en_attente' => $baseQuery->where('versement_effectué', false)
                ->whereIn('statut', ['Livré', 'Terminé'])
                ->sum('montant_versement_marchand'),

            'commission_totale_mois' => $baseQuery->where('date_creation', '>=', $thisMonth)
                ->sum('montant_commission'),
        ];
    }

    /**
     * Obtenir les dernières commandes pour l'admin
     */
    public function getDernieresCommandesAdmin(int $limit = 10): Collection
    {
        return CommandePrincipale::with([
                'client',
                'sousCommandes.marchand'
            ])
            ->withCount('sousCommandes')
            ->latest('date_commande')
            ->limit($limit)
            ->get()
            ->map(function ($commande) {
                return [
                    'id' => $commande->id,
                    'numero_commande' => $commande->numero_commande,
                    'client_nom' => $commande->client->nom ?? 'Client supprimé',
                    'client_email' => $commande->client->email ?? '',
                    'montant_total_ttc' => $commande->montant_total_ttc,
                    'statut_global' => $commande->statut_global,
                    'statut_paiement' => $commande->statut_paiement,
                    'date_commande' => $commande->date_commande,
                    'nombre_marchands' => $commande->nombre_marchands,
                    'nombre_sous_commandes' => $commande->sous_commandes_count,
                    'progression' => $commande->getPourcentageProgression(),
                ];
            });
    }

    /**
     * Obtenir les dernières sous-commandes pour un marchand
     */
    public function getDernieresSousCommandesMarchand(int $marchandId, int $limit = 10): Collection
    {
        return SousCommandeVendeur::where('marchand_id', $marchandId)
            ->with([
                'commandePrincipale.client',
                'articles.produit'
            ])
            ->withCount('articles')
            ->latest('date_creation')
            ->limit($limit)
            ->get();
    }

    /**
     * Mapper les statuts de l'ancien système vers le nouveau
     */
    public function mapperStatutAncienVersNouveau(string $ancienStatut): array
    {
        return match($ancienStatut) {
            'en_attente', 'pending' => [
                'statut_global' => 'EnAttente',
                'statut_sous_commande' => 'EnAttente'
            ],
            'confirme', 'confirmed' => [
                'statut_global' => 'PayementConfirme',
                'statut_sous_commande' => 'Confirmé'
            ],
            'en_preparation', 'processing' => [
                'statut_global' => 'EnTraitement',
                'statut_sous_commande' => 'EnPreparation'
            ],
            'expedie', 'shipped' => [
                'statut_global' => 'PartielExpedié',
                'statut_sous_commande' => 'Expédié'
            ],
            'livre', 'delivered' => [
                'statut_global' => 'TotalementLivré',
                'statut_sous_commande' => 'Livré'
            ],
            'annule', 'cancelled' => [
                'statut_global' => 'Annulé',
                'statut_sous_commande' => 'Annulé'
            ],
            default => [
                'statut_global' => 'EnAttente',
                'statut_sous_commande' => 'EnAttente'
            ]
        };
    }

    /**
     * Vérifier si une commande legacy existe encore
     */
    public function hasLegacyCommandes(): bool
    {
        return Commande::where('type_commande', 'legacy')
            ->orWhereNull('type_commande')
            ->exists();
    }

    /**
     * Obtenir le nombre de commandes legacy à migrer
     */
    public function countLegacyCommandes(): int
    {
        return Commande::where('type_commande', 'legacy')
            ->orWhereNull('type_commande')
            ->count();
    }
}
