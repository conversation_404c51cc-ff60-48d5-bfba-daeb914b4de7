<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\MerchantValidation;
use Illuminate\Support\Facades\Auth;

class MerchantValidationMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();
        if (!$user) {
            return redirect()->route('login');
        }

        // Vérifier si l'email est vérifié
        if (!$user->hasVerifiedEmail()) {
            return redirect()->route('verification.notice');
        }

        $validation = $user->merchantValidation;
        $currentRoute = $request->route()->getName();

        // Si pas de validation en cours et n'est pas sur welcome
        if (!$validation && $currentRoute !== 'seller.welcome') {
            return redirect()->route('seller.welcome');
        }

        // Si validation existe, rediriger selon le statut
        if ($validation) {
            switch ($validation->status) {
                case MerchantValidation::STATUS_EN_ATTENTE_SOUMISSION:
                    if (!in_array($currentRoute, ['seller.welcome', 'seller.personal-info', 'seller.personal-info.store'])) {
                        return redirect()->route('seller.personal-info');
                    }
                    break;

                case MerchantValidation::STATUS_PERSONAL_INFO_COMPLETED:
                    if (!in_array($currentRoute, ['seller.billing', 'seller.billing.store'])) {
                        return redirect()->route('seller.billing');
                    }
                    break;

                case MerchantValidation::STATUS_BILLING_INFO_COMPLETED:
                    if (!in_array($currentRoute, ['seller.store-setup', 'seller.store-setup.store'])) {
                        return redirect()->route('seller.store-setup');
                    }
                    break;

                case MerchantValidation::STATUS_STORE_INFO_COMPLETED:
                    if (!in_array($currentRoute, ['seller.documents', 'seller.upload-document', 'seller.submit-for-review'])) {
                        return redirect()->route('seller.documents');
                    }
                    break;
                case MerchantValidation::STATUS_INFORMATIONS_SOUMISES:
                    if (!in_array($currentRoute, ['seller.documents', 'seller.upload-document', 'seller.submit-for-review'])) {
                        return redirect()->route('seller.documents');
                    }
                    break;

                case MerchantValidation::STATUS_DOCUMENTS_SOUMIS:
                    if (!in_array($currentRoute, ['seller.submission-complete', 'seller.submit-for-review', 'seller.documents'])) {
                        // return redirect()->route('seller.submission-complete'); 
                        return redirect()->route('seller.documents');
                    }
                    break;
                case MerchantValidation::STATUS_EN_ATTENTE_VALIDATION:
                    if (!in_array($currentRoute, ['seller.submission-complete', 'seller.submit-for-review'])) {
                        return redirect()->route('seller.submission-complete');
                    }
                    break;

                case MerchantValidation::STATUS_REJETE:
                    if ($currentRoute !== 'seller.rejected') {
                        return redirect()->route('seller.rejected');
                    }
                    break;

                case MerchantValidation::STATUS_SUSPENDU:
                    if ($currentRoute !== 'seller.suspended') {
                        return redirect()->route('seller.suspended');
                    }
                    break;
            }
        }

        return $next($request);
    }
}
