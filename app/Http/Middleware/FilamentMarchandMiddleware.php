<?php

namespace App\Http\Middleware;

use App\Models\Marchand;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class FilamentMarchandMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Vérifier que l'utilisateur est connecté
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // // Vérifier que l'utilisateur a un profil marchand
        // $marchand = Marchand::where('user_id', $user->id)->first();

        // if (!$marchand) {
        //     // Rediriger vers le processus d'inscription marchand
        //     return redirect()->route('seller.welcome')
        //         ->with('error', 'Vous devez compléter votre inscription marchand pour accéder à cette section.');
        // }

        // // Vérifier que le marchand est validé
        // if ($marchand->statut_validation !== 'valide') {
        //     return redirect()->route('seller.welcome')
        //         ->with('warning', 'Votre compte marchand est en cours de validation. Vous recevrez un email une fois validé.');
        // }

        return $next($request);
    }
}
