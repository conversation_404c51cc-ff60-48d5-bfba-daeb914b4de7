<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Vérifier que l'utilisateur est connecté
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Vérifier que l'utilisateur est un administrateur
        // dd($user->get_current_user());
        // if (!$user->role != "Admin" && $user->email !== '<EMAIL>') {
        //     abort(403, 'Accès non autorisé. Vous devez être administrateur pour accéder à cette section.');
        // }

        return $next($request);
    }
}
