<?php

namespace App\Http\Middleware;

use App\Enums\AdminPermission;
use App\Enums\MarchandPermission;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPermissions
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $permission, string $context = 'admin'): Response
    {
        $user = auth()->user();

        if (!$user) {
            return redirect()->route('login');
        }

        $hasPermission = false;

        if ($context === 'admin') {
            // Vérifier les permissions admin
            $adminPermission = AdminPermission::tryFrom($permission);
            if ($adminPermission) {
                $hasPermission = $user->hasAdminPermission($adminPermission);
            }
        } elseif ($context === 'marchand') {
            // Vérifier les permissions marchand
            $marchandPermission = MarchandPermission::tryFrom($permission);
            if ($marchandPermission && $user->marchand) {
                $hasPermission = $user->hasMarchandPermission($user->marchand, $marchandPermission);
            }
        }

        if (!$hasPermission) {
            abort(403, 'Vous n\'avez pas les permissions nécessaires pour accéder à cette ressource.');
        }

        return $next($request);
    }
}
