<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        // Routes API pour les avatars (cross-domain depuis lorrelei)
        'api/avatar/*',

        // Routes API pour les images de reviews (cross-domain depuis lorrelei)
        'api/reviews/*',

        // Routes API pour les images de reviews boutiques (cross-domain depuis lorrelei)
        'api/boutique-reviews/*',

        // Webhook pour les notifications temps réel
        'api/webhook/*',
    ];
}
