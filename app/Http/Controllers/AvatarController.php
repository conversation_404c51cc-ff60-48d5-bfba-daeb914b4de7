<?php

namespace App\Http\Controllers;

use App\Services\AvatarService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Models\Marchand;
use Illuminate\Support\Facades\Log;
class AvatarController extends Controller
{
    private AvatarService $avatarService;

    public function __construct(AvatarService $avatarService)
    {
        $this->avatarService = $avatarService;
    }

    /**
     * Upload avatar pour un utilisateur
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadUserAvatar(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,webp|max:2048',
            'user_id' => 'required|integer|exists:users,id',
            'auth_token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Fichier invalide',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Vérifier le token d'authentification simple
            $secretKey = 'lorrelei-avatar-key-2024';
            $expectedToken = base64_encode($request->user_id . ':' . $secretKey);
            if ($request->auth_token !== $expectedToken) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token d\'authentification invalide'
                ], 401);
            }

            $user = User::findOrFail($request->user_id);
            $file = $request->file('avatar');
            // Suppression de l'ancien avatar
            if ($user->avatar) {
                $this->avatarService->deleteAvatar($user->avatar);
            }

            // Upload du nouvel avatar
            $avatarPath = $this->avatarService->uploadAvatar($file, 'user', $user->id);

            if (!$avatarPath) {
                return response()->json([
                    'success' => false,
                    'message' => 'Erreur lors de l\'upload de l\'avatar'
                ], 500);
            }

            // Mise à jour de l'utilisateur
            $user->update(['avatar' => $avatarPath]);

            return response()->json([
                'success' => true,
                'message' => 'Avatar mis à jour avec succès',
                'avatar_url' => $this->avatarService->getAvatarUrl($avatarPath)
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'upload d\'avatar: ' . $e->getMessage(), [
                'user_id' => $request->user_id,
                'user_email' => $user->email ?? 'unknown',
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Erreur serveur lors de l\'upload'
            ], 500);
        }
    }

    /**
     * Upload avatar pour un marchand
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadMarchandAvatar(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,webp|max:2048',
            'user_id' => 'required|integer|exists:users,id',
            'auth_token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Fichier invalide',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Vérifier le token d'authentification simple
            $secretKey = 'lorrelei-avatar-key-2024';
            $expectedToken = base64_encode($request->user_id . ':' . $secretKey);
            if ($request->auth_token !== $expectedToken) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token d\'authentification invalide'
                ], 401);
            }

            $user = User::findOrFail($request->user_id);
            $marchand = $user->marchand;

            if (!$marchand) {
                return response()->json([
                    'success' => false,
                    'message' => 'Marchand non trouvé'
                ], 404);
            }

            $file = $request->file('avatar');

            // Suppression de l'ancien logo
            if ($marchand->logo) {
                $this->avatarService->deleteAvatar($marchand->logo);
            }

            // Upload du nouveau logo
            $logoPath = $this->avatarService->uploadAvatar($file, 'marchand', $marchand->id);

            if (!$logoPath) {
                return response()->json([
                    'success' => false,
                    'message' => 'Erreur lors de l\'upload du logo'
                ], 500);
            }

            // Mise à jour du marchand
            $marchand->update(['logo' => $logoPath]);

            return response()->json([
                'success' => true,
                'message' => 'Logo de la boutique mis à jour avec succès',
                'logo_url' => $this->avatarService->getAvatarUrl($logoPath)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur serveur lors de l\'upload'
            ], 500);
        }
    }

    /**
     * Supprime l'avatar d'un utilisateur
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteUserAvatar(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'auth_token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Paramètres invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Vérifier le token d'authentification simple
            $secretKey = 'lorrelei-avatar-key-2024';
            $expectedToken = base64_encode($request->user_id . ':' . $secretKey);
            if ($request->auth_token !== $expectedToken) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token d\'authentification invalide'
                ], 401);
            }

            $user = User::findOrFail($request->user_id);

            if ($user->avatar) {
                $this->avatarService->deleteAvatar($user->avatar);
                $user->update(['avatar' => null]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Avatar supprimé avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la suppression'
            ], 500);
        }
    }

    /**
     * Supprime l'avatar d'un marchand
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteMarchandAvatar(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'auth_token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Paramètres invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Vérifier le token d'authentification simple
            $secretKey = 'lorrelei-avatar-key-2024';
            $expectedToken = base64_encode($request->user_id . ':' . $secretKey);
            if ($request->auth_token !== $expectedToken) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token d\'authentification invalide'
                ], 401);
            }

            $user = User::findOrFail($request->user_id);
            $marchand = $user->marchand;

            if (!$marchand) {
                return response()->json([
                    'success' => false,
                    'message' => 'Marchand non trouvé'
                ], 404);
            }

            if ($marchand->logo) {
                $this->avatarService->deleteAvatar($marchand->logo);
                $marchand->update(['logo' => null]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Logo de la boutique supprimé avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la suppression'
            ], 500);
        }
    }

    /**
     * Récupère l'URL de l'avatar d'un utilisateur
     *
     * @param int $userId
     * @return JsonResponse
     */
    public function getUserAvatar(int $userId): JsonResponse
    {
        try {
            $user = User::findOrFail($userId);

            return response()->json([
                'success' => true,
                'avatar_url' => $this->avatarService->getAvatarUrl($user->avatar),
                'initials' => $this->avatarService->generateInitials($user->name ?? 'User')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Utilisateur non trouvé'
            ], 404);
        }
    }

    /**
     * Récupère l'URL de l'avatar d'un marchand
     *
     * @param int $marchandId
     * @return JsonResponse
     */
    public function getMarchandAvatar(int $marchandId): JsonResponse
    {
        try {
            $marchand = Marchand::findOrFail($marchandId);

            return response()->json([
                'success' => true,
                'logo_url' => $this->avatarService->getAvatarUrl($marchand->logo),
                'initials' => $this->avatarService->generateInitials($marchand->nomEntreprise ?? 'Boutique')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Marchand non trouvé'
            ], 404);
        }
    }
}
