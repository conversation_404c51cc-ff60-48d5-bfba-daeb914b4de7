<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ReviewImageController extends Controller
{
    /**
     * Upload des images de reviews depuis le projet lorrelei
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadImages(Request $request): JsonResponse
    {
        // Valider les données
        $validator = Validator::make($request->all(), [
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'product_id' => 'required|string',
            'folder_prefix' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $image = $request->file('image');
            $productId = $request->input('product_id');
            $folderPrefix = $request->input('folder_prefix');

            // Créer le dossier de stockage
            $storagePath = public_path("images/reviews/{$folderPrefix}");
            if (!file_exists($storagePath)) {
                mkdir($storagePath, 0755, true);
            }

            // Générer un nom de fichier unique
            $filename = Str::uuid() . '.' . $image->getClientOriginalExtension();

            // Déplacer le fichier
            $image->move($storagePath, $filename);

            // Générer l'URL de l'image
            $imageUrl = url("/images/reviews/{$folderPrefix}/{$filename}");

            return response()->json([
                'success' => true,
                'filename' => $filename,
                'folder' => $folderPrefix,
                'url' => $imageUrl,
                'message' => 'Image uploadée avec succès'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Erreur lors de l\'upload: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Supprime une image de review
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteImage(Request $request): JsonResponse
    {
        // Valider les données
        $validator = Validator::make($request->all(), [
            'filename' => 'required|string',
            'folder' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $filename = $request->input('filename');
            $folder = $request->input('folder');

            // Construire le chemin du fichier
            $filePath = public_path("images/reviews/{$folder}/{$filename}");

            // Vérifier si le fichier existe et le supprimer
            if (file_exists($filePath)) {
                unlink($filePath);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Image supprimée avec succès'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'Fichier non trouvé'
                ], 404);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Erreur lors de la suppression: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupère les informations d'une image de review
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getImageInfo(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'filename' => 'required|string',
            'folder' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $filename = $request->input('filename');
            $folder = $request->input('folder');

            // Construire le chemin du fichier
            $filePath = public_path("images/reviews/{$folder}/{$filename}");

            if (file_exists($filePath)) {
                $imageUrl = url("/images/reviews/{$folder}/{$filename}");
                $fileSize = filesize($filePath);
                
                return response()->json([
                    'success' => true,
                    'filename' => $filename,
                    'folder' => $folder,
                    'url' => $imageUrl,
                    'size' => $fileSize,
                    'exists' => true
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'exists' => false,
                    'error' => 'Fichier non trouvé'
                ], 404);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Erreur lors de la récupération: ' . $e->getMessage()
            ], 500);
        }
    }
}
