<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Broadcast;

class WebhookController extends Controller
{
    /**
     * Recevoir les notifications en temps réel depuis lorrelei
     */
    public function realtimeNotification(Request $request)
    {
        try {
            $type = $request->input('type');
            $data = $request->input('data');
            $timestamp = $request->input('timestamp');

            Log::info('Notification temps réel reçue', [
                'type' => $type,
                'timestamp' => $timestamp
            ]);

            // Diffuser l'événement selon le type
            switch ($type) {
                case 'new_message':
                    $this->handleNewMessage($data);
                    break;
                
                case 'new_dispute':
                    $this->handleNewDispute($data);
                    break;
                
                case 'new_dispute_message':
                    $this->handleNewDisputeMessage($data);
                    break;
                
                case 'dispute_status_change':
                    $this->handleDisputeStatusChange($data);
                    break;
                
                default:
                    Log::warning('Type de notification non reconnu', ['type' => $type]);
                    return response()->json(['error' => 'Type non reconnu'], 400);
            }

            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            Log::error('Erreur lors du traitement de la notification temps réel', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json(['error' => 'Erreur serveur'], 500);
        }
    }

    /**
     * Gérer un nouveau message de conversation
     */
    private function handleNewMessage(array $data)
    {
        // Diffuser l'événement aux utilisateurs connectés
        broadcast(new \App\Events\NewMessageReceived($data));
        
        Log::info('Nouveau message diffusé', ['conversation_id' => $data['conversation_id'] ?? null]);
    }

    /**
     * Gérer un nouveau litige
     */
    private function handleNewDispute(array $data)
    {
        // Diffuser l'événement aux admins connectés
        broadcast(new \App\Events\NewDisputeReceived($data));
        
        Log::info('Nouveau litige diffusé', ['dispute_id' => $data['id'] ?? null]);
    }

    /**
     * Gérer un nouveau message de litige
     */
    private function handleNewDisputeMessage(array $data)
    {
        // Diffuser l'événement aux admins connectés
        broadcast(new \App\Events\NewDisputeMessageReceived($data));
        
        Log::info('Nouveau message de litige diffusé', ['dispute_id' => $data['dispute_id'] ?? null]);
    }

    /**
     * Gérer un changement de statut de litige
     */
    private function handleDisputeStatusChange(array $data)
    {
        // Diffuser l'événement aux admins connectés
        broadcast(new \App\Events\DisputeStatusChanged($data));
        
        Log::info('Changement de statut de litige diffusé', ['dispute_id' => $data['id'] ?? null]);
    }
}
