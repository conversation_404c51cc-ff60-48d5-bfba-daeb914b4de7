<?php

namespace App\Http\Controllers;

use App\Models\MerchantValidation;
use App\Models\MerchantValidationDocument;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class MerchantValidationController extends Controller
{
    public function showWelcome()
    {
        return Inertia::render('SellerRegistration/Welcome', [
            'user' => [
                'id' => Auth::id(),
                'name' => Auth::user()->name,
                'email' => Auth::user()->email,
            ]
        ]);
    }

    /**
     * Étape 1 : Informations personnelles
     */
    public function showPersonalInfo()
    {
        $validation = Auth::user()->merchantValidation;
        $countries = $this->getCountries();

        return Inertia::render('SellerRegistration/PersonalInfo', [
            'user' => Auth::user(),
            'validation' => $validation,
            'personalInfo' => $validation?->personal_info ?? [],
            'countries' => $countries,
        ]);
    }

    public function submitPersonalInfo(Request $request)
    {
        Log::info('Personal info submission started', [
            'user_id' => Auth::id(),
            'data' => $request->except(['_token'])
        ]);

        try {
            $validated = $request->validate([
                'prenom' => 'required|string|max:255',
                'nom' => 'required|string|max:255',
                'date_naissance' => 'required|date|before:today',
                'pays_citoyennete' => 'required|string|max:255',
                'pays_naissance' => 'required|string|max:255',
                'adresse_ligne1' => 'required|string|max:255',
                'adresse_ligne2' => 'nullable|string|max:255',
                'ville' => 'required|string|max:255',
                'region' => 'nullable|string|max:255',
                'code_postal' => 'nullable|string|max:20',
                'pays_adresse' => 'required|string|max:255',
                'telephone_verification' => 'required|string|max:20',
            ]);

            $validation = MerchantValidation::updateOrCreate(
                ['user_id' => Auth::id()],
                [
                    'personal_info' => $validated,
                    'status' => MerchantValidation::STATUS_PERSONAL_INFO_COMPLETED,
                ]
            );

            Log::info('Personal info submitted', [
                'validation_id' => $validation->id,
                'user_id' => Auth::id(),
            ]);

            return to_route('seller.billing');

        } catch (\Exception $e) {
            Log::error('Personal info submission error', ['error' => $e->getMessage()]);
            return back()->withErrors(['error' => 'Erreur lors de l\'enregistrement: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Étape 2 : Facturation et informations business
     */
    public function showBilling()
    {
        $validation = Auth::user()->merchantValidation;
        $countries = $this->getCountries();

        return Inertia::render('SellerRegistration/Billing', [
            'user' => Auth::user(),
            'validation' => $validation,
            'billingInfo' => $validation?->billing_info ?? [],
            'countries' => $countries,
        ]);
    }

    public function submitBilling(Request $request)
    {
        Log::info('Billing info submission started', [
            'user_id' => Auth::id(),
            'data' => $request->except(['_token'])
        ]);

        try {
            $validated = $request->validate([
                'type_business' => 'required|string|in:individuel,entreprise,cooperative,grande_entreprise',
                'nomEntreprise' => 'required|string|max:255',
                'pays_business' => 'required|string|max:255',
                'ville_business' => 'required|string|max:255',
                'telephone_principal' => 'required|string|max:20',
                'email_business' => 'nullable|email|max:255',
                'chiffre_affaires_estime' => 'nullable|numeric|min:0',
                'nombre_employes' => 'nullable|integer|min:0',
                // Informations bancaires
                'methode_paiement_preferee' => 'required|string|in:rib,orange_money,mtn_money',
                'iban' => 'required_if:methode_paiement_preferee,rib|nullable|string|max:34',
                'nom_titulaire_compte' => 'required_if:methode_paiement_preferee,rib|nullable|string|max:255',
                'numero_orange_money' => 'required_if:methode_paiement_preferee,orange_money|nullable|string|max:20',
                'numero_mtn_money' => 'required_if:methode_paiement_preferee,mtn_money|nullable|string|max:20',
            ]);

            $validation = MerchantValidation::updateOrCreate(
                ['user_id' => Auth::id()],
                [
                    'billing_info' => $validated,
                    'status' => MerchantValidation::STATUS_BILLING_INFO_COMPLETED,
                ]
            );

            Log::info('Billing info submitted', [
                'validation_id' => $validation->id,
                'user_id' => Auth::id(),
            ]);

            return to_route('seller.store-setup');

        } catch (\Exception $e) {
            Log::error('Billing info submission error', ['error' => $e->getMessage()]);
            return back()->withErrors(['error' => 'Erreur lors de l\'enregistrement: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Étape 3 : Configuration de la boutique
     */
    public function showStoreSetup()
    {
        $validation = Auth::user()->merchantValidation;

        return Inertia::render('SellerRegistration/StoreSetup', [
            'user' => Auth::user(),
            'validation' => $validation,
            'storeInfo' => $validation?->store_info ?? [],
        ]);
    }

    public function submitStoreSetup(Request $request)
    {
        Log::info('Store setup submission started', [
            'user_id' => Auth::id(),
            'data' => $request->except(['_token'])
        ]);

        try {
            $validated = $request->validate([
                'nom_boutique' => 'required|string|max:255',
                'description_business' => 'required|string|max:1000',
                'categories_produits' => 'required|array|min:1',
                'categories_produits.*' => 'string|max:255',
                'site_web' => 'nullable|url|max:255',
                'accepte_conditions' => 'required|accepted',
            ]);

            $validation = MerchantValidation::updateOrCreate(
                ['user_id' => Auth::id()],
                [
                    'store_info' => $validated,
                    'status' => MerchantValidation::STATUS_STORE_INFO_COMPLETED,
                ]
            );

            Log::info('Store setup submitted', [
                'validation_id' => $validation->id,
                'user_id' => Auth::id(),
            ]);

            return to_route('seller.documents');

        } catch (\Exception $e) {
            Log::error('Store setup submission error', ['error' => $e->getMessage()]);
            return back()->withErrors(['error' => 'Erreur lors de l\'enregistrement: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Méthode utilitaire pour obtenir la liste des pays
     */
    private function getCountries(): array
    {
        return [
            'CM' => 'Cameroun',
            'FR' => 'France',
            'BE' => 'Belgique',
            'CH' => 'Suisse',
            'CA' => 'Canada',
            'CI' => 'Côte d\'Ivoire',
            'SN' => 'Sénégal',
            'GA' => 'Gabon',
            'CG' => 'Congo',
            'BJ' => 'Bénin',
            'TG' => 'Togo',
            'BF' => 'Burkina Faso',
            'ML' => 'Mali',
            'NE' => 'Niger',
            'TD' => 'Tchad',
            'CF' => 'République Centrafricaine',
        ];
    }

    public function showBusinessInfo()
    {
        $validation = Auth::user()->merchantValidation;

        if (!$validation || $validation->status === MerchantValidation::STATUS_EN_ATTENTE_SOUMISSION) {
            return Inertia::render('SellerRegistration/BusinessInfo', [
                'user' => [
                    'id' => Auth::id(),
                    'name' => Auth::user()->name,
                    'email' => Auth::user()->email,
                ],
                'marchand' => $validation ? [
                    'nomEntreprise' => $validation->business_info['company_name'] ?? null,
                    'pays_business' => $validation->business_info['country'] ?? null,
                    'ville_business' => $validation->business_info['city'] ?? null,
                    'type_business' => $validation->business_info['business_sector'] ?? null,
                    'description_business' => $validation->business_info['description'] ?? null,
                    'telephone_principal' => $validation->business_info['phone'] ?? null,
                    'email_business' => $validation->business_info['email'] ?? null,
                    'site_web' => $validation->business_info['website'] ?? null,
                    'chiffre_affaires_estime' => $validation->business_info['estimated_revenue'] ?? null,
                    'nombre_employes' => $validation->business_info['employees_count'] ?? null,
                ] : null,
                'countries' => [
                    'CM' => 'Cameroun',
                    'FR' => 'France',
                    'BE' => 'Belgique',
                    'CH' => 'Suisse',
                    'CA' => 'Canada',
                    'CI' => 'Côte d\'Ivoire',
                    'SN' => 'Sénégal',
                    'GA' => 'Gabon',
                    'CG' => 'Congo',
                    'BJ' => 'Bénin'
                ]
            ]);
        }

        return redirect()->route('seller.documents');
    }

    public function submitBusinessInfo(Request $request)
    {
        Log::info('Business info submission started', [
            'user_id' => Auth::id(),
            'data' => $request->except(['_token'])
        ]);

        try {
            $validated = $request->validate([
                'nomEntreprise' => 'required|string|max:255',
                'pays_business' => 'required|string|max:255',
                'ville_business' => 'required|string|max:255',
                'type_business' => 'required|string|in:individuel,entreprise,cooperative,grande_entreprise',
                'description_business' => 'nullable|string|max:1000',
                'telephone_principal' => 'required|string|max:20',
                'email_business' => 'nullable|email|max:255',
                'site_web' => 'nullable|url|max:255',
                'chiffre_affaires_estime' => 'nullable|numeric|min:0',
                'nombre_employes' => 'nullable|integer|min:0',
                'accepte_conditions' => 'required|accepted',
            ]);

            $validation = MerchantValidation::updateOrCreate(
                ['user_id' => Auth::id()],
                [
                    'business_info' => $validated,
                    'status' => MerchantValidation::STATUS_INFORMATIONS_SOUMISES,
                    'submitted_at' => now(),
                ]
            );

            Log::info('Business info submitted', [
                'validation_id' => $validation->id,
                'user_id' => Auth::id(),
            ]);

            return to_route('seller.documents');

        } catch (\Exception $e) {
            Log::error('Business info submission error', ['error' => $e->getMessage()]);
            return back()->withErrors(['error' => 'Erreur lors de l\'enregistrement: ' . $e->getMessage()])->withInput();
        }
    }

    public function showDocuments()
    {
        $validation = Auth::user()->merchantValidation;

        if (!$validation || !$validation->isAllStepsCompleted()) {
            return redirect()->route('seller.welcome');
        }

        $requiredDocuments = $validation->getRequiredDocuments();
        $documentsLabels = [
            'piece_identite' => 'Pièce d\'identité',
            'photo_avec_piece' => 'Photo avec pièce d\'identité',
            'justificatif_domicile' => 'Justificatif de domicile',
            'registre_commerce' => 'Registre de commerce',
            'statuts_entreprise' => 'Statuts de l\'entreprise',
            'rib_bancaire' => 'RIB bancaire',
            'recepisse_declaration' => 'Récépissé de déclaration',
            'bilan_comptable' => 'Bilan comptable',
            'declaration_fiscale' => 'Déclaration fiscale',
        ];

        $documentsRequis = [];
        foreach ($requiredDocuments as $docType) {
            $documentsRequis[$docType] = $documentsLabels[$docType] ?? $docType;
        }

        // Récupérer les documents existants
        $documentsExistants = [];
        foreach ($validation->documents as $doc) {
            $documentsExistants[$doc->document_type] = [
                'id' => $doc->id,
                'type_document' => $doc->document_type,
                'nom_original' => $doc->original_name,
                'statut_validation' => $doc->status,
                'date_upload' => $doc->created_at->format('Y-m-d H:i:s'),
                'taille_fichier' => $doc->file_size,
                'url' => Storage::url($doc->file_path),
                'mime_type' => $doc->mime_type,
            ];
        }
        

        return Inertia::render('SellerRegistration/Documents', [
            'marchand' => [
                'id' => $validation->user_id,
                'nomEntreprise' => $validation->store_info['nom_boutique'] ?? 'Boutique',
                'type_business' => $validation->billing_info['type_business'] ?? 'individuel',
                'etape_inscription' => 'documents',
            ],
            'documentsRequis' => $requiredDocuments,
            'typesDocuments' => $documentsRequis,
            'documentsExistants' => $documentsExistants,
            'extensionsAutorisees' => ['.pdf', '.jpg', '.jpeg', '.png'],
            'tailleMaximale' => 10485760, // 10MB
        ]);
    }

    public function uploadDocument(Request $request)
    {
        $validation = Auth::user()->merchantValidation;

        if (!$validation) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Validation non trouvée'], 404);
            }
            return back()->withErrors(['error' => 'Validation non trouvée']);
        }

        $allowedTypes = $validation->getRequiredDocuments();

        $request->validate([
            'document' => 'required|file|mimes:pdf,jpg,jpeg,png|max:10240', // 10MB max
            'type' => 'required|string|in:' . implode(',', $allowedTypes),
        ]);

        $file = $request->file('document');
        $path = Storage::disk('public')->put('documents/marchand/validation', $file);

        // Supprimer l'ancien document du même type s'il existe
        $oldDocument = $validation->documents()->where('document_type', $request->type)->first();
        if ($oldDocument) {
            Storage::delete($oldDocument->file_path);
            $oldDocument->delete();
        }
        Log::info('Document en cours d\'upload', [
            'validation_id' => $validation->id,
            'document_type' => $request->type,
            'file_path' => $path,
            'original_name' => $file->getClientOriginalName(),
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'status' => MerchantValidationDocument::STATUS_EN_ATTENTE,
        ]);
        $document = MerchantValidationDocument::create([
            'validation_id' => $validation->id,
            'document_type' => $request->type,
            'file_path' => $path,
            'original_name' => $file->getClientOriginalName(),
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'status' => MerchantValidationDocument::STATUS_EN_ATTENTE,
        ]);
        // Mettre à jour le statut si tous les documents requis sont uploadés
        $requiredTypes = $validation->getRequiredDocuments();
        $uploadedTypes = $validation->documents()->pluck('document_type')->toArray();
        $allDocumentsUploaded = true;
        foreach ($requiredTypes as $docType) {
            if (!in_array(strtoupper($docType), $uploadedTypes)) {
                $allDocumentsUploaded = false;
                break;
            }
        }
        if ($allDocumentsUploaded) {
            $validation->update([
                'status' => MerchantValidation::STATUS_DOCUMENTS_SOUMIS,
            ]);
        }

        // Pour les uploads Inertia, retourner une réponse JSON
        if ($request->expectsJson() || $request->header('Accept') === 'application/json') {
            return response()->json([
                'success' => true,
                'message' => 'Document uploadé avec succès',
                'document' => $document,
            ]);
        }
        Log::info('Document uploadé', [
            'validation_id' => $validation->id,
            'document_type' => $request->type,
            'file_path' => $path,
            'original_name' => $file->getClientOriginalName(),
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'status' => MerchantValidationDocument::STATUS_EN_ATTENTE,
        ]);
        return back()->with('success', 'Document uploadé avec succès');
    }

    public function submitForReview()
    {
        $validation = Auth::user()->merchantValidation;
        if (!$validation || !$validation->isComplete()) {
            return back()->withErrors(['error' => 'Dossier incomplet']);
        }

        DB::transaction(function () use ($validation) {
            $validation->update([
                'status' => MerchantValidation::STATUS_EN_ATTENTE_VALIDATION,
                'submitted_at' => now(),
            ]);

            // Déclencher l'événement de nouvelle soumission
            event(new \App\Events\MerchantSubmissionReceived($validation));
        });

        return redirect()->route('seller.submission-complete');
    }

    public function showSubmissionComplete()
    {
        $validation = Auth::user()->merchantValidation;

        if (!$validation || !in_array($validation->status, [
            MerchantValidation::STATUS_EN_ATTENTE_VALIDATION,
            MerchantValidation::STATUS_VALIDE,
            MerchantValidation::STATUS_REJETE,
        ])) {
            return redirect()->route('seller.welcome');
        }

        return Inertia::render('SellerRegistration/Success', [
            'validation' => $validation,
        ]);
    }

    /**
     * Télécharger un document de validation pour le marchand connecté
     */
    public function downloadDocument($documentId)
    {
        $validation = Auth::user()->merchantValidation;
        if (!$validation) {
            abort(403, 'Aucune validation trouvée.');
        }
        $document = $validation->documents()->where('id', $documentId)->first();
        if (!$document) {
            abort(404, 'Document introuvable.');
        }
        return Storage::disk('public')->download($document->file_path, $document->original_name);
    }

    /**
     * Visualiser un document de validation pour le marchand connecté
     */
    public function viewDocumentMarchand(MerchantValidation $validation, MerchantValidationDocument $document)
    {
        // Vérifier que l'utilisateur est bien le propriétaire de cette validation
        if ($validation->user_id !== Auth::id()) {
            abort(403, 'Accès non autorisé');
        }

        // Vérifier que le document appartient bien à cette validation
        if ($document->validation_id !== $validation->id) {
            abort(404, 'Document non trouvé');
        }

        // Vérifier que le fichier existe
        if (!Storage::disk('public')->exists($document->file_path)) {
            abort(404, 'Fichier non trouvé sur le serveur');
        }

        // Vérifier que c'est un type de fichier visualisable
        $extension = strtolower(pathinfo($document->original_name, PATHINFO_EXTENSION));
        if (!in_array($extension, ['pdf', 'jpg', 'jpeg', 'png', 'gif'])) {
            abort(400, 'Ce type de fichier ne peut pas être visualisé');
        }

        // Retourner le fichier pour visualisation
        return Storage::disk('public')->response($document->file_path);
    }

    /**
     * Télécharger un document de validation spécifique pour le marchand connecté
     */
    public function downloadDocumentMarchand(MerchantValidation $validation, MerchantValidationDocument $document)
    {
        // Vérifier que l'utilisateur est bien le propriétaire de cette validation
        if ($validation->user_id !== Auth::id()) {
            abort(403, 'Accès non autorisé');
        }

        // Vérifier que le document appartient bien à cette validation
        if ($document->validation_id !== $validation->id) {
            abort(404, 'Document non trouvé');
        }

        // Vérifier que le fichier existe
        if (!Storage::disk('public')->exists($document->file_path)) {
            abort(404, 'Fichier non trouvé sur le serveur');
        }

        // Télécharger le fichier
        return Storage::disk('public')->download(
            $document->file_path,
            $document->original_name
        );
    }

    /**
     * Télécharger tous les documents de validation pour le marchand connecté
     */
    public function downloadAllDocumentsMarchand(MerchantValidation $validation)
    {
        // Vérifier que l'utilisateur est bien le propriétaire de cette validation
        if ($validation->user_id !== Auth::id()) {
            abort(403, 'Accès non autorisé');
        }

        // Vérifier qu'il y a des documents
        if ($validation->documents->count() === 0) {
            abort(404, 'Aucun document trouvé');
        }

        // Créer un fichier ZIP temporaire
        $zipFileName = 'documents_validation_' . $validation->id . '_' . now()->format('Y-m-d_H-i-s') . '.zip';
        $zipPath = storage_path('app/temp/' . $zipFileName);

        // Créer le répertoire temp s'il n'existe pas
        if (!file_exists(dirname($zipPath))) {
            mkdir(dirname($zipPath), 0755, true);
        }

        $zip = new \ZipArchive();
        if ($zip->open($zipPath, \ZipArchive::CREATE) !== TRUE) {
            abort(500, 'Impossible de créer l\'archive ZIP');
        }

        // Ajouter chaque document au ZIP
        foreach ($validation->documents as $document) {
            if ($document->file_path && Storage::disk('public')->exists($document->file_path)) {
                $fileContent = Storage::disk('public')->get($document->file_path);
                $zip->addFromString($document->original_name, $fileContent);
            }
        }

        $zip->close();

        // Vérifier que le fichier ZIP a été créé
        if (!file_exists($zipPath)) {
            abort(500, 'Erreur lors de la création de l\'archive');
        }

        // Retourner le fichier ZIP et le supprimer après téléchargement
        return response()->download($zipPath, $zipFileName)->deleteFileAfterSend(true);
    }
}
