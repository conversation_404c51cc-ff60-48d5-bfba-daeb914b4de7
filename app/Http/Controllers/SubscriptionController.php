<?php

namespace App\Http\Controllers;

use App\Models\MarchandAbonnement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class SubscriptionController extends Controller
{
    /**
     * Affiche la page de choix d'abonnement
     */
    public function choose()
    {
        $user = Auth::user();
        $marchand = $user->marchand;
        
        if (!$marchand) {
            return redirect()->route('seller.welcome')
                ->with('error', 'Vous devez d\'abord compléter votre inscription marchand.');
        }

        $currentSubscription = MarchandAbonnement::where('marchand_id', $marchand->id)
            ->where('statut', 'actif')
            ->first();

        $isTrialActive = $currentSubscription && 
                        $currentSubscription->type_abonnement === 'trial' && 
                        $currentSubscription->fin_periode_essai > now();

        $plans = $this->getAvailablePlans($currentSubscription);

        return Inertia::render('Subscription/Choose', [
            'currentSubscription' => $currentSubscription,
            'isTrialActive' => $isTrialActive,
            'trialDaysRemaining' => $isTrialActive ? 
                now()->diffInDays($currentSubscription->fin_periode_essai, false) : 0,
            'plans' => $plans,
            'marchand' => $marchand,
        ]);
    }

    /**
     * Traite la sélection d'un plan d'abonnement
     */
    public function subscribe(Request $request)
    {
        $request->validate([
            'plan' => 'required|in:gratuit,basique,premium,elite',
        ]);

        $user = Auth::user();
        $marchand = $user->marchand;
        
        if (!$marchand) {
            return back()->with('error', 'Marchand non trouvé.');
        }

        $planType = $request->plan;
        
        // Désactiver l'abonnement actuel
        MarchandAbonnement::where('marchand_id', $marchand->id)
            ->where('statut', 'actif')
            ->update(['statut' => 'inactif']);

        // Créer le nouvel abonnement
        $newSubscription = MarchandAbonnement::creerAbonnement($marchand->id, $planType);

        return redirect()->route('marchand.dashboard')
            ->with('success', "Votre abonnement {$planType} a été activé avec succès !");
    }

    /**
     * Obtient les plans disponibles
     */
    private function getAvailablePlans($currentSubscription): array
    {
        $plans = [
            'gratuit' => [
                'name' => 'Gratuit',
                'price' => 0,
                'period' => 'mois',
                'commission' => '5-10%',
                'description' => 'Parfait pour commencer votre activité',
                'features' => [
                    'Produits illimités',
                    'Support par email',
                    'Commission standard',
                    'Tableau de bord de base',
                ],
                'color' => 'gray',
                'popular' => false,
                'buttonText' => 'Commencer gratuitement',
            ],
            'basique' => [
                'name' => 'Basique',
                'price' => 32797.85,
                'period' => 'mois',
                'commission' => '4-8%',
                'description' => 'Idéal pour les marchands en croissance',
                'features' => [
                    'Toutes les fonctionnalités Gratuit',
                    'Support prioritaire (email + chat)',
                    'Commission réduite',
                    'Réduction logistique 5%',
                    'Rapports détaillés',
                ],
                'color' => 'blue',
                'popular' => true,
                'buttonText' => 'Choisir Basique',
            ],
            'premium' => [
                'name' => 'Premium',
                'price' => 65595.70,
                'period' => 'mois',
                'commission' => '3-6%',
                'description' => 'Pour les marchands établis',
                'features' => [
                    'Toutes les fonctionnalités Basique',
                    'Analytics avancées',
                    'Gestionnaire dédié',
                    'Commission très réduite',
                    'Réduction logistique 10%',
                    'Campagnes marketing',
                ],
                'color' => 'purple',
                'popular' => false,
                'buttonText' => 'Choisir Premium',
            ],
            'elite' => [
                'name' => 'Elite',
                'price' => 131191.40,
                'period' => 'mois',
                'commission' => '2-4%',
                'description' => 'Pour les grandes entreprises',
                'features' => [
                    'Toutes les fonctionnalités Premium',
                    'IA prédictive',
                    'Événements exclusifs',
                    'Commission minimale',
                    'Réduction logistique 15%',
                    'Support téléphonique 24/7',
                    'Intégrations personnalisées',
                ],
                'color' => 'gold',
                'popular' => false,
                'buttonText' => 'Choisir Elite',
            ],
        ];

        // Marquer le plan actuel
        if ($currentSubscription) {
            $currentType = $currentSubscription->type_abonnement === 'trial' ? 'basique' : $currentSubscription->type_abonnement;
            if (isset($plans[$currentType])) {
                $plans[$currentType]['current'] = true;
                $plans[$currentType]['buttonText'] = 'Plan actuel';
            }
        }

        return $plans;
    }
}
