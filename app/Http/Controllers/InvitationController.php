<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Marchand;
use App\Services\UserInvitationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\URL;
use Illuminate\Validation\Rules\Password;
use Inertia\Inertia;
use Carbon\Carbon;

class InvitationController extends Controller
{
    public function __construct(
        private UserInvitationService $invitationService
    ) {}

    /**
     * Afficher la page d'acceptation d'invitation admin
     */
    public function showAdminInvitation(Request $request, User $user, string $token)
    {

        // Vérifier que l'invitation est valide
        if ($user->password_reset_token !== $token ||
            $user->password_reset_expires_at->isPast()) {
            return Inertia::render('auth/InvitationExpired', [
                'type' => 'admin'
            ]);
        }

        // Générer l'URL d'acceptation signée
        $acceptUrl = URL::temporarySignedRoute(
            'admin.invitation.accept',
            Carbon::now('UTC')->addHours(24),
            [
                'user' => $user->id,
                'token' => $token,
            ]
        );

        return Inertia::render('auth/AcceptAdminInvitation', [
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
            ],
            'adminUser' => [
                'role' => $user->adminUser?->role?->name,
                'department' => $user->adminUser?->department,
                'access_level' => $user->adminUser?->access_level,
            ],
            'token' => $token,
            'acceptUrl' => $acceptUrl,
        ]);
    }

    /**
     * Accepter une invitation admin
     */
    public function acceptAdminInvitation(Request $request, User $user, string $token)
    {
        $request->validate([
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        // Vérifier et accepter l'invitation
        if (!$this->invitationService->acceptAdminInvitation($user, $token)) {
            return back()->withErrors([
                'token' => 'Cette invitation est invalide ou expirée.'
            ]);
        }

        // Mettre à jour le mot de passe
        $user->update([
            'password' => Hash::make($request->password),
        ]);

        // Connecter l'utilisateur
        Auth::login($user);

        // Redirection complète (pas Inertia) vers le dashboard Filament
        return redirect()->away(route('filament.admin.pages.dashboard'))
            ->with('success', 'Bienvenue dans l\'équipe admin Lorelei !');
    }

    /**
     * Afficher la page d'acceptation d'invitation marchand
     */
    public function showMarchandInvitation(
        Request $request,
        User $user,
        Marchand $marchand,
        string $token
    ) {
        // Vérifier que l'invitation est valide
        $marchandUser = $user->marchandUsers()
            ->where('marchand_id', $marchand->id)
            ->where('invitation_token', $token)
            ->where('invitation_expires_at', '>', now()->toDateTimeString())
            ->first();

        if (!$marchandUser) {
            return Inertia::render('auth/InvitationExpired', [
                'type' => 'marchand'
            ]);
        }

        // Générer l'URL d'acceptation signée
        $acceptUrl = URL::temporarySignedRoute(
            'marchand.invitation.accept',
            Carbon::now('UTC')->addDays(7),
            [
                'user' => $user->id,
                'marchand' => $marchand->id,
                'token' => $token,
            ]
        );

        return Inertia::render('auth/AcceptMarchandInvitation', [
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
            ],
            'marchand' => [
                'id' => $marchand->id,
                'name' => $marchand->nomEntreprise,
                'description' => $marchand->description_business,
            ],
            'marchandUser' => [
                'role' => $marchandUser->role?->name,
                'access_level' => $marchandUser->access_level,
                'permissions' => $marchandUser->role?->formatted_permissions ?? [],
            ],
            'token' => $token,
            'acceptUrl' => $acceptUrl,
        ]);
    }

    /**
     * Accepter une invitation marchand
     */
    public function acceptMarchandInvitation(
        Request $request,
        User $user,
        Marchand $marchand,
        string $token
    ) {
        $request->validate([
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        // Vérifier et accepter l'invitation
        if (!$this->invitationService->acceptMarchandInvitation($user, $marchand, $token)) {
            return back()->withErrors([
                'token' => 'Cette invitation est invalide ou expirée.'
            ]);
        }

        // Mettre à jour le mot de passe si l'utilisateur n'en a pas
        if (!$user->password) {
            $user->update([
                'password' => Hash::make($request->password),
            ]);
        }

        // Connecter l'utilisateur
        Auth::login($user);

        // Redirection complète (pas Inertia) vers le dashboard Filament
        return redirect()->away(route('filament.marchand.pages.dashboard'))
            ->with('success', 'Bienvenue dans l\'équipe de ' . $marchand->nomEntreprise . ' !');
    }

    /**
     * Afficher la page d'invitation expirée
     */
    public function expired(string $type = 'admin')
    {
        return Inertia::render('auth/InvitationExpired', [
            'type' => $type
        ]);
    }

    /**
     * Renvoyer une invitation (pour les admins)
     */
    public function resendInvitation(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'type' => 'required|in:admin,marchand',
        ]);

        $user = User::findOrFail($request->user_id);

        if ($request->type === 'admin') {
            $adminUser = $user->adminUser;
            if ($adminUser) {
                $this->invitationService->resendAdminInvitation($adminUser);
                return back()->with('success', 'Invitation renvoyée avec succès.');
            }
        } else {
            // Pour les invitations marchand, on a besoin de l'ID du marchand
            $request->validate(['marchand_id' => 'required|exists:marchands,id']);

            $marchandUser = $user->marchandUsers()
                ->where('marchand_id', $request->marchand_id)
                ->first();

            if ($marchandUser) {
                $this->invitationService->resendMarchandInvitation($marchandUser);
                return back()->with('success', 'Invitation renvoyée avec succès.');
            }
        }

        return back()->withErrors(['error' => 'Invitation introuvable.']);
    }
}
