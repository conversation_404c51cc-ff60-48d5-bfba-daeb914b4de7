<?php

namespace App\Helpers;

class ThumbnailHelper
{
    /**
     * Tailles de miniatures par type de contenu
     */
    protected static $thumbnailSizes = [
        'products' => [
            'small' => [150, 150],
            'medium' => [300, 300],
            'large' => [600, 600]
        ],
        'categories' => [
            'small' => [100, 100],
            'medium' => [200, 200],
            'large' => [400, 400]
        ],
        'banners' => [
            'small' => [300, 128],
            'medium' => [600, 257],
            'large' => [1200, 514]
        ],
        'reviews' => [
            'small' => [100, 100],
            'medium' => [200, 200],
            'large' => [400, 400]
        ]
    ];

    /**
     * Obtient l'URL d'une miniature
     *
     * @param string $imagePath Chemin de l'image originale (ex: "products/1/abc123.jpg")
     * @param string $size La taille de la miniature (small, medium, large)
     * @return string L'URL de la miniature ou de l'image originale si la miniature n'existe pas
     */
    public static function getThumbnailUrl(string $imagePath, string $size = 'medium'): string
    {
        if (empty($imagePath)) {
            return '';
        }

        // Extraire les informations du chemin de l'image
        $pathParts = explode('/', $imagePath);
        
        if (count($pathParts) < 3) {
            // Si le chemin n'a pas le format attendu, retourner l'image originale
            return url("images/{$imagePath}");
        }

        $baseDir = $pathParts[0]; // ex: "products"
        $folderPrefix = $pathParts[1]; // ex: "1"
        $filename = $pathParts[2]; // ex: "abc123.jpg"

        // Vérifier si le type de contenu est supporté
        if (!isset(self::$thumbnailSizes[$baseDir])) {
            return url("images/{$imagePath}");
        }

        // Vérifier si la taille demandée existe pour ce type de contenu
        if (!isset(self::$thumbnailSizes[$baseDir][$size])) {
            $size = 'medium'; // Fallback vers medium
        }

        // Construire le chemin de la miniature
        $thumbnailPath = "thumbnail/{$baseDir}/{$folderPrefix}/{$size}/{$filename}";
        $fullThumbnailPath = public_path("images/{$thumbnailPath}");

        // Vérifier si la miniature existe
        if (file_exists($fullThumbnailPath)) {
            return url("images/{$thumbnailPath}");
        }

        // Si la miniature n'existe pas, retourner l'image originale
        return url("images/{$imagePath}");
    }

    /**
     * Obtient l'URL d'une miniature pour un produit
     *
     * @param string $imagePath Chemin de l'image du produit
     * @param string $size La taille (small, medium, large)
     * @return string L'URL de la miniature
     */
    public static function getProductThumbnail(string $imagePath, string $size = 'medium'): string
    {
        return self::getThumbnailUrl($imagePath, $size);
    }

    /**
     * Obtient l'URL d'une miniature pour une catégorie
     *
     * @param string $imagePath Chemin de l'image de la catégorie
     * @param string $size La taille (small, medium, large)
     * @return string L'URL de la miniature
     */
    public static function getCategoryThumbnail(string $imagePath, string $size = 'medium'): string
    {
        return self::getThumbnailUrl($imagePath, $size);
    }

    /**
     * Obtient l'URL d'une miniature pour une bannière
     *
     * @param string $imagePath Chemin de l'image de la bannière
     * @param string $size La taille (small, medium, large)
     * @return string L'URL de la miniature
     */
    public static function getBannerThumbnail(string $imagePath, string $size = 'medium'): string
    {
        return self::getThumbnailUrl($imagePath, $size);
    }

    /**
     * Obtient l'URL d'une miniature pour une review
     *
     * @param string $imagePath Chemin de l'image de la review
     * @param string $size La taille (small, medium, large)
     * @return string L'URL de la miniature
     */
    public static function getReviewThumbnail(string $imagePath, string $size = 'medium'): string
    {
        return self::getThumbnailUrl($imagePath, $size);
    }

    /**
     * Obtient toutes les tailles disponibles pour un type de contenu
     *
     * @param string $baseDir Le type de contenu (products, categories, banners, reviews)
     * @return array Les tailles disponibles
     */
    public static function getAvailableSizes(string $baseDir): array
    {
        return isset(self::$thumbnailSizes[$baseDir]) 
            ? array_keys(self::$thumbnailSizes[$baseDir]) 
            : [];
    }

    /**
     * Obtient les dimensions d'une taille spécifique pour un type de contenu
     *
     * @param string $baseDir Le type de contenu
     * @param string $size La taille
     * @return array|null Les dimensions [largeur, hauteur] ou null si non trouvé
     */
    public static function getSizeDimensions(string $baseDir, string $size): ?array
    {
        return self::$thumbnailSizes[$baseDir][$size] ?? null;
    }

    /**
     * Vérifie si une miniature existe
     *
     * @param string $imagePath Chemin de l'image originale
     * @param string $size La taille de la miniature
     * @return bool True si la miniature existe
     */
    public static function thumbnailExists(string $imagePath, string $size = 'medium'): bool
    {
        if (empty($imagePath)) {
            return false;
        }

        $pathParts = explode('/', $imagePath);
        
        if (count($pathParts) < 3) {
            return false;
        }

        $baseDir = $pathParts[0];
        $folderPrefix = $pathParts[1];
        $filename = $pathParts[2];

        if (!isset(self::$thumbnailSizes[$baseDir][$size])) {
            return false;
        }

        $thumbnailPath = public_path("images/thumbnail/{$baseDir}/{$folderPrefix}/{$size}/{$filename}");
        return file_exists($thumbnailPath);
    }

    /**
     * Génère un set d'URLs pour toutes les tailles d'un type de contenu
     *
     * @param string $imagePath Chemin de l'image originale
     * @return array Tableau associatif [taille => URL]
     */
    public static function getAllThumbnailUrls(string $imagePath): array
    {
        if (empty($imagePath)) {
            return [];
        }

        $pathParts = explode('/', $imagePath);
        
        if (count($pathParts) < 3) {
            return ['original' => url("images/{$imagePath}")];
        }

        $baseDir = $pathParts[0];
        
        if (!isset(self::$thumbnailSizes[$baseDir])) {
            return ['original' => url("images/{$imagePath}")];
        }

        $urls = ['original' => url("images/{$imagePath}")];
        
        foreach (array_keys(self::$thumbnailSizes[$baseDir]) as $size) {
            $urls[$size] = self::getThumbnailUrl($imagePath, $size);
        }

        return $urls;
    }
}
