<?php

namespace App\Helpers;

use App\Models\Currency;

class CurrencyHelper
{
    /**
     * Obtient le code de la devise par défaut
     */
    public static function getDefaultCurrencyCode(): string
    {
        $defaultCurrency = Currency::getDefault();
        return $defaultCurrency ? $defaultCurrency->code : 'FCFA';
    }

    /**
     * Obtient le symbole de la devise par défaut
     */
    public static function getDefaultCurrencySymbol(): string
    {
        $defaultCurrency = Currency::getDefault();
        return $defaultCurrency ? $defaultCurrency->symbol : 'FCFA';
    }

    /**
     * Formate un montant avec la devise par défaut
     */
    public static function format(float $amount, ?string $currencyCode = null): string
    {
        $code = $currencyCode ?? self::getDefaultCurrencyCode();
        
        // Pour FCFA, pas de décimales
        if (in_array($code, ['FCFA', 'XOF', 'XAF'])) {
            return number_format($amount, 0, ',', ' ') . ' FCFA';
        }
        
        // Pour les autres devises, 2 décimales
        $currency = Currency::where('code', $code)->first();
        $symbol = $currency ? $currency->symbol : $code;
        
        return number_format($amount, 2, ',', ' ') . ' ' . $symbol;
    }

    /**
     * Obtient le code de devise pour Filament money()
     * Convertit FCFA vers XOF pour la compatibilité internationale
     */
    public static function getFilamentCurrencyCode(): string
    {
        $defaultCode = self::getDefaultCurrencyCode();
        
        // Convertir FCFA vers XOF pour Filament (standard international)
        if ($defaultCode === 'FCFA') {
            return 'XOF';
        }
        
        return $defaultCode;
    }

    /**
     * Vérifie si une devise utilise des décimales
     */
    public static function hasDecimals(string $currencyCode): bool
    {
        // Les devises CFA n'utilisent généralement pas de décimales
        return !in_array($currencyCode, ['FCFA', 'XOF', 'XAF']);
    }

    /**
     * Obtient toutes les devises actives pour les sélecteurs
     */
    public static function getActiveCurrenciesForSelect(): array
    {
        return Currency::getActive()
            ->pluck('name', 'code')
            ->toArray();
    }

    /**
     * Convertit un montant d'une devise vers une autre
     */
    public static function convert(float $amount, string $fromCurrency, string $toCurrency): float
    {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        $fromCurrencyModel = Currency::where('code', $fromCurrency)->first();
        $toCurrencyModel = Currency::where('code', $toCurrency)->first();

        if (!$fromCurrencyModel || !$toCurrencyModel) {
            return $amount; // Retourner le montant original si les devises ne sont pas trouvées
        }

        // Convertir vers la devise de base (FCFA) puis vers la devise cible
        $baseAmount = $amount / $fromCurrencyModel->exchange_rate;
        return $baseAmount * $toCurrencyModel->exchange_rate;
    }
}
