<?php

namespace App\Helpers;

class FileHelper
{
    /**
     * Obtenir l'URL complète d'un fichier stocké dans lorrelei/
     */
    public static function getLorreleiFileUrl(string $path): string
    {
        $lorreleiUrl = config('app.lorrelei_url', 'http://localhost:8000');
        return rtrim($lorreleiUrl, '/') . '/storage/' . ltrim($path, '/');
    }

    /**
     * Obtenir l'URL d'une pièce jointe de conversation
     */
    public static function getConversationAttachmentUrl(string $conversationId, string $filename): string
    {
        return self::getLorreleiFileUrl("conversations/{$conversationId}/{$filename}");
    }

    /**
     * Obtenir l'URL d'une pièce jointe de litige
     */
    public static function getDisputeAttachmentUrl(string $disputeId, string $filename): string
    {
        return self::getLorreleiFileUrl("disputes/{$disputeId}/{$filename}");
    }

    /**
     * Obtenir l'URL d'une image de produit
     */
    public static function getProductImageUrl(string $imagePath): string
    {
        return self::getLorreleiFileUrl("products/{$imagePath}");
    }

    /**
     * Vérifier si un fichier est une image
     */
    public static function isImage(string $filename): bool
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($extension, $imageExtensions);
    }

    /**
     * Obtenir l'icône appropriée pour un type de fichier
     */
    public static function getFileIcon(string $filename): string
    {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        return match($extension) {
            'pdf' => '📄',
            'doc', 'docx' => '📝',
            'xls', 'xlsx' => '📊',
            'ppt', 'pptx' => '📋',
            'zip', 'rar', '7z' => '🗜️',
            'jpg', 'jpeg', 'png', 'gif', 'webp' => '🖼️',
            'mp4', 'avi', 'mov' => '🎥',
            'mp3', 'wav', 'ogg' => '🎵',
            default => '📎'
        };
    }

    /**
     * Formater la taille d'un fichier
     */
    public static function formatFileSize(int $bytes): string
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }
}
