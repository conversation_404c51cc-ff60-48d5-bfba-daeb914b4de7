<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Produit;
use App\Services\MeilisearchService;

class TestDiscountPrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meilisearch:test-discounts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Teste les prix promotionnels dans Meilisearch vs base de données';

    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        parent::__construct();
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('💰 Test des prix promotionnels...');

        try {
            // Chercher des produits avec discount_price
            $produitsAvecDiscount = Produit::whereNotNull('discount_price')
                ->where('discount_price', '>', 0)
                ->with(['categorie', 'marchand', 'reviews'])
                ->limit(5)
                ->get();

            if ($produitsAvecDiscount->isEmpty()) {
                $this->warn('⚠️  Aucun produit avec discount_price trouvé');
                return Command::SUCCESS;
            }

            foreach ($produitsAvecDiscount as $produit) {
                $this->info("📦 Produit ID: {$produit->id}");
                
                // Gérer le nom multilangue
                $nomProduit = is_array($produit->nom) ? json_encode($produit->nom) : $produit->nom;
                $this->line("   Nom: {$nomProduit}");
                
                // Données de la base de données
                $this->line("   Prix DB: {$produit->prix}");
                $this->line("   Discount Price DB: " . ($produit->discount_price ?? 'null'));
                $this->line("   Discount Start DB: " . ($produit->discount_start_date ?? 'null'));
                $this->line("   Discount End DB: " . ($produit->discount_end_date ?? 'null'));
                $this->line("   Is On Discount DB: " . ($produit->is_on_discount ? 'true' : 'false'));

                // Rechercher dans Meilisearch
                $results = $this->meilisearchService->searchProduits('', ["id = {$produit->id}"]);
                
                if (!empty($results['hits'])) {
                    $produitMeili = $results['hits'][0];
                    $this->line("   Prix Meilisearch: " . ($produitMeili['prix'] ?? 'null'));
                    $this->line("   Discount Price Meilisearch: " . ($produitMeili['discount_price'] ?? 'null'));
                    $this->line("   Discount Start Meilisearch: " . ($produitMeili['discount_start_date'] ?? 'null'));
                    $this->line("   Discount End Meilisearch: " . ($produitMeili['discount_end_date'] ?? 'null'));
                    $this->line("   Is On Discount Meilisearch: " . ($produitMeili['is_on_discount'] ? 'true' : 'false'));

                    // Comparer les valeurs
                    $prixMatch = (float)$produit->prix === (float)($produitMeili['prix'] ?? 0);
                    $discountMatch = (float)($produit->discount_price ?? 0) === (float)($produitMeili['discount_price'] ?? 0);
                    
                    if ($prixMatch && $discountMatch) {
                        $this->info("   ✅ Prix cohérents");
                    } else {
                        $this->error("   ❌ Prix incohérents !");
                    }
                } else {
                    $this->warn("   ⚠️  Produit non trouvé dans Meilisearch");
                }

                $this->line(''); // Ligne vide pour séparer
            }

            $this->info('🎉 Test terminé !');
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
