<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Notifications\AdminInvitationNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:email {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tester l\'envoi d\'email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        $this->info("Test d'envoi d'email vers : {$email}");

        try {
            // Test simple avec Mail::raw
            Mail::raw('Ceci est un test d\'email depuis Lorelei Admin', function ($message) use ($email) {
                $message->to($email)
                        ->subject('Test Email Lorelei');
            });

            $this->info('✅ Email de test envoyé avec succès !');

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de l\'envoi : ' . $e->getMessage());
        }
    }
}
