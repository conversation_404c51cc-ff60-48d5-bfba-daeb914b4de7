<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MeilisearchService;

class TestMeilisearchConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meilisearch:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Teste la connexion à Meilisearch';

    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        parent::__construct();
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Test de connexion à Meilisearch...');

        $result = $this->meilisearchService->testConnection();

        if ($result['status'] === 'connected') {
            $this->info('✅ Connexion réussie !');
            $this->line('📍 Host: ' . $result['host']);
            $this->line('🏥 Health: ' . json_encode($result['health']));
            $this->line('📦 Version: ' . json_encode($result['version']));
            
            return Command::SUCCESS;
        } else {
            $this->error('❌ Connexion échouée !');
            $this->line('📍 Host: ' . $result['host']);
            $this->line('💥 Erreur: ' . $result['message']);
            $this->line('🔢 Code: ' . $result['code']);
            
            $this->warn('💡 Vérifications à faire :');
            $this->line('   1. Meilisearch est-il démarré ?');
            $this->line('   2. L\'URL est-elle correcte dans .env ?');
            $this->line('   3. La clé maître est-elle valide ?');
            
            return Command::FAILURE;
        }
    }
}
