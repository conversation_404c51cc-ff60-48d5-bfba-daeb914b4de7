<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Marchand;
use App\Models\BoutiqueReview;

class TestBoutiqueStats extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:boutique-stats {marchand_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Teste les statistiques des reviews de boutique';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $marchandId = $this->argument('marchand_id');
        
        if ($marchandId) {
            $marchand = Marchand::find($marchandId);
            if (!$marchand) {
                $this->error("Marchand {$marchandId} non trouvé");
                return Command::FAILURE;
            }
        } else {
            $marchand = Marchand::first();
            if (!$marchand) {
                $this->error('Aucun marchand trouvé');
                return Command::FAILURE;
            }
        }

        $this->info("🏪 Test des statistiques pour le marchand: {$marchand->nomEntreprise} (ID: {$marchand->id})");

        try {
            // Test 1: Compter les reviews
            $this->info('1️⃣ Comptage des reviews...');
            $totalReviews = $marchand->boutiqueReviews()->count();
            $approvedReviews = $marchand->approvedBoutiqueReviews()->count();
            
            $this->line("   Total reviews: {$totalReviews}");
            $this->line("   Reviews approuvées: {$approvedReviews}");

            if ($approvedReviews === 0) {
                $this->warn('⚠️  Aucune review approuvée trouvée. Créons une review de test...');
                
                // Créer une review de test
                $review = new BoutiqueReview();
                $review->marchand_id = $marchand->id;
                $review->name = 'Test User';
                $review->email = '<EMAIL>';
                $review->rating = 5;
                $review->comment = 'Excellent service de test !';
                $review->is_approved = true;
                $review->is_verified = false;
                $review->ip_address = '127.0.0.1';
                $review->save();
                
                $this->info('✅ Review de test créée');
                
                // Recharger le marchand
                $marchand->refresh();
            }

            // Test 2: Accessors
            $this->info('2️⃣ Test des accessors...');
            $averageRating = $marchand->boutique_average_rating;
            $reviewsCount = $marchand->boutique_reviews_count;
            $ratingStats = $marchand->boutique_rating_stats;

            $this->line("   Average rating: {$averageRating}");
            $this->line("   Reviews count: {$reviewsCount}");
            $this->line("   Rating stats: " . json_encode($ratingStats, JSON_PRETTY_PRINT));

            // Test 3: Distribution des étoiles
            $this->info('3️⃣ Test de la distribution des étoiles...');
            $distribution = $ratingStats['distribution'];
            
            foreach ([5, 4, 3, 2, 1] as $rating) {
                $count = $distribution[$rating];
                $percentage = $ratingStats['total'] > 0 ? round(($count / $ratingStats['total']) * 100, 1) : 0;
                $this->line("   {$rating} étoiles: {$count} reviews ({$percentage}%)");
            }

            // Test 4: API Response simulation
            $this->info('4️⃣ Test de la réponse API...');
            $apiResponse = [
                'average_rating' => $marchand->boutique_average_rating,
                'total_reviews' => $marchand->boutique_reviews_count,
                'rating_stats' => $marchand->boutique_rating_stats,
            ];
            
            $this->line("   API Response: " . json_encode($apiResponse, JSON_PRETTY_PRINT));

            if ($ratingStats['total'] > 0 && !empty($distribution)) {
                $this->info('🎉 Les statistiques fonctionnent correctement !');
            } else {
                $this->warn('⚠️  Problème avec les statistiques');
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur: ' . $e->getMessage());
            $this->line('Stack trace: ' . $e->getTraceAsString());
            return Command::FAILURE;
        }
    }
}
