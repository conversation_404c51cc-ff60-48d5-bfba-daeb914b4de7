<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MeilisearchService;
use Illuminate\Support\Facades\Log;

class InitializeMeilisearch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meilisearch:init
                            {--index-all : Indexe également tous les produits et catégories}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialise Meilisearch avec les index et configurations nécessaires';

    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        parent::__construct();
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Initialisation de Meilisearch...');

        try {
            // Tester la connexion d'abord
            $this->info('🔍 Test de connexion...');
            $connectionTest = $this->meilisearchService->testConnection();

            if ($connectionTest['status'] !== 'connected') {
                $this->error('❌ Impossible de se connecter à Meilisearch');
                $this->line('💥 Erreur: ' . $connectionTest['message']);
                return Command::FAILURE;
            }

            $this->info('✅ Connexion établie');

            // Initialiser les index
            $this->info('🔧 Configuration des index...');
            $this->meilisearchService->initializeIndexes();
            $this->info('✅ Index configurés avec succès');

            // Attendre un peu pour que les index soient prêts
            $this->info('⏳ Attente de la disponibilité des index...');
            sleep(2);

            // Indexer toutes les données si demandé
            if ($this->option('index-all')) {
                $this->info('📦 Indexation de tous les produits...');
                try {
                    $this->meilisearchService->indexAllProduits();
                    $this->info('✅ Produits indexés');
                } catch (\Exception $e) {
                    $this->warn('⚠️  Erreur lors de l\'indexation des produits: ' . $e->getMessage());
                    $this->line('💡 Vous pouvez réessayer avec: php artisan meilisearch:index-products');
                }

                $this->info('📂 Indexation de toutes les catégories...');
                try {
                    $this->meilisearchService->indexAllCategories();
                    $this->info('✅ Catégories indexées');
                } catch (\Exception $e) {
                    $this->warn('⚠️  Erreur lors de l\'indexation des catégories: ' . $e->getMessage());
                    $this->line('💡 Vous pouvez réessayer avec: php artisan meilisearch:index-categories');
                }
            }

            $this->info('🎉 Meilisearch initialisé avec succès !');

            if (!$this->option('index-all')) {
                $this->warn('💡 Pour indexer les données existantes, utilisez :');
                $this->line('   php artisan meilisearch:index-products');
                $this->line('   php artisan meilisearch:index-categories');
                $this->line('   ou utilisez --index-all avec cette commande');
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de l\'initialisation de Meilisearch: ' . $e->getMessage());
            Log::error('Erreur lors de l\'initialisation de Meilisearch', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return Command::FAILURE;
        }
    }
}
