<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MeilisearchService;
use Illuminate\Support\Facades\Log;

class IndexProductsToMeilisearch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meilisearch:index-products 
                            {--fresh : Réinitialise l\'index avant l\'indexation}
                            {--chunk=100 : Nombre de produits à traiter par lot}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Indexe tous les produits dans Meilisearch';

    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        parent::__construct();
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Début de l\'indexation des produits dans Meilisearch...');

        try {
            // Initialiser les index si nécessaire
            if ($this->option('fresh')) {
                $this->info('🔄 Réinitialisation de l\'index des produits...');
                $this->meilisearchService->initializeIndexes();
                $this->info('✅ Index réinitialisé avec succès');
            }

            // Indexer tous les produits
            $this->info('📦 Indexation des produits en cours...');
            $this->meilisearchService->indexAllProduits();
            
            $this->info('✅ Indexation des produits terminée avec succès !');
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de l\'indexation des produits: ' . $e->getMessage());
            Log::error('Erreur lors de l\'indexation des produits', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return Command::FAILURE;
        }
    }
}
