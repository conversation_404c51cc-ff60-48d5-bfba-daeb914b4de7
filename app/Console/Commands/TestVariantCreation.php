<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProductVariant;
use App\Models\Produit;

class TestVariantCreation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:variant-creation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Teste la création d\'un variant de produit';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Test de création d\'un variant de produit');

        try {
            // Récupérer un produit pour le test
            $produit = Produit::first();
            if (!$produit) {
                $this->error('❌ Aucun produit trouvé pour le test');
                return Command::FAILURE;
            }

            $this->line("Produit de test: {$produit->nom} (ID: {$produit->id})");

            // Données du variant
            $variantData = [
                'produit_id' => $produit->id,
                'prix_supplement' => 15.99,
                'stock' => 10,
                'images' => ['variant-test-image.jpg', 'variant-test-image2.jpg'],
                'attributs' => [
                    'couleur' => [
                        'type' => 'couleur',
                        'nom' => 'Rouge Vif',
                        'code' => 'RED',
                        'hex' => '#FF0000',
                        'with_image' => true,
                        'color_image' => 'products/variants/0/colors/red-texture.jpg'
                    ],
                    'taille' => [
                        'type' => 'taille',
                        'nom' => 'Large',
                        'code' => 'L'
                    ]
                ]
            ];

            $this->info('1️⃣ Création du variant...');
            $this->line('   Données: ' . json_encode($variantData, JSON_PRETTY_PRINT));

            // Créer le variant
            $variant = ProductVariant::create($variantData);

            $this->info('✅ Variant créé avec succès !');
            $this->line("   ID: {$variant->id}");
            $this->line("   SKU: {$variant->sku}");
            $this->line("   Prix supplément: {$variant->prix_supplement}");
            $this->line("   Stock: {$variant->stock}");
            $this->line("   Images: " . json_encode($variant->images));
            $this->line("   Attributs: " . json_encode($variant->attributs));

            // Tester les accessors
            $this->info('2️⃣ Test des accessors...');
            $imageUrls = $variant->image_urls;
            $processedAttributs = $variant->processed_attributs;

            $this->line("   Image URLs: " . json_encode($imageUrls));
            $this->line("   Processed Attributs: " . json_encode($processedAttributs, JSON_PRETTY_PRINT));

            // Tester la récupération
            $this->info('3️⃣ Test de récupération...');
            $retrievedVariant = ProductVariant::find($variant->id);
            
            if ($retrievedVariant) {
                $this->info('✅ Variant récupéré avec succès');
                $this->line("   Prix supplément récupéré: {$retrievedVariant->prix_supplement}");
                $this->line("   Stock récupéré: {$retrievedVariant->stock}");
                $this->line("   Images récupérées: " . json_encode($retrievedVariant->images));
            } else {
                $this->error('❌ Impossible de récupérer le variant');
            }

            // Nettoyage
            $this->info('🧹 Nettoyage...');
            $variant->delete();
            $this->info('✅ Variant supprimé');

            $this->info('🎉 Test terminé avec succès !');
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur: ' . $e->getMessage());
            $this->line('Stack trace: ' . $e->getTraceAsString());
            return Command::FAILURE;
        }
    }
}
