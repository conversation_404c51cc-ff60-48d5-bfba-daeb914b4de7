<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Produit;

class DebugDiscount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:discount {id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug les calculs de discount pour un produit';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $id = $this->argument('id');
        $produit = Produit::find($id);

        if (!$produit) {
            $this->error("Produit {$id} non trouvé");
            return Command::FAILURE;
        }

        $this->info("🔍 Debug du produit {$id}");
        $this->line("Nom: " . (is_array($produit->nom) ? json_encode($produit->nom) : $produit->nom));
        $this->line("Prix: {$produit->prix}");
        $this->line("Discount Price: " . ($produit->discount_price ?? 'null'));
        $this->line("Discount Start: " . ($produit->discount_start_date ?? 'null'));
        $this->line("Discount End: " . ($produit->discount_end_date ?? 'null'));

        $now = now();
        $this->line("Heure actuelle: {$now}");

        // Test étape par étape
        $this->info("📋 Tests étape par étape:");

        // 1. Vérifier si discount_price existe et est valide
        if (empty($produit->discount_price)) {
            $this->error("❌ discount_price est vide");
            return Command::SUCCESS;
        }

        if ($produit->discount_price >= $produit->prix) {
            $this->error("❌ discount_price ({$produit->discount_price}) >= prix ({$produit->prix})");
            return Command::SUCCESS;
        }

        $this->info("✅ discount_price est valide");

        // 2. Vérifier les dates
        if ($produit->discount_start_date) {
            $this->line("Start date: {$produit->discount_start_date}");
            if ($now->lt($produit->discount_start_date)) {
                $this->error("❌ La promotion n'a pas encore commencé");
                return Command::SUCCESS;
            } else {
                $this->info("✅ La promotion a commencé");
            }
        } else {
            $this->info("✅ Pas de date de début (promotion toujours active)");
        }

        if ($produit->discount_end_date) {
            $this->line("End date: {$produit->discount_end_date}");
            if ($now->gt($produit->discount_end_date)) {
                $this->error("❌ La promotion est expirée");
                return Command::SUCCESS;
            } else {
                $this->info("✅ La promotion n'est pas expirée");
            }
        } else {
            $this->info("✅ Pas de date de fin (promotion toujours active)");
        }

        // 3. Test final
        $isOnDiscount = $produit->is_on_discount;
        $this->line("Résultat final is_on_discount: " . ($isOnDiscount ? 'true' : 'false'));

        if ($isOnDiscount) {
            $this->info("🎉 Le produit devrait être en promotion !");
        } else {
            $this->error("❌ Le produit n'est pas en promotion selon la méthode");
        }

        return Command::SUCCESS;
    }
}
