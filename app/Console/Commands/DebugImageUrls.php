<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Produit;

class DebugImageUrls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:image-urls {id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug les URLs d\'images pour un produit';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $id = $this->argument('id');
        $produit = Produit::with(['categorie', 'marchand', 'reviews'])->find($id);

        if (!$produit) {
            $this->error("Produit {$id} non trouvé");
            return Command::FAILURE;
        }

        $this->info("🔍 Debug des URLs d'images pour le produit {$id}");
        
        // Nom du produit
        $nomProduit = is_array($produit->nom) ? json_encode($produit->nom) : $produit->nom;
        $this->line("Nom: {$nomProduit}");

        // Images brutes
        $this->line("Images brutes: " . json_encode($produit->images));

        // Test des accessors un par un
        $this->info("📋 Test des accessors d'images:");

        try {
            $imageUrls = $produit->image_urls;
            $this->line("✅ image_urls: " . json_encode($imageUrls));
        } catch (\Exception $e) {
            $this->error("❌ image_urls: " . $e->getMessage());
        }

        try {
            $mainImageUrls = $produit->main_image_urls;
            $this->line("✅ main_image_urls: " . json_encode($mainImageUrls));
        } catch (\Exception $e) {
            $this->error("❌ main_image_urls: " . $e->getMessage());
        }

        try {
            $additionalImageUrls = $produit->additional_image_urls;
            $this->line("✅ additional_image_urls: " . json_encode($additionalImageUrls));
        } catch (\Exception $e) {
            $this->error("❌ additional_image_urls: " . $e->getMessage());
        }

        try {
            $thumbnailUrls = $produit->thumbnail_urls;
            $this->line("✅ thumbnail_urls: " . json_encode($thumbnailUrls));
        } catch (\Exception $e) {
            $this->error("❌ thumbnail_urls: " . $e->getMessage());
        }

        // Vérifier si les accessors sont dans $appends
        $this->info("📋 Accessors dans \$appends:");
        $appends = $produit->getAppends();
        $this->line("Appends: " . json_encode($appends));

        // Vérifier si les méthodes existent
        $this->info("📋 Méthodes d'accessors:");
        $this->line("getImageUrlsAttribute existe: " . (method_exists($produit, 'getImageUrlsAttribute') ? 'Oui' : 'Non'));
        $this->line("getMainImageUrlsAttribute existe: " . (method_exists($produit, 'getMainImageUrlsAttribute') ? 'Oui' : 'Non'));
        $this->line("getAdditionalImageUrlsAttribute existe: " . (method_exists($produit, 'getAdditionalImageUrlsAttribute') ? 'Oui' : 'Non'));
        $this->line("getThumbnailUrlsAttribute existe: " . (method_exists($produit, 'getThumbnailUrlsAttribute') ? 'Oui' : 'Non'));

        return Command::SUCCESS;
    }
}
