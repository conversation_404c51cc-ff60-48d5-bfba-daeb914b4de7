<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MeilisearchService;
use Illuminate\Support\Facades\Log;

class IndexCategoriesToMeilisearch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meilisearch:index-categories 
                            {--fresh : Réinitialise l\'index avant l\'indexation}
                            {--chunk=100 : Nombre de catégories à traiter par lot}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Indexe toutes les catégories dans Meilisearch';

    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        parent::__construct();
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Début de l\'indexation des catégories dans Meilisearch...');

        try {
            // Initialiser les index si nécessaire
            if ($this->option('fresh')) {
                $this->info('🔄 Réinitialisation de l\'index des catégories...');
                $this->meilisearchService->initializeIndexes();
                $this->info('✅ Index réinitialisé avec succès');
            }

            // Indexer toutes les catégories
            $this->info('📂 Indexation des catégories en cours...');
            $this->meilisearchService->indexAllCategories();
            
            $this->info('✅ Indexation des catégories terminée avec succès !');
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de l\'indexation des catégories: ' . $e->getMessage());
            Log::error('Erreur lors de l\'indexation des catégories', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return Command::FAILURE;
        }
    }
}
