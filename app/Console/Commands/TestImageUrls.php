<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Produit;
use App\Models\Categorie;
use App\Services\MeilisearchService;

class TestImageUrls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meilisearch:test-images';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Teste si les URLs d\'images sont correctement indexées dans Meilisearch';

    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        parent::__construct();
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🖼️  Test des URLs d\'images dans Meilisearch...');

        try {
            // Test avec un produit existant
            $this->info('📦 Test avec un produit existant...');
            $produit = Produit::with(['categorie', 'marchand', 'reviews'])->first();

            if (!$produit) {
                $this->error('❌ Aucun produit trouvé');
                return Command::FAILURE;
            }

            // Gérer le nom multilangue
            $nomProduit = is_array($produit->nom) ? json_encode($produit->nom) : $produit->nom;
            $this->line("   Produit: {$nomProduit}");
            $this->line("   Images brutes: " . ($produit->images ?? 'null'));

            // Tester les accessors
            $this->info('🔍 Test des accessors d\'images...');
            $imageUrls = $produit->image_urls;
            $mainImageUrls = $produit->main_image_urls;
            $additionalImageUrls = $produit->additional_image_urls;
            $thumbnailUrls = $produit->thumbnail_urls;

            $this->line("   Image URLs: " . (is_array($imageUrls) ? implode(', ', $imageUrls) : json_encode($imageUrls)));
            $this->line("   Main Image URLs: " . (is_array($mainImageUrls) ? implode(', ', $mainImageUrls) : json_encode($mainImageUrls)));
            $this->line("   Additional Image URLs: " . (is_array($additionalImageUrls) ? implode(', ', $additionalImageUrls) : json_encode($additionalImageUrls)));
            $this->line("   Thumbnail URLs: " . json_encode($thumbnailUrls));

            // Rechercher dans Meilisearch
            $this->info('🔍 Recherche dans Meilisearch...');
            // Utiliser l'ID pour la recherche au lieu du nom multilangue
            $results = $this->meilisearchService->searchProduits('', ["id = {$produit->id}"]);

            if (empty($results['hits'])) {
                $this->warn('⚠️  Produit non trouvé dans Meilisearch');
                return Command::FAILURE;
            }

            $produitMeili = $results['hits'][0];
            $this->info('✅ Produit trouvé dans Meilisearch !');

            // Vérifier les URLs d'images dans Meilisearch
            $this->info('🖼️  URLs d\'images dans Meilisearch :');
            $this->line("   Images brutes: " . ($produitMeili['images'] ?? 'null'));
            $this->line("   Image URLs: " . json_encode($produitMeili['image_urls'] ?? null));
            $this->line("   Main Image URLs: " . json_encode($produitMeili['main_image_urls'] ?? null));
            $this->line("   Additional Image URLs: " . json_encode($produitMeili['additional_image_urls'] ?? null));
            $this->line("   Thumbnail URLs: " . json_encode($produitMeili['thumbnail_urls'] ?? null));

            // Test avec une catégorie
            $this->info('📂 Test avec une catégorie...');
            $categorie = Categorie::with(['categorieParent', 'produits'])->first();

            if ($categorie) {
                // Gérer le nom multilangue pour les catégories aussi
                $nomCategorie = is_array($categorie->nom) ? json_encode($categorie->nom) : $categorie->nom;
                $this->line("   Catégorie: {$nomCategorie}");
                $this->line("   Image URL brute: " . ($categorie->image_url ?? 'null'));
                $this->line("   Full Image URL: " . ($categorie->full_image_url ?? 'null'));
                $this->line("   Thumbnail URLs: " . json_encode($categorie->thumbnail_urls ?? null));

                // Rechercher dans Meilisearch
                $resultsCategorie = $this->meilisearchService->searchCategories('', ["id = {$categorie->id}"]);

                if (!empty($resultsCategorie['hits'])) {
                    $categorieMeili = $resultsCategorie['hits'][0];
                    $this->info('✅ Catégorie trouvée dans Meilisearch !');
                    $this->line("   Image URL: " . ($categorieMeili['image_url'] ?? 'null'));
                    $this->line("   Full Image URL: " . ($categorieMeili['full_image_url'] ?? 'null'));
                    $this->line("   Thumbnail URLs: " . json_encode($categorieMeili['thumbnail_urls'] ?? null));
                } else {
                    $this->warn('⚠️  Catégorie non trouvée dans Meilisearch');
                }
            }

            $this->info('🎉 Test des images terminé !');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
