<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Produit;
use App\Models\Categorie;
use App\Models\Marchand;
use App\Services\MeilisearchService;
use App\Observers\ProduitObserver;

class TestObserverSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meilisearch:test-observer';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Teste si les observers sont bien enregistrés';

    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        parent::__construct();
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Test des Observers Meilisearch...');

        try {
            // Vérifier si les observers sont enregistrés
            $this->info('📋 Vérification des observers enregistrés...');
            
            $produitObservers = Produit::getObservableEvents();
            $this->line('   Événements observables pour Produit: ' . implode(', ', $produitObservers));

            // Créer un produit et déclencher manuellement l'observer
            $this->info('🆕 Création d\'un produit de test...');
            
            $categorie = Categorie::first();
            $marchand = Marchand::first();
            
            if (!$categorie || !$marchand) {
                $this->error('❌ Aucune catégorie ou marchand trouvé.');
                return Command::FAILURE;
            }

            $produit = new Produit();
            $produit->nom = 'Test Observer ' . now()->format('H:i:s');
            $produit->description = 'Test pour vérifier l\'observer';
            $produit->prix = 99.99;
            $produit->stock = 10;
            $produit->categorie_id = $categorie->id;
            $produit->marchand_id = $marchand->id;
            $produit->product_code = 'TEST-OBS-' . uniqid();
            $produit->marque = 'Test Brand';
            $produit->images = json_encode(['test-image.jpg']);

            $this->line("   Nom: {$produit->nom}");

            // Compter avant
            $resultsBefore = $this->meilisearchService->searchProduits('', [], ['limit' => 1]);
            $countBefore = $resultsBefore['total'];
            $this->line("   Produits indexés avant: {$countBefore}");

            // Sauvegarder (devrait déclencher l'observer)
            $this->info('💾 Sauvegarde du produit...');
            $produit->save();
            $this->info("✅ Produit sauvé avec ID: {$produit->id}");

            // Déclencher manuellement l'observer pour test
            $this->info('🔧 Test manuel de l\'observer...');
            $observer = app(ProduitObserver::class);
            $observer->created($produit);
            $this->info('✅ Observer déclenché manuellement');

            // Attendre et vérifier
            sleep(2);
            $resultsAfter = $this->meilisearchService->searchProduits('', [], ['limit' => 1]);
            $countAfter = $resultsAfter['total'];
            $this->line("   Produits indexés après: {$countAfter}");

            if ($countAfter > $countBefore) {
                $this->info('✅ Observer fonctionne !');
            } else {
                $this->warn('⚠️  Observer ne semble pas fonctionner automatiquement');
            }

            // Rechercher le produit spécifique
            $searchResults = $this->meilisearchService->searchProduits($produit->nom);
            $found = false;
            foreach ($searchResults['hits'] as $hit) {
                if ($hit['id'] == $produit->id) {
                    $found = true;
                    break;
                }
            }

            if ($found) {
                $this->info('✅ Produit trouvé dans Meilisearch !');
            } else {
                $this->warn('⚠️  Produit non trouvé dans Meilisearch');
            }

            // Nettoyage
            $this->info('🧹 Nettoyage...');
            $produit->delete();

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
