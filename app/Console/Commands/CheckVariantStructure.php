<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use App\Models\ProductVariant;

class CheckVariantStructure extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:variant-structure';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vérifie la structure de la table product_variants';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Vérification de la structure de la table product_variants');

        try {
            // Vérifier si la table existe
            if (!Schema::hasTable('product_variants')) {
                $this->error('❌ La table product_variants n\'existe pas');
                return Command::FAILURE;
            }

            // Récupérer les colonnes
            $columns = Schema::getColumnListing('product_variants');
            $this->info('📋 Colonnes de la table:');
            foreach ($columns as $column) {
                $this->line("   - {$column}");
            }

            // Vérifier le modèle
            $this->info('📋 Modèle ProductVariant:');
            $variant = new ProductVariant();
            $fillable = $variant->getFillable();
            $this->line('   Fillable: ' . implode(', ', $fillable));

            $casts = $variant->getCasts();
            $this->line('   Casts: ' . json_encode($casts));

            // Tester la création d'un variant
            $this->info('🧪 Test de création d\'un variant...');
            
            // Récupérer un produit pour le test
            $produit = \App\Models\Produit::first();
            if (!$produit) {
                $this->warn('⚠️  Aucun produit trouvé pour le test');
                return Command::SUCCESS;
            }

            $testData = [
                'produit_id' => $produit->id,
                'sku' => 'TEST-VAR-' . uniqid(),
                'prix_supplement' => 10.50,
                'stock' => 5,
                'images' => ['test-image.jpg'],
                'attributs' => [
                    'couleur' => ['nom' => 'Rouge', 'code' => '#FF0000'],
                    'taille' => ['nom' => 'M', 'code' => 'M']
                ]
            ];

            $this->line('   Données de test: ' . json_encode($testData, JSON_PRETTY_PRINT));

            // Vérifier quels champs sont acceptés
            $acceptedFields = [];
            $rejectedFields = [];

            foreach ($testData as $field => $value) {
                if (in_array($field, $fillable)) {
                    $acceptedFields[$field] = $value;
                } else {
                    $rejectedFields[$field] = $value;
                }
            }

            $this->info('✅ Champs acceptés:');
            foreach ($acceptedFields as $field => $value) {
                $this->line("   - {$field}: " . (is_array($value) ? json_encode($value) : $value));
            }

            if (!empty($rejectedFields)) {
                $this->error('❌ Champs rejetés (pas dans fillable):');
                foreach ($rejectedFields as $field => $value) {
                    $this->line("   - {$field}: " . (is_array($value) ? json_encode($value) : $value));
                }
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
