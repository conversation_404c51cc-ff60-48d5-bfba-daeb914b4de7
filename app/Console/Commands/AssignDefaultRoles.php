<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\AdminRole;
use App\Models\MarchandRole;
use App\Models\AdminUser;
use App\Models\MarchandUser;
use Illuminate\Console\Command;

class AssignDefaultRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'roles:assign-default {--force : Force l\'assignation même si l\'utilisateur a déjà un rôle}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assigner les rôles par défaut aux utilisateurs existants';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $force = $this->option('force');

        $this->info('Assignation des rôles par défaut...');

        $this->assignAdminRoles($force);
        $this->assignMarchandRoles($force);

        $this->info('✅ Assignation des rôles terminée.');

        return Command::SUCCESS;
    }

    private function assignAdminRoles(bool $force): void
    {
        $this->info('📋 Assignation des rôles admin...');
        $super_admin = User::where('email', '<EMAIL>')->first();

        // Récupérer les rôles par défaut
        $superAdminRole = AdminRole::where('slug', 'super_admin')->first();
        $adminRole = AdminRole::where('slug', 'admin')->first();

        if (!$superAdminRole || !$adminRole) {
            $this->error('❌ Rôles admin non trouvés. Exécutez d\'abord: php artisan db:seed --class=RoleSeeder');
            return;
        }

        // Assigner le rôle super_admin aux utilisateurs avec role='super_admin'
        $superAdmins = User::where('role', 'super_admin')
            ->when(!$force, fn($query) => $query->whereDoesntHave('adminUser'))
            ->get();

        foreach ($superAdmins as $user) {
            if ($force && $user->adminUser) {
                $user->adminUser->delete();
            }

            AdminUser::create([
                'user_id' => $user->id,
                'role_id' => $superAdminRole->id,
                'access_level' => 'super_admin',
                'is_active' => true,
                'created_by' => $super_admin->id, // Système
            ]);

            $this->line("✅ Super Admin assigné à: {$user->name} ({$user->email})");
        }

        // Assigner le rôle admin aux utilisateurs avec is_admin=true
        $admins = User::where('is_admin', true)
            ->where('role', '!=', 'super_admin')
            ->when(!$force, fn($query) => $query->whereDoesntHave('adminUser'))
            ->get();

        foreach ($admins as $user) {
            if ($force && $user->adminUser) {
                $user->adminUser->delete();
            }

            AdminUser::create([
                'user_id' => $user->id,
                'role_id' => $adminRole->id,
                'access_level' => 'full',
                'is_active' => true,
                'created_by' => $super_admin->id, // Système
            ]);

            $this->line("✅ Admin assigné à: {$user->name} ({$user->email})");
        }

        $this->info("📊 {$superAdmins->count()} Super Admins et {$admins->count()} Admins assignés.");
    }

    private function assignMarchandRoles(bool $force): void
    {
        $super_admin = User::where('email', '<EMAIL>')->first();
        $this->info('🏪 Assignation des rôles marchand...');

        // Récupérer le rôle propriétaire
        $ownerRole = MarchandRole::where('slug', 'owner')->first();

        if (!$ownerRole) {
            $this->error('❌ Rôle propriétaire non trouvé. Exécutez d\'abord: php artisan db:seed --class=RoleSeeder');
            return;
        }

        // Assigner le rôle propriétaire aux marchands existants
        $marchands = \App\Models\Marchand::with('user')
            ->when(!$force, function($query) {
                return $query->whereDoesntHave('user.marchandUsers');
            })
            ->get();

        foreach ($marchands as $marchand) {
            if (!$marchand->user) {
                $this->warn("⚠️  Marchand sans utilisateur: {$marchand->nomEntreprise}");
                continue;
            }

            if ($force) {
                $marchand->user->marchandUsers()
                    ->where('marchand_id', $marchand->id)
                    ->delete();
            }

            MarchandUser::create([
                'user_id' => $marchand->user->id,
                'marchand_id' => $marchand->id,
                'role_id' => $ownerRole->id,
                'access_level' => 'owner',
                'is_active' => true,
                'invited_by' => $super_admin->id, // Système
            ]);

            $this->line("✅ Propriétaire assigné à: {$marchand->user->name} pour {$marchand->nomEntreprise}");
        }

        $this->info("📊 {$marchands->count()} propriétaires de boutique assignés.");
    }
}
