<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MeilisearchService;
use MeiliSearch\Client;
use MeiliSearch\Exceptions\MeiliSearchException;
use Illuminate\Support\Facades\Log;

class CreateMeilisearchIndexes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meilisearch:create-indexes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Crée les index Meilisearch de base';

    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        parent::__construct();
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Création des index Meilisearch...');

        try {
            // Tester la connexion d'abord
            $this->info('🔍 Test de connexion...');
            $connectionTest = $this->meilisearchService->testConnection();
            
            if ($connectionTest['status'] !== 'connected') {
                $this->error('❌ Impossible de se connecter à Meilisearch');
                $this->line('💥 Erreur: ' . $connectionTest['message']);
                return Command::FAILURE;
            }
            
            $this->info('✅ Connexion établie');

            $config = config('meilisearch');
            $client = new Client($config['host'], $config['key']);

            // Créer l'index des produits
            $this->info('📦 Création de l\'index produits...');
            try {
                $task = $client->createIndex('produits', ['primaryKey' => 'id']);
                $client->waitForTask($task['taskUid']);
                $this->info('✅ Index produits créé');
            } catch (MeiliSearchException $e) {
                if (str_contains($e->getMessage(), 'already exists')) {
                    $this->warn('⚠️  Index produits existe déjà');
                } else {
                    throw $e;
                }
            }

            // Créer l'index des catégories
            $this->info('📂 Création de l\'index catégories...');
            try {
                $task = $client->createIndex('categories', ['primaryKey' => 'id']);
                $client->waitForTask($task['taskUid']);
                $this->info('✅ Index catégories créé');
            } catch (MeiliSearchException $e) {
                if (str_contains($e->getMessage(), 'already exists')) {
                    $this->warn('⚠️  Index catégories existe déjà');
                } else {
                    throw $e;
                }
            }

            $this->info('🎉 Index créés avec succès !');
            $this->warn('💡 Maintenant configurez les index avec: php artisan meilisearch:configure-indexes');
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de la création des index: ' . $e->getMessage());
            Log::error('Erreur lors de la création des index Meilisearch', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return Command::FAILURE;
        }
    }
}
