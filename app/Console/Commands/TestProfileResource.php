<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Marchand;
use App\Models\User;

class TestProfileResource extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:profile-resource';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Teste le ProfileResource pour vérifier les erreurs de closure';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Test du ProfileResource');

        try {
            // Récupérer un marchand pour le test
            $marchand = Marchand::with(['user', 'documents'])->first();
            
            if (!$marchand) {
                $this->error('❌ Aucun marchand trouvé pour le test');
                return Command::FAILURE;
            }

            $this->line("Marchand de test: {$marchand->nomEntreprise} (ID: {$marchand->id})");
            $this->line("Utilisateur: {$marchand->user->email}");

            // Test 1: Vérifier les données de base
            $this->info('1️⃣ Test des données de base...');
            $this->line("   Nom entreprise: {$marchand->nomEntreprise}");
            $this->line("   Slug: " . ($marchand->slug ?? 'null'));
            $this->line("   Statut validation: " . ($marchand->statut_validation ?? 'null'));
            $this->line("   Type business: " . ($marchand->type_business ?? 'null'));

            // Test 2: Vérifier les relations
            $this->info('2️⃣ Test des relations...');
            $this->line("   Utilisateur lié: " . ($marchand->user ? 'Oui' : 'Non'));
            $this->line("   Documents: " . $marchand->documents->count());

            // Test 3: Simuler les closures problématiques
            $this->info('3️⃣ Test des closures...');
            
            // Simuler les données d'état pour les actions
            $testStates = [
                ['view_url' => 'http://example.com/view', 'download_url' => 'http://example.com/download'],
                ['download_url' => 'http://example.com/download'],
                ['view_url' => null, 'download_url' => null],
                null, // Test avec état null
                'invalid_state', // Test avec état invalide
            ];

            foreach ($testStates as $index => $state) {
                $this->line("   Test état {$index}: " . json_encode($state));
                
                // Simuler les closures corrigées
                $viewUrl = is_array($state) ? ($state['view_url'] ?? null) : null;
                $downloadUrl = is_array($state) ? ($state['download_url'] ?? null) : null;
                $viewVisible = is_array($state) && !empty($state['view_url']);
                $downloadVisible = is_array($state) && !empty($state['download_url']);
                
                $this->line("     View URL: " . ($viewUrl ?? 'null'));
                $this->line("     Download URL: " . ($downloadUrl ?? 'null'));
                $this->line("     View visible: " . ($viewVisible ? 'true' : 'false'));
                $this->line("     Download visible: " . ($downloadVisible ? 'true' : 'false'));
            }

            // Test 4: Vérifier les accessors du modèle
            $this->info('4️⃣ Test des accessors...');
            
            try {
                $logoUrl = $marchand->logo_url;
                $this->line("   Logo URL: " . ($logoUrl ?? 'null'));
            } catch (\Exception $e) {
                $this->warn("   Erreur logo URL: " . $e->getMessage());
            }

            try {
                $logoData = $marchand->logo_data;
                $this->line("   Logo data: " . json_encode($logoData));
            } catch (\Exception $e) {
                $this->warn("   Erreur logo data: " . $e->getMessage());
            }

            $this->info('🎉 Test terminé avec succès !');
            $this->info('✅ Le ProfileResource devrait maintenant fonctionner sans erreur de closure');
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur: ' . $e->getMessage());
            $this->line('Stack trace: ' . $e->getTraceAsString());
            return Command::FAILURE;
        }
    }
}
