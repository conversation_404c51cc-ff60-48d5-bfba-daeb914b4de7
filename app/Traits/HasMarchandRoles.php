<?php

namespace App\Traits;

use App\Enums\MarchandPermission;
use App\Models\Marchand;
use App\Models\MarchandRole;
use App\Models\MarchandUser;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HasMarchandRoles
{
    /**
     * Relation avec MarchandUser (peut avoir plusieurs marchands)
     */
    public function marchandUsers(): HasMany
    {
        return $this->hasMany(MarchandUser::class);
    }

    /**
     * Relation avec les marchands via MarchandUser
     */
    public function marchands(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Marchand::class, 'marchand_users')
                    ->withPivot(['role_id', 'permissions', 'access_level', 'is_active', 'last_login_at'])
                    ->withTimestamps();
    }

    /**
     * Vérifier si l'utilisateur est associé à des marchands
     */
    public function isMarchand(): bool
    {
        return $this->marchandUsers()->where('is_active', true)->exists();
    }

    /**
     * Obtenir le marchand principal de l'utilisateur (le premier actif)
     */
    public function getPrimaryMarchand(): ?Marchand
    {
        return $this->marchandUsers()
                    ->where('is_active', true)
                    ->with('marchand')
                    ->first()?->marchand;
    }

    /**
     * Obtenir le MarchandUser pour un marchand spécifique
     */
    public function getMarchandUser(Marchand|int $marchand): ?MarchandUser
    {
        $marchandId = $marchand instanceof Marchand ? $marchand->id : $marchand;
        
        return $this->marchandUsers()
                    ->where('marchand_id', $marchandId)
                    ->where('is_active', true)
                    ->first();
    }

    /**
     * Vérifier si l'utilisateur est propriétaire d'un marchand
     */
    public function isOwnerOf(Marchand|int $marchand): bool
    {
        $marchandUser = $this->getMarchandUser($marchand);
        return $marchandUser?->isOwner() ?? false;
    }

    /**
     * Vérifier si l'utilisateur est propriétaire de son marchand principal
     */
    public function isOwner(?Marchand $marchand = null): bool
    {
        if (!$marchand) {
            $marchand = $this->marchand ?? $this->getPrimaryMarchand();
        }

        if (!$marchand) {
            return false;
        }

        return $this->isOwnerOf($marchand);
    }

    /**
     * Vérifier si l'utilisateur est gestionnaire d'un marchand
     */
    public function isManagerOf(Marchand|int $marchand): bool
    {
        $marchandUser = $this->getMarchandUser($marchand);
        return $marchandUser?->isManager() ?? false;
    }

    /**
     * Vérifier si l'utilisateur a une permission pour un marchand spécifique
     */
    public function hasMarchandPermission(MarchandPermission|string $permission, Marchand|int $marchand): bool
    {
        $marchandUser = $this->getMarchandUser($marchand);
        return $marchandUser?->hasPermission($permission) ?? false;
    }

    /**
     * Obtenir toutes les permissions pour un marchand spécifique
     */
    public function getMarchandPermissions(Marchand|int $marchand): array
    {
        $marchandUser = $this->getMarchandUser($marchand);
        return $marchandUser?->getAllPermissions() ?? [];
    }

    /**
     * Assigner un rôle marchand à l'utilisateur
     */
    public function assignMarchandRole(
        Marchand|int $marchand, 
        MarchandRole|int $role, 
        array $options = []
    ): MarchandUser {
        $marchandId = $marchand instanceof Marchand ? $marchand->id : $marchand;
        $roleId = $role instanceof MarchandRole ? $role->id : $role;

        return $this->marchandUsers()->updateOrCreate(
            [
                'user_id' => $this->id,
                'marchand_id' => $marchandId,
            ],
            array_merge([
                'role_id' => $roleId,
                'is_active' => true,
                'invited_by' => auth()->id(),
            ], $options)
        );
    }

    /**
     * Retirer l'utilisateur d'un marchand
     */
    public function removeFromMarchand(Marchand|int $marchand): bool
    {
        $marchandId = $marchand instanceof Marchand ? $marchand->id : $marchand;
        
        return $this->marchandUsers()
                    ->where('marchand_id', $marchandId)
                    ->delete() > 0;
    }

    /**
     * Inviter l'utilisateur à rejoindre un marchand
     */
    public function inviteToMarchand(
        Marchand|int $marchand, 
        MarchandRole|int $role, 
        User $inviter,
        array $options = []
    ): MarchandUser {
        $marchandId = $marchand instanceof Marchand ? $marchand->id : $marchand;
        $roleId = $role instanceof MarchandRole ? $role->id : $role;

        $marchandUser = $this->marchandUsers()->create(array_merge([
            'marchand_id' => $marchandId,
            'role_id' => $roleId,
            'invited_by' => $inviter->id,
            'is_active' => false, // Sera activé après acceptation
        ], $options));

        // Générer le token d'invitation
        $marchandUser->generateInvitationToken();

        return $marchandUser;
    }

    /**
     * Accepter une invitation pour un marchand
     */
    public function acceptMarchandInvitation(string $token): ?MarchandUser
    {
        $marchandUser = $this->marchandUsers()
                             ->where('invitation_token', $token)
                             ->where('invitation_expires_at', '>', now())
                             ->first();

        if ($marchandUser) {
            $marchandUser->acceptInvitation();
            return $marchandUser;
        }

        return null;
    }

    /**
     * Obtenir les invitations en attente
     */
    public function getPendingMarchandInvitations()
    {
        return $this->marchandUsers()
                    ->pendingInvitations()
                    ->with(['marchand', 'role', 'inviter'])
                    ->get();
    }

    /**
     * Mettre à jour la dernière connexion pour un marchand
     */
    public function updateMarchandLastLogin(Marchand|int $marchand): void
    {
        $marchandUser = $this->getMarchandUser($marchand);
        $marchandUser?->updateLastLogin();
    }

    /**
     * Scope pour les utilisateurs d'un marchand spécifique
     */
    public function scopeForMarchand($query, int $marchandId)
    {
        return $query->whereHas('marchandUsers', function ($q) use ($marchandId) {
            $q->where('marchand_id', $marchandId)
              ->where('is_active', true);
        });
    }

    /**
     * Scope pour les propriétaires de marchands
     */
    public function scopeMarchandOwners($query)
    {
        return $query->whereHas('marchandUsers', function ($q) {
            $q->where('is_active', true)
              ->where('access_level', 'owner');
        });
    }

    /**
     * Scope pour les gestionnaires de marchands
     */
    public function scopeMarchandManagers($query)
    {
        return $query->whereHas('marchandUsers', function ($q) {
            $q->where('is_active', true)
              ->whereIn('access_level', ['owner', 'manager']);
        });
    }
}
