<?php

namespace App\Traits;

use App\Enums\AdminPermission;
use App\Models\AdminRole;
use App\Models\AdminUser;
use Illuminate\Database\Eloquent\Relations\HasOne;

trait HasAdminRoles
{
    /**
     * Relation avec AdminUser
     */
    public function adminUser(): HasOne
    {
        return $this->hasOne(AdminUser::class);
    }

    /**
     * Vérifier si l'utilisateur est un admin
     */
    public function isAdmin(): bool
    {
        return $this->adminUser()->exists() && $this->adminUser->is_active;
    }

    /**
     * Vérifier si l'utilisateur est super admin
     */
    public function isSuperAdmin(): bool
    {
        return $this->isAdmin() && $this->adminUser->isSuperAdmin();
    }

    /**
     * Obtenir le rôle admin de l'utilisateur
     */
    public function getAdminRole(): ?AdminRole
    {
        return $this->adminUser?->role;
    }

    /**
     * Vérifier si l'utilisateur admin a une permission spécifique
     */
    public function hasAdminPermission(AdminPermission|string $permission): bool
    {
        if (!$this->isAdmin()) {
            return false;
        }

        // Vérifier si l'utilisateur a un adminUser
        if (!$this->adminUser) {
            return false;
        }

        return $this->adminUser->hasPermission($permission);
    }

    /**
     * Obtenir toutes les permissions admin de l'utilisateur
     */
    public function getAdminPermissions(): array
    {
        if (!$this->isAdmin()) {
            return [];
        }

        return $this->adminUser->getAllPermissions();
    }

    /**
     * Assigner un rôle admin à l'utilisateur
     */
    public function assignAdminRole(AdminRole|int $role, array $options = []): AdminUser
    {
        $roleId = $role instanceof AdminRole ? $role->id : $role;

        return $this->adminUser()->updateOrCreate(
            ['user_id' => $this->id],
            array_merge([
                'role_id' => $roleId,
                'is_active' => true,
                'created_by' => auth()->id(),
            ], $options)
        );
    }

    /**
     * Retirer le rôle admin de l'utilisateur
     */
    public function removeAdminRole(): bool
    {
        return $this->adminUser()?->delete() ?? false;
    }

    /**
     * Vérifier si l'utilisateur peut supprimer d'autres admins
     */
    public function canDeleteAdmins(): bool
    {
        return $this->hasAdminPermission(AdminPermission::DELETE_ADMINS);
    }

    /**
     * Vérifier si l'utilisateur peut gérer les rôles
     */
    public function canManageRoles(): bool
    {
        return $this->hasAdminPermission(AdminPermission::MANAGE_ROLES);
    }

    /**
     * Vérifier si l'utilisateur peut voir les infos bancaires
     */
    public function canViewBankingInfo(): bool
    {
        return $this->hasAdminPermission(AdminPermission::VIEW_BANKING_INFO);
    }

    /**
     * Obtenir le département de l'admin
     */
    public function getAdminDepartment(): ?string
    {
        return $this->adminUser?->department;
    }

    /**
     * Obtenir le niveau d'accès admin
     */
    public function getAdminAccessLevel(): ?string
    {
        return $this->adminUser?->access_level;
    }

    /**
     * Mettre à jour la dernière connexion admin
     */
    public function updateAdminLastLogin(): void
    {
        $this->adminUser?->updateLastLogin();
    }

    /**
     * Scope pour les utilisateurs admin
     */
    public function scopeAdmins($query)
    {
        return $query->whereHas('adminUser', function ($q) {
            $q->where('is_active', true);
        });
    }

    /**
     * Scope pour les super admins
     */
    public function scopeSuperAdmins($query)
    {
        return $query->whereHas('adminUser', function ($q) {
            $q->where('is_active', true)
              ->where('access_level', 'super_admin');
        });
    }

    /**
     * Scope pour un département admin spécifique
     */
    public function scopeAdminsByDepartment($query, string $department)
    {
        return $query->whereHas('adminUser', function ($q) use ($department) {
            $q->where('is_active', true)
              ->where('department', $department);
        });
    }
}
