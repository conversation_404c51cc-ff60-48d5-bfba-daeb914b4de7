<?php

namespace App\Observers;

use App\Models\Categorie;
use App\Services\MeilisearchService;
use Illuminate\Support\Facades\Log;

class CategorieObserver
{
    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Handle the Categorie "created" event.
     */
    public function created(Categorie $categorie): void
    {
        try {
            // Recharger la catégorie avec ses relations pour s'assurer que les accessors fonctionnent
            $categorieFraiche = Categorie::with(['categorieParent', 'produits'])->find($categorie->id);

            if ($categorieFraiche) {
                $this->meilisearchService->indexCategorie($categorieFraiche);
                Log::info("Catégorie {$categorie->id} ajoutée à l'index Meilisearch après création");
            }
        } catch (\Exception $e) {
            Log::error("Erreur lors de l'indexation de la catégorie {$categorie->id} après création: " . $e->getMessage());
        }
    }

    /**
     * Handle the Categorie "updated" event.
     */
    public function updated(Categorie $categorie): void
    {
        try {
            // Recharger la catégorie avec ses relations pour s'assurer que les accessors fonctionnent
            $categorieFraiche = Categorie::with(['categorieParent', 'produits'])->find($categorie->id);

            if ($categorieFraiche) {
                $this->meilisearchService->indexCategorie($categorieFraiche);
                Log::info("Catégorie {$categorie->id} mise à jour dans l'index Meilisearch");
            }
        } catch (\Exception $e) {
            Log::error("Erreur lors de la mise à jour de la catégorie {$categorie->id} dans Meilisearch: " . $e->getMessage());
        }
    }

    /**
     * Handle the Categorie "deleted" event.
     */
    public function deleted(Categorie $categorie): void
    {
        try {
            $this->meilisearchService->deleteCategorie($categorie->id);
            Log::info("Catégorie {$categorie->id} supprimée de l'index Meilisearch");
        } catch (\Exception $e) {
            Log::error("Erreur lors de la suppression de la catégorie {$categorie->id} de Meilisearch: " . $e->getMessage());
        }
    }

    /**
     * Handle the Categorie "restored" event.
     */
    public function restored(Categorie $categorie): void
    {
        try {
            // Recharger la catégorie avec ses relations pour s'assurer que les accessors fonctionnent
            $categorieFraiche = Categorie::with(['categorieParent', 'produits'])->find($categorie->id);

            if ($categorieFraiche) {
                $this->meilisearchService->indexCategorie($categorieFraiche);
                Log::info("Catégorie {$categorie->id} restaurée dans l'index Meilisearch");
            }
        } catch (\Exception $e) {
            Log::error("Erreur lors de la restauration de la catégorie {$categorie->id} dans Meilisearch: " . $e->getMessage());
        }
    }

    /**
     * Handle the Categorie "force deleted" event.
     */
    public function forceDeleted(Categorie $categorie): void
    {
        try {
            $this->meilisearchService->deleteCategorie($categorie->id);
            Log::info("Catégorie {$categorie->id} définitivement supprimée de l'index Meilisearch");
        } catch (\Exception $e) {
            Log::error("Erreur lors de la suppression définitive de la catégorie {$categorie->id} de Meilisearch: " . $e->getMessage());
        }
    }
}
