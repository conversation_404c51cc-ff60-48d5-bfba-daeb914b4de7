<?php

namespace App\Enums;

enum MarchandPermission: string
{
    // === GESTION DES PRODUITS ===
    case VIEW_PRODUCTS = 'view_products';
    case CREATE_PRODUCTS = 'create_products';
    case EDIT_PRODUCTS = 'edit_products';
    case DELETE_PRODUCTS = 'delete_products';
    case MANAGE_PRODUCTS = 'manage_products';
    case MANAGE_INVENTORY = 'manage_inventory';
    case MANAGE_CATEGORIES = 'manage_categories';

    // === GESTION DES COMMANDES ===
    case VIEW_ORDERS = 'view_orders';
    case MANAGE_ORDERS = 'manage_orders';
    case PROCESS_ORDERS = 'process_orders';
    case MANAGE_SHIPPING = 'manage_shipping';
    case VIEW_ORDER_DETAILS = 'view_order_details';
    case CANCEL_ORDERS = 'cancel_orders';

    // === GESTION FINANCIÈRE ===
    case VIEW_FINANCES = 'view_finances';
    case MANAGE_FINANCES = 'manage_finances';
    case VIEW_PAYMENTS = 'view_payments';
    case MANAGE_SUBSCRIPTIONS = 'manage_subscriptions';
    case VIEW_COMMISSIONS = 'view_commissions';
    case MANAGE_PRICING = 'manage_pricing';

    // === GESTION DE L'ÉQUIPE ===
    case VIEW_TEAM = 'view_team';
    case MANAGE_TEAM = 'manage_team';
    case INVITE_USERS = 'invite_users';
    case REMOVE_USERS = 'remove_users';
    case MANAGE_ROLES = 'manage_roles';

    // === ANALYTICS ET RAPPORTS ===
    case VIEW_ANALYTICS = 'view_analytics';
    case VIEW_REPORTS = 'view_reports';
    case EXPORT_DATA = 'export_data';
    case VIEW_PERFORMANCE = 'view_performance';

    // === SUPPORT ET LITIGES ===
    case VIEW_SUPPORT = 'view_support';
    case MANAGE_DISPUTES = 'manage_disputes';
    case MANAGE_REVIEWS = 'manage_reviews';
    case VIEW_REVIEWS = 'view_reviews';
    case RESPOND_TO_REVIEWS = 'respond_to_reviews';
    case CONTACT_SUPPORT = 'contact_support';

    // === PARAMÈTRES ET CONFIGURATION ===
    case MANAGE_SETTINGS = 'manage_settings';
    case MANAGE_PROFILE = 'manage_profile';
    case MANAGE_NOTIFICATIONS = 'manage_notifications';
    case VIEW_LOGS = 'view_logs';

    // === MARKETING ===
    case MANAGE_PROMOTIONS = 'manage_promotions';
    case MANAGE_COUPONS = 'manage_coupons';
    case VIEW_MARKETING = 'view_marketing';

    // === ACTIONS CRITIQUES ===
    case DELETE_ACCOUNT = 'delete_account';
    case MANAGE_BILLING = 'manage_billing';
    case ACCESS_API = 'access_api';

    /**
     * Obtenir le label lisible de la permission
     */
    public function label(): string
    {
        return match($this) {
            // Produits
            self::VIEW_PRODUCTS => 'Voir les produits',
            self::CREATE_PRODUCTS => 'Créer des produits',
            self::EDIT_PRODUCTS => 'Modifier les produits',
            self::DELETE_PRODUCTS => 'Supprimer les produits',
            self::MANAGE_PRODUCTS => 'Gérer les produits',
            self::MANAGE_INVENTORY => 'Gérer le stock',
            self::MANAGE_CATEGORIES => 'Gérer les catégories',

            // Commandes
            self::VIEW_ORDERS => 'Voir les commandes',
            self::MANAGE_ORDERS => 'Gérer les commandes',
            self::PROCESS_ORDERS => 'Traiter les commandes',
            self::MANAGE_SHIPPING => 'Gérer les expéditions',
            self::VIEW_ORDER_DETAILS => 'Voir les détails des commandes',
            self::CANCEL_ORDERS => 'Annuler les commandes',

            // Finances
            self::VIEW_FINANCES => 'Voir les finances',
            self::MANAGE_FINANCES => 'Gérer les finances',
            self::VIEW_PAYMENTS => 'Voir les paiements',
            self::MANAGE_SUBSCRIPTIONS => 'Gérer les abonnements',
            self::VIEW_COMMISSIONS => 'Voir les commissions',
            self::MANAGE_PRICING => 'Gérer les prix',

            // Équipe
            self::VIEW_TEAM => 'Voir l\'équipe',
            self::MANAGE_TEAM => 'Gérer l\'équipe',
            self::INVITE_USERS => 'Inviter des utilisateurs',
            self::REMOVE_USERS => 'Supprimer des utilisateurs',
            self::MANAGE_ROLES => 'Gérer les rôles',

            // Analytics
            self::VIEW_ANALYTICS => 'Voir les analytics',
            self::VIEW_REPORTS => 'Voir les rapports',
            self::EXPORT_DATA => 'Exporter les données',
            self::VIEW_PERFORMANCE => 'Voir les performances',

            // Support
            self::VIEW_SUPPORT => 'Voir le support',
            self::MANAGE_DISPUTES => 'Gérer les litiges',
            self::MANAGE_REVIEWS => 'Gérer les avis',
            self::VIEW_REVIEWS => 'Voir les avis',
            self::RESPOND_TO_REVIEWS => 'Répondre aux avis',
            self::CONTACT_SUPPORT => 'Contacter le support',

            // Paramètres
            self::MANAGE_SETTINGS => 'Gérer les paramètres',
            self::MANAGE_PROFILE => 'Gérer le profil',
            self::MANAGE_NOTIFICATIONS => 'Gérer les notifications',
            self::VIEW_LOGS => 'Voir les logs',

            // Marketing
            self::MANAGE_PROMOTIONS => 'Gérer les promotions',
            self::MANAGE_COUPONS => 'Gérer les coupons',
            self::VIEW_MARKETING => 'Voir le marketing',

            // Actions critiques
            self::DELETE_ACCOUNT => 'Supprimer le compte',
            self::MANAGE_BILLING => 'Gérer la facturation',
            self::ACCESS_API => 'Accès API',
        };
    }

    /**
     * Obtenir la catégorie de la permission
     */
    public function category(): string
    {
        return match($this) {
            self::VIEW_PRODUCTS, self::CREATE_PRODUCTS, self::EDIT_PRODUCTS,
            self::DELETE_PRODUCTS, self::MANAGE_PRODUCTS, self::MANAGE_INVENTORY,
            self::MANAGE_CATEGORIES => 'Boutique',

            self::VIEW_ORDERS, self::MANAGE_ORDERS, self::PROCESS_ORDERS,
            self::MANAGE_SHIPPING, self::VIEW_ORDER_DETAILS, self::CANCEL_ORDERS => 'Commandes',

            self::VIEW_FINANCES, self::MANAGE_FINANCES, self::VIEW_PAYMENTS,
            self::MANAGE_SUBSCRIPTIONS, self::VIEW_COMMISSIONS, self::MANAGE_PRICING => 'Finances',

            self::VIEW_TEAM, self::MANAGE_TEAM, self::INVITE_USERS,
            self::REMOVE_USERS, self::MANAGE_ROLES => 'Équipe',

            self::VIEW_ANALYTICS, self::VIEW_REPORTS, self::EXPORT_DATA,
            self::VIEW_PERFORMANCE => 'Analytics',

            self::VIEW_SUPPORT, self::MANAGE_DISPUTES, self::MANAGE_REVIEWS,
            self::VIEW_REVIEWS, self::RESPOND_TO_REVIEWS, self::CONTACT_SUPPORT => 'Support',

            self::MANAGE_SETTINGS, self::MANAGE_PROFILE, self::MANAGE_NOTIFICATIONS,
            self::VIEW_LOGS => 'Paramètres',

            self::MANAGE_PROMOTIONS, self::MANAGE_COUPONS, self::VIEW_MARKETING => 'Marketing',

            self::DELETE_ACCOUNT, self::MANAGE_BILLING, self::ACCESS_API => 'Système',
        };
    }

    /**
     * Obtenir toutes les permissions groupées par catégorie
     */
    public static function getGrouped(): array
    {
        $grouped = [];
        foreach (self::cases() as $permission) {
            $category = $permission->category();
            $grouped[$category][] = $permission;
        }
        return $grouped;
    }
}
