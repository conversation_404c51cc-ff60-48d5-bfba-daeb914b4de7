<?php

namespace App\Imports;

use App\Models\Categorie;
use App\Models\Marchand;
use App\Models\Produit;
use App\Services\SpreadsheetImporter;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ProduitImport
{
    /**
     * @var array
     */
    protected $results = [
        'total' => 0,
        'created' => 0,
        'updated' => 0,
        'errors' => [],
        'warnings' => [],
    ];

    /**
     * @var int|null
     */
    protected $marchandId;

    /**
     * Normalise une chaîne pour la comparaison
     *
     * @param string $string La chaîne à normaliser
     * @return string La chaîne normalisée
     */
    protected function normalizeString($string): string
    {
        // Convertir en minuscules
        $string = mb_strtolower(trim($string));

        // Supprimer les espaces multiples
        $string = preg_replace('/\s+/', ' ', $string);

        // Supprimer les caractères spéciaux et les accents
        $string = $this->removeAccents($string);

        return $string;
    }

    /**
     * Supprime les accents d'une chaîne
     *
     * @param string $string La chaîne avec accents
     * @return string La chaîne sans accents
     */
    protected function removeAccents($string): string
    {
        if (!preg_match('/[\x80-\xff]/', $string)) {
            return $string;
        }

        $chars = [
            // Décompositions pour le latin-1 Supplement
            chr(195).chr(128) => 'A', chr(195).chr(129) => 'A',
            chr(195).chr(130) => 'A', chr(195).chr(131) => 'A',
            chr(195).chr(132) => 'A', chr(195).chr(133) => 'A',
            chr(195).chr(135) => 'C', chr(195).chr(136) => 'E',
            chr(195).chr(137) => 'E', chr(195).chr(138) => 'E',
            chr(195).chr(139) => 'E', chr(195).chr(140) => 'I',
            chr(195).chr(141) => 'I', chr(195).chr(142) => 'I',
            chr(195).chr(143) => 'I', chr(195).chr(145) => 'N',
            chr(195).chr(146) => 'O', chr(195).chr(147) => 'O',
            chr(195).chr(148) => 'O', chr(195).chr(149) => 'O',
            chr(195).chr(150) => 'O', chr(195).chr(153) => 'U',
            chr(195).chr(154) => 'U', chr(195).chr(155) => 'U',
            chr(195).chr(156) => 'U', chr(195).chr(157) => 'Y',
            chr(195).chr(159) => 's', chr(195).chr(160) => 'a',
            chr(195).chr(161) => 'a', chr(195).chr(162) => 'a',
            chr(195).chr(163) => 'a', chr(195).chr(164) => 'a',
            chr(195).chr(165) => 'a', chr(195).chr(167) => 'c',
            chr(195).chr(168) => 'e', chr(195).chr(169) => 'e',
            chr(195).chr(170) => 'e', chr(195).chr(171) => 'e',
            chr(195).chr(172) => 'i', chr(195).chr(173) => 'i',
            chr(195).chr(174) => 'i', chr(195).chr(175) => 'i',
            chr(195).chr(177) => 'n', chr(195).chr(178) => 'o',
            chr(195).chr(179) => 'o', chr(195).chr(180) => 'o',
            chr(195).chr(181) => 'o', chr(195).chr(182) => 'o',
            chr(195).chr(185) => 'u', chr(195).chr(186) => 'u',
            chr(195).chr(187) => 'u', chr(195).chr(188) => 'u',
            chr(195).chr(189) => 'y', chr(195).chr(191) => 'y',
        ];

        return strtr($string, $chars);
    }

    /**
     * Constructeur
     *
     * @param int|null $marchandId ID du marchand (si null, utilise le marchand connecté)
     */
    public function __construct($marchandId = null)
    {
        $this->marchandId = $marchandId;
    }

    /**
     * Importe les produits à partir d'un fichier
     *
     * @param string $filePath Chemin du fichier à importer
     * @return void
     */
    public function import(string $filePath)
    {
        // Importer les données du fichier
        $rows = SpreadsheetImporter::import($filePath);

        // Déterminer le marchand_id à utiliser
        $marchandId = $this->marchandId;
        if (!$marchandId && Auth::check() && Auth::user()->marchand) {
            $marchandId = Auth::user()->marchand->id;
        }

        if (!$marchandId) {
            // Si aucun marchand n'est spécifié, utiliser le premier marchand disponible
            $marchand = Marchand::first();
            if ($marchand) {
                $marchandId = $marchand->id;
            } else {
                $this->results['errors'][] = "Aucun marchand disponible pour l'importation";
                return;
            }
        }

        // Charger toutes les catégories pour la recherche par nom
        $categories = Categorie::all();
        $categoriesMap = [];
        $categoriesMapEn = []; // Carte séparée pour les noms en anglais
        $categoriesMapNormalized = []; // Carte pour les noms normalisés

        foreach ($categories as $category) {
            $nom = $category->nom;
            if (is_array($nom)) {
                // Stocker le nom français comme clé principale
                if (isset($nom['fr']) && !empty($nom['fr'])) {
                    $frName = $nom['fr'];
                    $categoriesMap[strtolower($frName)] = $category->id;
                    // Stocker aussi la version normalisée
                    $categoriesMapNormalized[$this->normalizeString($frName)] = $category->id;
                }

                // Stocker le nom anglais dans une carte séparée
                if (isset($nom['en']) && !empty($nom['en'])) {
                    $enName = $nom['en'];
                    $categoriesMapEn[strtolower($enName)] = $category->id;
                    // Stocker aussi la version normalisée
                    $categoriesMapNormalized[$this->normalizeString($enName)] = $category->id;
                }
            } else {
                // Pour les catégories sans traduction
                $categoriesMap[strtolower($nom)] = $category->id;
                // Stocker aussi la version normalisée
                $categoriesMapNormalized[$this->normalizeString($nom)] = $category->id;
            }
        }

        // Créer une carte des catégories par objet pour une recherche plus approfondie
        $categoriesByName = collect();
        foreach ($categories as $category) {
            $nom = $category->nom;
            if (is_array($nom)) {
                if (isset($nom['fr']) && !empty($nom['fr'])) {
                    $categoriesByName[$this->normalizeString($nom['fr'])] = $category;
                }
                if (isset($nom['en']) && !empty($nom['en'])) {
                    $categoriesByName[$this->normalizeString($nom['en'])] = $category;
                }
            } else {
                $categoriesByName[$this->normalizeString($nom)] = $category;
            }
        }

        // Charger tous les produits existants pour la mise à jour
        $existingProducts = Produit::all()->keyBy(function ($product) {
            $nom = $product->nom;
            return is_array($nom) ? ($nom['fr'] ?? '') : $nom;
        });

        foreach ($rows as $index => $row) {
            $this->results['total']++;

            // Trouver la catégorie
            $categorieId = null;
            if (!empty($row['categorie'])) {
                $categorieNom = trim($row['categorie']);
                $categorieNomLower = strtolower($categorieNom);
                $categorieNomNormalized = $this->normalizeString($categorieNom);

                // Chercher d'abord dans les noms normalisés (méthode la plus précise)
                if (isset($categoriesMapNormalized[$categorieNomNormalized])) {
                    $categorieId = $categoriesMapNormalized[$categorieNomNormalized];
                }
                // Puis chercher dans les noms français (priorité)
                elseif (isset($categoriesMap[$categorieNomLower])) {
                    $categorieId = $categoriesMap[$categorieNomLower];
                }
                // Puis chercher dans les noms anglais
                elseif (isset($categoriesMapEn[$categorieNomLower])) {
                    $categorieId = $categoriesMapEn[$categorieNomLower];
                }
                // Recherche approfondie dans la collection d'objets
                elseif ($categoriesByName->has($categorieNomNormalized)) {
                    $categorieId = $categoriesByName->get($categorieNomNormalized)->id;
                }
                // Essayer une recherche plus flexible
                else {
                    // Parcourir toutes les catégories pour une comparaison plus flexible
                    foreach ($categories as $category) {
                        $nom = $category->nom;
                        $existingFrName = is_array($nom) ? ($nom['fr'] ?? '') : $nom;
                        $existingEnName = is_array($nom) ? ($nom['en'] ?? '') : '';

                        // Normaliser les noms existants
                        $existingFrNameNormalized = $this->normalizeString($existingFrName);
                        $existingEnNameNormalized = !empty($existingEnName) ? $this->normalizeString($existingEnName) : '';

                        // Comparer les noms normalisés avec une tolérance pour les petites différences
                        if ($categorieNomNormalized === $existingFrNameNormalized ||
                            $categorieNomNormalized === $existingEnNameNormalized ||
                            // Vérifier si le nom est contenu dans le nom existant ou vice versa
                            (strlen($categorieNomNormalized) > 3 && strpos($existingFrNameNormalized, $categorieNomNormalized) !== false) ||
                            (strlen($existingFrNameNormalized) > 3 && strpos($categorieNomNormalized, $existingFrNameNormalized) !== false) ||
                            (strlen($categorieNomNormalized) > 3 && !empty($existingEnNameNormalized) && strpos($existingEnNameNormalized, $categorieNomNormalized) !== false) ||
                            (strlen($existingEnNameNormalized) > 3 && strpos($categorieNomNormalized, $existingEnNameNormalized) !== false)) {

                            $categorieId = $category->id;

                            // Ajouter un avertissement pour informer l'utilisateur
                            $this->results['warnings'][] = "Ligne " . ($index + 2) . ": Catégorie '{$categorieNom}' associée à '{$existingFrName}' (correspondance approximative)";
                            break;
                        }
                    }
                }

                // Si la catégorie n'est toujours pas trouvée
                if ($categorieId === null) {
                    $this->results['errors'][] = "Ligne " . ($index + 2) . ": Catégorie '{$categorieNom}' introuvable";
                    continue;
                }
            } else {
                $this->results['errors'][] = "Ligne " . ($index + 2) . ": Catégorie non spécifiée";
                continue;
            }

            // Préparer les données pour la création/mise à jour
            $nom = [
                'fr' => $row['nom_fr'],
                'en' => $row['nom_en'] ?? $row['nom_fr'],
            ];

            $description = [
                'fr' => $row['description_fr'] ?? '',
                'en' => $row['description_en'] ?? ($row['description_fr'] ?? ''),
            ];

            $prix = floatval(str_replace(',', '.', $row['prix']));
            $stock = intval($row['stock'] ?? 0);

            // Récupérer le code produit et la marque
            $productCode = $row['product_code'] ?? null;
            $marque = $row['marque'] ?? null;

            // Gérer la devise
            $currency = 'FCFA'; // Valeur par défaut
            if (!empty($row['currency'])) {
                $currencyCode = trim($row['currency']);
                // Convertir en majuscules et valider
                $currencyCode = strtoupper($currencyCode);

                // Vérifier si la devise est valide
                $validCurrencies = ['FCFA', 'EUR', 'USD', 'GBP', 'XAF', 'XOF'];
                if (in_array($currencyCode, $validCurrencies)) {
                    $currency = $currencyCode;
                } else {
                    $this->results['errors'][] = "Ligne " . ($index + 2) . ": Devise '{$row['currency']}' invalide. Devises acceptées: " . implode(', ', $validCurrencies);
                    // Continuer avec la devise par défaut
                }
            }

            // Gérer les remises
            $discountPrice = null;
            $discountStartDate = null;
            $discountEndDate = null;

            if (!empty($row['prix_remise']) && floatval(str_replace(',', '.', $row['prix_remise'])) > 0) {
                $discountPrice = floatval(str_replace(',', '.', $row['prix_remise']));

                if (!empty($row['date_debut_remise'])) {
                    try {
                        $discountStartDate = \Carbon\Carbon::createFromFormat('Y-m-d', $row['date_debut_remise'])->startOfDay();
                    } catch (\Exception) {
                        $this->results['errors'][] = "Ligne " . ($index + 2) . ": Format de date de début de remise invalide (utilisez YYYY-MM-DD)";
                    }
                }

                if (!empty($row['date_fin_remise'])) {
                    try {
                        $discountEndDate = \Carbon\Carbon::createFromFormat('Y-m-d', $row['date_fin_remise'])->endOfDay();
                    } catch (\Exception) {
                        $this->results['errors'][] = "Ligne " . ($index + 2) . ": Format de date de fin de remise invalide (utilisez YYYY-MM-DD)";
                    }
                }
            }

            // Gérer les dimensions et le poids
            $dimensions = [];
            if (!empty($row['longueur']) || !empty($row['largeur']) || !empty($row['hauteur'])) {
                $dimensions = [
                    'longueur' => floatval(str_replace(',', '.', $row['longueur'] ?? 0)),
                    'largeur' => floatval(str_replace(',', '.', $row['largeur'] ?? 0)),
                    'hauteur' => floatval(str_replace(',', '.', $row['hauteur'] ?? 0)),
                ];
            }

            $poids = !empty($row['poids']) ? floatval(str_replace(',', '.', $row['poids'])) : null;

            // Vérifier si le produit existe déjà
            $existingProduct = $existingProducts->get($row['nom_fr']);

            if ($existingProduct) {
                // Mettre à jour le produit existant
                $existingProduct->nom = $nom;
                $existingProduct->description = $description;
                $existingProduct->prix = $prix;
                $existingProduct->currency = $currency;
                $existingProduct->stock = $stock;
                $existingProduct->categorie_id = $categorieId;
                $existingProduct->discount_price = $discountPrice;
                $existingProduct->discount_start_date = $discountStartDate;
                $existingProduct->discount_end_date = $discountEndDate;
                $existingProduct->poids = $poids;
                $existingProduct->dimensions = !empty($dimensions) ? $dimensions : null;
                $existingProduct->product_code = $productCode;
                $existingProduct->marque = $marque;
                $existingProduct->misAJourLe = now();

                $existingProduct->save();
                $this->results['updated']++;
            } else {
                // Créer un nouveau produit
                $product = new Produit();
                $product->marchand_id = $marchandId;
                $product->nom = $nom;
                $product->description = $description;
                $product->prix = $prix;
                $product->currency = $currency;
                $product->stock = $stock;
                $product->categorie_id = $categorieId;
                $product->discount_price = $discountPrice;
                $product->discount_start_date = $discountStartDate;
                $product->discount_end_date = $discountEndDate;
                $product->poids = $poids;
                $product->dimensions = !empty($dimensions) ? $dimensions : null;
                $product->product_code = $productCode;
                $product->marque = $marque;
                $product->creeLe = now();
                $product->images = []; // Tableau vide par défaut

                $product->save();
                $this->results['created']++;
            }
        }
    }

    /**
     * Valide les données d'une ligne
     *
     * @param array $row Données à valider
     * @return array Erreurs de validation (tableau vide si aucune erreur)
     */
    protected function validateRow(array $row): array
    {
        $errors = [];

        // Vérifier les champs obligatoires
        if (empty($row['nom_fr'])) {
            $errors[] = 'Le nom en français est obligatoire';
        } elseif (strlen($row['nom_fr']) > 255) {
            $errors[] = 'Le nom en français ne doit pas dépasser 255 caractères';
        }

        if (!empty($row['nom_en']) && strlen($row['nom_en']) > 255) {
            $errors[] = 'Le nom en anglais ne doit pas dépasser 255 caractères';
        }

        if (empty($row['categorie'])) {
            $errors[] = 'La catégorie est obligatoire';
        }

        if (empty($row['prix'])) {
            $errors[] = 'Le prix est obligatoire';
        } elseif (!is_numeric(str_replace(',', '.', $row['prix']))) {
            $errors[] = 'Le prix doit être un nombre';
        } elseif (floatval(str_replace(',', '.', $row['prix'])) < 0) {
            $errors[] = 'Le prix doit être supérieur ou égal à 0';
        }

        if (!empty($row['stock']) && !is_numeric($row['stock'])) {
            $errors[] = 'Le stock doit être un nombre entier';
        } elseif (!empty($row['stock']) && intval($row['stock']) < 0) {
            $errors[] = 'Le stock doit être supérieur ou égal à 0';
        }

        // Valider la devise
        if (!empty($row['currency'])) {
            $currencyCode = strtoupper(trim($row['currency']));
            $validCurrencies = ['FCFA', 'EUR', 'USD', 'GBP', 'XAF', 'XOF'];
            if (!in_array($currencyCode, $validCurrencies)) {
                $errors[] = "La devise '{$row['currency']}' n'est pas valide. Devises acceptées: " . implode(', ', $validCurrencies);
            }
        }

        return $errors;
    }

    /**
     * Récupérer les résultats de l'importation
     */
    public function getResults(): array
    {
        return $this->results;
    }
}
