<?php

namespace App\Listeners;

use App\Events\MerchantApproved;
use App\Models\MarchandAbonnement;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class GrantMerchantAccess implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(MerchantApproved $event): void
    {
        try {
            $validation = $event->validation;
            $marchand = $event->marchand;

            if (!$marchand) {
                Log::warning('Marchand non trouvé pour accorder l\'accès', [
                    'validation_id' => $validation->id,
                ]);
                return;
            }

            // Créer un abonnement trial par défaut si pas déjà existant
            $existingSubscription = MarchandAbonnement::where('marchand_id', $marchand->id)
                ->where('statut', 'actif')
                ->first();

            if (!$existingSubscription) {
                // Créer un abonnement trial de 14 jours
                $trialSubscription = MarchandAbonnement::create([
                    'marchand_id' => $marchand->id,
                    'type_abonnement' => 'trial',
                    'statut' => 'actif',
                    'date_debut' => now(),
                    'date_fin' => now()->addDays(14),
                    'est_periode_essai' => true,
                    'fin_periode_essai' => now()->addDays(14),
                    // Configuration identique au plan basique
                    'prix_mensuel' => 0,
                    'commission_taux_min' => 4.00,
                    'commission_taux_max' => 8.00,
                    'reduction_logistique' => 5.00,
                    'limite_produits' => null,
                    'limite_commandes_mois' => null,
                    'limite_campagnes_mois' => 0,
                    'acces_analytics_avancees' => false,
                    'acces_support_prioritaire' => true,
                    'acces_gestionnaire_dedie' => false,
                    'acces_ia_predictive' => false,
                    'acces_evenements_exclusifs' => false,
                ]);

                Log::info('Abonnement trial créé pour le marchand approuvé', [
                    'validation_id' => $validation->id,
                    'marchand_id' => $marchand->id,
                    'trial_end_date' => $trialSubscription->fin_periode_essai,
                ]);
            }

            // Mettre à jour le statut du marchand pour s'assurer qu'il est actif
            $marchand->update([
                'statut_validation' => 'valide',
                'date_validation' => now(),
            ]);

            // Mettre à jour l'utilisateur pour s'assurer qu'il a le bon rôle
            $validation->user->update([
                'role' => 'Marchand',
                'is_active' => true,
            ]);

            Log::info('Accès marchand accordé avec succès', [
                'validation_id' => $validation->id,
                'user_id' => $validation->user_id,
                'marchand_id' => $marchand->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'octroi d\'accès marchand', [
                'validation_id' => $event->validation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(MerchantApproved $event, \Throwable $exception): void
    {
        Log::error('Échec définitif de l\'octroi d\'accès marchand', [
            'validation_id' => $event->validation->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
