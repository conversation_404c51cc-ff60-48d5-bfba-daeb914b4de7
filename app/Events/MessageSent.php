<?php

namespace App\Events;

use App\Models\ConversationMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public ConversationMessage $message
    ) {
        $this->message->load(['conversation']);
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('conversation.' . $this->message->conversation_id),
            new PrivateChannel('user.' . $this->message->conversation->client_id),
            new PrivateChannel('marchand.' . $this->message->conversation->marchand_id),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'message.sent';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'message' => [
                'id' => $this->message->id,
                'conversation_id' => $this->message->conversation_id,
                'message' => $this->message->message,
                'auteur_type' => $this->message->auteur_type,
                'auteur_id' => $this->message->auteur_id,
                'auteur_nom' => $this->message->auteur_nom,
                'pieces_jointes' => $this->message->pieces_jointes,
                'created_at' => $this->message->created_at->toISOString(),
            ],
            'conversation' => [
                'id' => $this->message->conversation->id,
                'sujet' => $this->message->conversation->sujet,
                'statut' => $this->message->conversation->statut,
                'date_dernier_message' => $this->message->conversation->date_dernier_message,
                'messages_non_lus_client' => $this->message->conversation->messages_non_lus_client,
                'messages_non_lus_marchand' => $this->message->conversation->messages_non_lus_marchand,
            ],
        ];
    }
}
