<?php

namespace App\Events;

use App\Models\MerchantValidation;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MerchantValidationStarted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public MerchantValidation $validation;
    public int $validatorId;

    /**
     * Create a new event instance.
     */
    public function __construct(MerchantValidation $validation, int $validatorId)
    {
        $this->validation = $validation;
        $this->validatorId = $validatorId;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
