<?php

namespace App\Filament\Traits;

use App\Enums\AdminPermission;
use App\Enums\MarchandPermission;

trait HasPermissionChecks
{
    /**
     * Helper pour vérifier les permissions admin avec fallback pour super admin
     */
    protected static function checkAdminPermission(AdminPermission $permission): bool
    {
        $user = auth()->user();

        // Permettre l'accès aux super admins même sans AdminUser (pour la configuration initiale)
        if ($user?->isSuperAdmin()) {
            return true;
        }

        return $user?->hasAdminPermission($permission) ?? false;
    }

    /**
     * Vérifier les permissions admin avec niveau d'accès et département
     */
    protected static function checkAdminPermissionWithLevel(AdminPermission $permission, string $action = 'view', ?string $department = null): bool
    {
        $user = auth()->user();
        $adminUser = $user?->adminUser;

        // Super admin a tous les droits
        if ($user?->isSuperAdmin()) {
            return true;
        }

        if (!$adminUser || !$adminUser->is_active) {
            return false;
        }

        // Vérifier le département si spécifié
        if ($department && $adminUser->department && $adminUser->department !== $department) {
            return false;
        }

        // Vérifier la permission de base
        if (!$user->hasAdminPermission($permission)) {
            return false;
        }

        // Vérifier le niveau d'accès selon l'action
        return static::checkAccessLevel($adminUser->access_level, $action);
    }

    /**
     * Helper pour vérifier les permissions marchand avec fallback pour propriétaire
     */
    protected static function checkMarchandPermission(MarchandPermission $permission): bool
    {
        $user = auth()->user();
        $marchand = $user?->marchand;

        // Permettre l'accès aux propriétaires de marchand même sans MarchandUser (pour la configuration initiale)
        if ($marchand && $user?->id === $marchand->user_id) {
            return true;
        }

        return $user?->hasMarchandPermission($permission, $marchand) ?? false;
    }

    /**
     * Vérifier si l'utilisateur peut voir les informations bancaires (super admin uniquement)
     */
    protected static function canViewBankingInfo(): bool
    {
        return auth()->user()?->isSuperAdmin() ?? false;
    }

    /**
     * Vérifier si l'utilisateur peut supprimer d'autres admins
     */
    protected static function canDeleteAdmins(): bool
    {
        $user = auth()->user();
        return $user?->isSuperAdmin() ?? false;
    }

    /**
     * Vérifier si l'utilisateur peut créer des utilisateurs admin
     */
    protected static function canCreateAdmins(): bool
    {
        return static::checkAdminPermission(AdminPermission::CREATE_ADMINS);
    }

    /**
     * Vérifier le niveau d'accès selon l'action
     */
    protected static function checkAccessLevel(string $accessLevel, string $action): bool
    {
        return match($action) {
            'view' => in_array($accessLevel, ['read', 'write', 'full', 'super_admin']),
            'create' => in_array($accessLevel, ['write', 'full', 'super_admin']),
            'edit' => in_array($accessLevel, ['write', 'full', 'super_admin']),
            'delete' => in_array($accessLevel, ['full', 'super_admin']),
            'super' => $accessLevel === 'super_admin',
            default => false,
        };
    }

    /**
     * Vérifier les permissions marchand avec niveau d'accès
     */
    protected static function checkMarchandPermissionWithLevel(MarchandPermission $permission, string $action = 'view'): bool
    {
        $user = auth()->user();
        $marchand = $user?->marchand;
        $marchandUser = $user?->marchandUser;

        // Propriétaire de marchand a tous les droits
        if ($marchand && $user?->id === $marchand->user_id) {
            return true;
        }

        if (!$marchandUser || !$marchandUser->is_active) {
            return false;
        }

        // Vérifier la permission de base
        if (!$user->hasMarchandPermission($permission, $marchand)) {
            return false;
        }

        // Vérifier le niveau d'accès selon l'action
        return static::checkMarchandAccessLevel($marchandUser->access_level, $action);
    }

    /**
     * Vérifier le niveau d'accès marchand selon l'action
     */
    protected static function checkMarchandAccessLevel(string $accessLevel, string $action): bool
    {
        return match($action) {
            'view' => in_array($accessLevel, ['read', 'write', 'employee', 'manager', 'owner']),
            'create' => in_array($accessLevel, ['write', 'employee', 'manager', 'owner']),
            'edit' => in_array($accessLevel, ['write', 'manager', 'owner']),
            'delete' => in_array($accessLevel, ['manager', 'owner']),
            'owner' => $accessLevel === 'owner',
            default => false,
        };
    }

    /**
     * Vérifier si l'utilisateur peut inviter des membres d'équipe
     */
    protected static function canInviteTeamMembers(): bool
    {
        return static::checkMarchandPermissionWithLevel(MarchandPermission::INVITE_USERS, 'create');
    }

    // === MÉTHODES SPÉCIFIQUES POUR LES ACTIONS CRUD ADMIN ===

    /**
     * Vérifier si l'utilisateur admin peut créer une resource
     */
    public static function canCreateAdmin(AdminPermission $permission, ?string $department = null): bool
    {
        return static::checkAdminPermissionWithLevel($permission, 'create', $department);
    }

    /**
     * Vérifier si l'utilisateur admin peut éditer une resource
     */
    protected static function canEditAdmin(AdminPermission $permission, ?string $department = null): bool
    {
        return static::checkAdminPermissionWithLevel($permission, 'edit', $department);
    }

    /**
     * Vérifier si l'utilisateur admin peut supprimer une resource
     */
    protected static function canDeleteAdmin(AdminPermission $permission, ?string $department = null): bool
    {
        return static::checkAdminPermissionWithLevel($permission, 'delete', $department);
    }

    /**
     * Vérifier si l'utilisateur admin peut voir une resource
     */
    protected static function canViewAdmin(AdminPermission $permission, ?string $department = null): bool
    {
        return static::checkAdminPermissionWithLevel($permission, 'view', $department);
    }

    // === MÉTHODES SPÉCIFIQUES POUR LES ACTIONS CRUD MARCHAND ===

    /**
     * Vérifier si l'utilisateur marchand peut créer une resource
     */
    protected static function canCreateMarchand(MarchandPermission $permission): bool
    {
        return static::checkMarchandPermissionWithLevel($permission, 'create');
    }

    /**
     * Vérifier si l'utilisateur marchand peut éditer une resource
     */
    protected static function canEditMarchand(MarchandPermission $permission): bool
    {
        return static::checkMarchandPermissionWithLevel($permission, 'edit');
    }

    /**
     * Vérifier si l'utilisateur marchand peut supprimer une resource
     */
    protected static function canDeleteMarchand(MarchandPermission $permission): bool
    {
        return static::checkMarchandPermissionWithLevel($permission, 'delete');
    }

    /**
     * Vérifier si l'utilisateur marchand peut voir une resource
     */
    protected static function canViewMarchand(MarchandPermission $permission): bool
    {
        return static::checkMarchandPermissionWithLevel($permission, 'view');
    }
}
