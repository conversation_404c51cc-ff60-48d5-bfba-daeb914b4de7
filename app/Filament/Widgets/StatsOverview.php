<?php

namespace App\Filament\Widgets;

use App\Models\Produit;
use App\Models\User;
use App\Models\Marchand;
use App\Models\MerchantValidation;
use App\Services\CommandeAdapterService;
use App\Helpers\CurrencyHelper;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\AdminPermission;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    use HasPermissionChecks;

    public static function canView(): bool
    {
        return static::canViewAdmin(AdminPermission::VIEW_ANALYTICS);
    }

    protected function getStats(): array
    {
        $commandeService = new CommandeAdapterService();
        $stats = $commandeService->getStatistiquesAdmin();

        // Statistiques des utilisateurs
        $totalUsers = User::count();
        $totalClients = User::where('role', 'Client')->count();
        $marchandsActifs = Marchand::where('statut_validation', 'valide')->count();
        $marchandsEnAttente = MerchantValidation::where('status', 'EN_ATTENTE_VALIDATION')->count();

        // Statistiques des produits
        $totalProduits = Produit::count();
        $produitsEnRupture = Produit::where('stock', 0)->count();

        // Calculer l'évolution
        $evolutionCommandes = $stats['commandes_mois_dernier'] > 0
            ? round((($stats['commandes_ce_mois'] - $stats['commandes_mois_dernier']) / $stats['commandes_mois_dernier']) * 100, 1)
            : 0;

        $evolutionRevenus = $stats['revenus_mois_dernier'] > 0
            ? round((($stats['revenus_ce_mois'] - $stats['revenus_mois_dernier']) / $stats['revenus_mois_dernier']) * 100, 1)
            : 0;

        return [
            Stat::make('Utilisateurs', $totalUsers)
                ->description($totalClients . ' clients, ' . $marchandsActifs . ' marchands actifs')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),

            Stat::make('Commandes principales', $stats['total_commandes'])
                ->description('Système multi-marchands')
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('success'),

            Stat::make('Commandes ce mois', $stats['commandes_ce_mois'])
                ->description($evolutionCommandes >= 0 ? '+' . $evolutionCommandes . '% vs mois dernier' : $evolutionCommandes . '% vs mois dernier')
                ->descriptionIcon($evolutionCommandes >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($evolutionCommandes >= 0 ? 'success' : 'danger')
                ->chart([
                    $stats['commandes_mois_dernier'],
                    $stats['commandes_ce_mois']
                ])
                ->chartColor($evolutionCommandes >= 0 ? 'success' : 'danger'),

            Stat::make('Revenus ce mois', CurrencyHelper::format($stats['revenus_ce_mois']))
                ->description($evolutionRevenus >= 0 ? '+' . $evolutionRevenus . '% vs mois dernier' : $evolutionRevenus . '% vs mois dernier')
                ->descriptionIcon($evolutionRevenus >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($evolutionRevenus >= 0 ? 'success' : 'danger'),

            Stat::make('Commissions plateforme', CurrencyHelper::format($stats['commission_plateforme_mois']))
                ->description('Ce mois-ci')
                ->descriptionIcon('heroicon-m-calculator')
                ->color('info'),

            Stat::make('Marchands en attente', $marchandsEnAttente)
                ->description('À valider')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('Produits', $totalProduits)
                ->description($produitsEnRupture . ' en rupture de stock')
                ->descriptionIcon('heroicon-m-shopping-bag')
                ->color($produitsEnRupture > 0 ? 'danger' : 'success'),
        ];
    }
}
