<?php

namespace App\Filament\Widgets;

use App\Models\CommandePrincipale;
use App\Services\CommandeAdapterService;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\AdminPermission;
use App\Helpers\CurrencyHelper;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class LatestOrders extends BaseWidget
{
    use HasPermissionChecks;

    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = 2;

    protected static ?string $heading = 'Dernières Commandes Principales';

    public static function canView(): bool
    {
        return static::canViewAdmin(AdminPermission::VIEW_ORDERS, 'operations');
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                CommandePrincipale::query()
                    ->with(['client', 'sousCommandes'])
                    ->latest('date_commande')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('numero_commande')
                    ->label('N° Commande')
                    ->searchable()
                    ->copyable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('client.nom')
                    ->label('Client')
                    ->formatStateUsing(fn (CommandePrincipale $record): string =>
                        ($record->client->prenom ?? '') . ' ' . ($record->client->nom ?? 'Client supprimé')
                    )
                    ->searchable(),

                Tables\Columns\TextColumn::make('nombre_marchands')
                    ->label('Marchands')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('montant_total_ttc')
                    ->label('Montant TTC')
                    ->money(CurrencyHelper::getFilamentCurrencyCode())
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('statut_global')
                    ->colors([
                        'danger' => 'Annulé',
                        'warning' => 'EnAttente',
                        'info' => 'PayementConfirme',
                        'primary' => 'EnTraitement',
                        'success' => 'TotalementLivré',
                    ]),

                Tables\Columns\TextColumn::make('date_commande')
                    ->label('Date')
                    ->dateTime()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('Voir')
                    ->url(fn (CommandePrincipale $record): string => route('filament.admin.resources.commandes.view', ['record' => $record]))
                    ->icon('heroicon-m-eye'),
            ]);
    }
}
