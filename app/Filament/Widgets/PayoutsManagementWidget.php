<?php

namespace App\Filament\Widgets;

use App\Models\Versement;
use App\Models\Marchand;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\AdminPermission;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class PayoutsManagementWidget extends BaseWidget
{
    use HasPermissionChecks;

    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = 4;

    protected static ?string $heading = 'Gestion des Versements';

    public static function canView(): bool
    {
        return static::canViewAdmin(AdminPermission::MANAGE_PAYMENTS);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Versement::query()
                    ->with(['marchand'])
                    ->whereIn('statut', ['EnAttente', 'EnCours'])
                    ->latest('created_at')
                    ->limit(15)
            )
            ->columns([
                Tables\Columns\TextColumn::make('reference_versement')
                    ->label('Référence')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('Référence copiée!')
                    ->copyMessageDuration(1500),
                
                Tables\Columns\TextColumn::make('marchand.nomEntreprise')
                    ->label('Marchand')
                    ->searchable()
                    ->icon('heroicon-m-building-storefront')
                    ->url(fn (Versement $record): string => route('filament.admin.resources.marchands.view', ['record' => $record->marchand_id])),
                
                Tables\Columns\TextColumn::make('montant_brut')
                    ->label('Montant brut')
                    ->money('FCFA')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('frais_transaction')
                    ->label('Frais')
                    ->money('FCFA')
                    ->sortable()
                    ->color('warning'),
                
                Tables\Columns\TextColumn::make('montant_net')
                    ->label('Montant net')
                    ->money('FCFA')
                    ->sortable()
                    ->weight('bold')
                    ->color('success'),
                
                Tables\Columns\BadgeColumn::make('statut')
                    ->label('Statut')
                    ->colors([
                        'warning' => 'EnAttente',
                        'primary' => 'EnCours',
                        'success' => 'Complété',
                        'danger' => 'Échoué',
                    ])
                    ->icons([
                        'heroicon-m-clock' => 'EnAttente',
                        'heroicon-m-arrow-path' => 'EnCours',
                        'heroicon-m-check-circle' => 'Complété',
                        'heroicon-m-x-circle' => 'Échoué',
                    ])
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'EnAttente' => 'En attente',
                        'EnCours' => 'En cours',
                        'Complété' => 'Complété',
                        'Échoué' => 'Échoué',
                        default => $state
                    }),
                
                Tables\Columns\TextColumn::make('methode_versement')
                    ->label('Méthode')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'virement_bancaire' => 'Virement',
                        'orange_money' => 'Orange Money',
                        'mtn_money' => 'MTN Money',
                        'paypal' => 'PayPal',
                        'stripe' => 'Stripe',
                        'wave' => 'Wave',
                        default => $state
                    })
                    ->colors([
                        'primary' => 'virement_bancaire',
                        'warning' => ['orange_money', 'mtn_money'],
                        'info' => ['paypal', 'stripe'],
                        'success' => 'wave',
                    ]),
                
                Tables\Columns\TextColumn::make('nombre_commandes')
                    ->label('Commandes')
                    ->numeric()
                    ->alignCenter()
                    ->badge()
                    ->color('info'),
                
                Tables\Columns\TextColumn::make('date_demande')
                    ->label('Demandé le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('delai_traitement')
                    ->label('Délai')
                    ->state(function (Versement $record): string {
                        $heures = now()->diffInHours($record->date_demande);
                        if ($heures < 24) {
                            return $heures . 'h';
                        }
                        return now()->diffInDays($record->date_demande) . 'j';
                    })
                    ->badge()
                    ->color(function (Versement $record): string {
                        $heures = now()->diffInHours($record->date_demande);
                        if ($heures < 24) return 'success';
                        if ($heures < 72) return 'warning';
                        return 'danger';
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('statut')
                    ->options([
                        'EnAttente' => 'En attente',
                        'EnCours' => 'En cours',
                        'Complété' => 'Complété',
                        'Échoué' => 'Échoué',
                    ]),
                
                Tables\Filters\SelectFilter::make('methode_versement')
                    ->label('Méthode')
                    ->options([
                        'virement_bancaire' => 'Virement bancaire',
                        'orange_money' => 'Orange Money',
                        'mtn_money' => 'MTN Money',
                        'paypal' => 'PayPal',
                        'stripe' => 'Stripe',
                        'wave' => 'Wave',
                    ]),
                
                Tables\Filters\Filter::make('montant_min')
                    ->form([
                        \Filament\Forms\Components\TextInput::make('montant')
                            ->label('Montant minimum')
                            ->numeric()
                            ->suffix('FCFA'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['montant'],
                                fn (Builder $query, $montant): Builder => $query->where('montant_net', '>=', $montant),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('approve')
                    ->label('Approuver')
                    ->icon('heroicon-m-check')
                    ->color('success')
                    ->visible(fn (Versement $record): bool => $record->statut === 'EnAttente')
                    ->requiresConfirmation()
                    ->modalHeading('Approuver le versement')
                    ->modalDescription('Êtes-vous sûr de vouloir approuver ce versement ?')
                    ->action(function (Versement $record) {
                        $record->update([
                            'statut' => 'EnCours',
                            'date_traitement' => now(),
                            'traite_par_admin_id' => auth()->id(),
                        ]);
                        
                        // TODO: Déclencher le processus de versement
                        
                        \Filament\Notifications\Notification::make()
                            ->title('Versement approuvé')
                            ->success()
                            ->send();
                    }),
                
                Tables\Actions\Action::make('reject')
                    ->label('Rejeter')
                    ->icon('heroicon-m-x-mark')
                    ->color('danger')
                    ->visible(fn (Versement $record): bool => $record->statut === 'EnAttente')
                    ->requiresConfirmation()
                    ->modalHeading('Rejeter le versement')
                    ->form([
                        \Filament\Forms\Components\Textarea::make('motif_rejet')
                            ->label('Motif du rejet')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function (Versement $record, array $data) {
                        $record->update([
                            'statut' => 'Échoué',
                            'motif_rejet' => $data['motif_rejet'],
                            'date_traitement' => now(),
                            'traite_par_admin_id' => auth()->id(),
                        ]);
                        
                        \Filament\Notifications\Notification::make()
                            ->title('Versement rejeté')
                            ->warning()
                            ->send();
                    }),
                
                Tables\Actions\Action::make('complete')
                    ->label('Marquer complété')
                    ->icon('heroicon-m-check-circle')
                    ->color('success')
                    ->visible(fn (Versement $record): bool => $record->statut === 'EnCours')
                    ->requiresConfirmation()
                    ->form([
                        \Filament\Forms\Components\TextInput::make('reference_transaction')
                            ->label('Référence de transaction')
                            ->required(),
                        \Filament\Forms\Components\Textarea::make('notes_completion')
                            ->label('Notes')
                            ->rows(2),
                    ])
                    ->action(function (Versement $record, array $data) {
                        $record->update([
                            'statut' => 'Complété',
                            'reference_transaction_externe' => $data['reference_transaction'],
                            'notes_completion' => $data['notes_completion'],
                            'date_completion' => now(),
                            'traite_par_admin_id' => auth()->id(),
                        ]);
                        
                        \Filament\Notifications\Notification::make()
                            ->title('Versement complété')
                            ->success()
                            ->send();
                    }),
                
                Tables\Actions\Action::make('details')
                    ->label('Détails')
                    ->icon('heroicon-m-eye')
                    ->color('primary')
                    ->modalHeading(fn (Versement $record): string => "Versement {$record->reference_versement}")
                    ->modalContent(fn (Versement $record): \Illuminate\Contracts\View\View => view('filament.modals.versement-admin-details', ['versement' => $record]))
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Fermer'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('bulk_approve')
                        ->label('Approuver sélectionnés')
                        ->icon('heroicon-m-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function ($records) {
                            $records->each(function (Versement $record) {
                                if ($record->statut === 'EnAttente') {
                                    $record->update([
                                        'statut' => 'EnCours',
                                        'date_traitement' => now(),
                                        'traite_par_admin_id' => auth()->id(),
                                    ]);
                                }
                            });
                            
                            \Filament\Notifications\Notification::make()
                                ->title('Versements approuvés')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->emptyStateHeading('Aucun versement en attente')
            ->emptyStateDescription('Les versements en attente de traitement apparaîtront ici.')
            ->emptyStateIcon('heroicon-o-banknotes')
            ->defaultSort('created_at', 'desc')
            ->striped()
            ->paginated([15, 25, 50]);
    }
}
