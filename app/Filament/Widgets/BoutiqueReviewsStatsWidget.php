<?php

namespace App\Filament\Widgets;

use App\Models\BoutiqueReview;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BoutiqueReviewsStatsWidget extends BaseWidget
{
    protected static ?int $sort = 4;

    protected function getStats(): array
    {
        $user = Auth::user();

        if (!$user) {
            return [];
        }

        $stats = [];

        // Statistiques pour les admins
        if ($user->adminUser) {
            $totalReviews = BoutiqueReview::count();
            $pendingReviews = BoutiqueReview::where('is_approved', false)->count();
            $reportedReviews = BoutiqueReview::where('is_reported', true)->count();
            $averageRating = BoutiqueReview::where('is_approved', true)->avg('rating');

            $stats = [
                Stat::make('Total des avis', $totalReviews)
                    ->description('Tous les avis boutiques')
                    ->descriptionIcon('heroicon-m-star')
                    ->color('primary'),

                Stat::make('En attente de modération', $pendingReviews)
                    ->description('Avis à valider')
                    ->descriptionIcon('heroicon-m-clock')
                    ->color($pendingReviews > 0 ? 'warning' : 'success'),

                Stat::make('Avis signalés', $reportedReviews)
                    ->description('Nécessitent une attention')
                    ->descriptionIcon('heroicon-m-flag')
                    ->color($reportedReviews > 0 ? 'danger' : 'success'),

                Stat::make('Note moyenne', number_format($averageRating ?? 0, 1) . '/5')
                    ->description('Toutes boutiques confondues')
                    ->descriptionIcon('heroicon-m-star')
                    ->color('info'),
            ];
        }

        // Ce widget est uniquement pour le panel admin
        // Pas de statistiques marchands ici

        return $stats;
    }

    protected function getRatingColor(float $rating): string
    {
        if ($rating >= 4.5) {
            return 'success';
        } elseif ($rating >= 3.5) {
            return 'warning';
        } elseif ($rating >= 2.5) {
            return 'danger';
        } else {
            return 'gray';
        }
    }

    public static function canView(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Super admin peut tout voir
        if ($user->adminUser && $user->adminUser->access_level === 'super_admin') {
            return true;
        }

        // Vérification des permissions admin (seulement pour les non-super admin)
        if ($user->adminUser) {
            return $user->adminUser->hasPermission(\App\Enums\AdminPermission::VIEW_REVIEWS) ||
                   $user->adminUser->hasPermission(\App\Enums\AdminPermission::MODERATE_CONTENT);
        }

        return false;
    }
}
