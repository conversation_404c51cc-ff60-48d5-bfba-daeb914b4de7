<?php

namespace App\Filament\Widgets;

use App\Models\Dispute;
use App\Models\DisputeMessage;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\AdminPermission;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class DisputeManagementWidget extends BaseWidget
{
    use HasPermissionChecks;

    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = 5;

    protected static ?string $heading = '🚨 Gestion des Litiges Clients';

    protected static ?string $description = 'Interface de gestion et résolution des litiges clients avec chat intégré';

    public static function canView(): bool
    {
        return static::canViewAdmin(AdminPermission::MANAGE_DISPUTES);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Dispute::query()
                    ->with(['client', 'marchand', 'commandePrincipale', 'messages' => function($query) {
                        $query->publics()->latest()->limit(1);
                    }])
                    ->enAttenteAdmin()
                    ->latest('created_at')
                    ->limit(20)
            )
            ->columns([
                Tables\Columns\TextColumn::make('numero_litige')
                    ->label('N° Litige')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('Numéro copié!')
                    ->copyMessageDuration(1500)
                    ->weight('bold')
                    ->color('primary'),

                Tables\Columns\TextColumn::make('priorite')
                    ->label('Priorité')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'basse' => 'gray',
                        'normale' => 'primary',
                        'haute' => 'warning',
                        'critique' => 'danger',
                        default => 'gray'
                    })
                    ->icon(fn (string $state): string => match($state) {
                        'basse' => 'heroicon-m-minus',
                        'normale' => 'heroicon-m-equals',
                        'haute' => 'heroicon-m-plus',
                        'critique' => 'heroicon-m-exclamation-triangle',
                        default => 'heroicon-m-minus'
                    })
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'basse' => 'Basse',
                        'normale' => 'Normale',
                        'haute' => 'Haute',
                        'critique' => 'CRITIQUE',
                        default => ucfirst($state)
                    }),

                Tables\Columns\TextColumn::make('urgent')
                    ->label('🔥')
                    ->getStateUsing(fn (Dispute $record): string => $record->urgent ? 'urgent' : 'normal')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'urgent' => 'danger',
                        'normal' => 'gray',
                        default => 'gray'
                    })
                    ->formatStateUsing(fn (string $state): string => $state === 'urgent' ? 'URGENT' : '')
                    ->placeholder('')
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('type_litige')
                    ->label('Type')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'produit_non_conforme' => 'Non conforme',
                        'produit_defectueux' => 'Défectueux',
                        'livraison_retard' => 'Retard livraison',
                        'livraison_non_recue' => 'Non reçu',
                        'remboursement' => 'Remboursement',
                        'service_client' => 'Service client',
                        'facturation' => 'Facturation',
                        'autre' => 'Autre',
                        default => ucfirst($state)
                    })
                    ->colors([
                        'warning' => ['produit_non_conforme', 'produit_defectueux'],
                        'danger' => ['livraison_retard', 'livraison_non_recue'],
                        'info' => ['remboursement', 'facturation'],
                        'primary' => ['service_client', 'autre'],
                    ]),

                Tables\Columns\TextColumn::make('sujet')
                    ->label('Sujet')
                    ->limit(40)
                    ->tooltip(function (Dispute $record): string {
                        return $record->sujet . "\n\n" . substr($record->description, 0, 200) . '...';
                    }),

                Tables\Columns\TextColumn::make('client.nom')
                    ->label('Client')
                    ->formatStateUsing(function (Dispute $record): string {
                        return $record->client ?
                            $record->client->prenom . ' ' . $record->client->nom :
                            'Client supprimé';
                    })
                    ->icon('heroicon-m-user')
                    ->color('primary'),

                Tables\Columns\TextColumn::make('marchand.nomEntreprise')
                    ->label('Marchand')
                    ->icon('heroicon-m-building-storefront')
                    ->color('warning')
                    ->url(fn (Dispute $record): string =>
                        $record->marchand ?
                        route('filament.admin.resources.marchands.view', ['record' => $record->marchand_id]) :
                        '#'
                    ),

                Tables\Columns\TextColumn::make('statut')
                    ->label('Statut')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'ouvert' => 'warning',
                        'en_cours' => 'primary',
                        'attente_client' => 'info',
                        'attente_marchand' => 'info',
                        'escalade' => 'danger',
                        'resolu' => 'success',
                        'ferme' => 'gray',
                        'annule' => 'gray',
                        default => 'gray'
                    })
                    ->icon(fn (string $state): string => match($state) {
                        'ouvert' => 'heroicon-m-exclamation-circle',
                        'en_cours' => 'heroicon-m-arrow-path',
                        'attente_client' => 'heroicon-m-clock',
                        'attente_marchand' => 'heroicon-m-clock',
                        'escalade' => 'heroicon-m-arrow-trending-up',
                        'resolu' => 'heroicon-m-check-circle',
                        'ferme' => 'heroicon-m-x-circle',
                        'annule' => 'heroicon-m-x-circle',
                        default => 'heroicon-m-minus'
                    })
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'ouvert' => 'Ouvert',
                        'en_cours' => 'En cours',
                        'attente_client' => 'Attente client',
                        'attente_marchand' => 'Attente marchand',
                        'escalade' => 'Escaladé',
                        'resolu' => 'Résolu',
                        'ferme' => 'Fermé',
                        'annule' => 'Annulé',
                        default => ucfirst($state)
                    }),

                Tables\Columns\TextColumn::make('montant_conteste')
                    ->label('Montant')
                    ->money('FCFA')
                    ->sortable()
                    ->color('danger'),

                Tables\Columns\TextColumn::make('delai_restant')
                    ->label('Délai')
                    ->badge()
                    ->color(function (Dispute $record): string {
                        if (!$record->date_limite_reponse) return 'gray';
                        $heures = now()->diffInHours($record->date_limite_reponse, false);
                        if ($heures < 0) return 'danger';
                        if ($heures < 24) return 'warning';
                        return 'success';
                    })
                    ->icon(function (Dispute $record): string {
                        if (!$record->date_limite_reponse) return 'heroicon-m-minus';
                        $heures = now()->diffInHours($record->date_limite_reponse, false);
                        if ($heures < 0) return 'heroicon-m-exclamation-triangle';
                        return 'heroicon-m-clock';
                    }),

                Tables\Columns\TextColumn::make('assigneA.name')
                    ->label('Assigné à')
                    ->placeholder('Non assigné')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ouvert le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\IconColumn::make('messages_non_lus')
                    ->label('💬')
                    ->getStateUsing(function (Dispute $record): bool {
                        return $record->messages()
                            ->publics()
                            ->nonLusPar('admin')
                            ->exists();
                    })
                    ->boolean()
                    ->trueIcon('heroicon-s-chat-bubble-left-ellipsis')
                    ->falseIcon('heroicon-o-chat-bubble-left')
                    ->trueColor('danger')
                    ->falseColor('gray')
                    ->tooltip('Messages non lus'),
            ])
            ->filters([
                // Tables\Filters\SelectFilter::make('priorite')
                //     ->options([
                //         'basse' => 'Basse',
                //         'normale' => 'Normale',
                //         'haute' => 'Haute',
                //         'critique' => 'Critique',
                //     ]),

                // Tables\Filters\SelectFilter::make('statut')
                //     ->options([
                //         'ouvert' => 'Ouvert',
                //         'en_cours' => 'En cours',
                //         'attente_client' => 'Attente client',
                //         'attente_marchand' => 'Attente marchand',
                //         'escalade' => 'Escaladé',
                //     ]),

                // Tables\Filters\SelectFilter::make('type_litige')
                //     ->label('Type')
                //     ->options([
                //         'produit_non_conforme' => 'Produit non conforme',
                //         'produit_defectueux' => 'Produit défectueux',
                //         'livraison_retard' => 'Retard de livraison',
                //         'livraison_non_recue' => 'Livraison non reçue',
                //         'remboursement' => 'Remboursement',
                //         'service_client' => 'Service client',
                //         'facturation' => 'Facturation',
                //         'autre' => 'Autre',
                //     ]),

                // Tables\Filters\TernaryFilter::make('urgent')
                //     ->label('Urgent')
                //     ->placeholder('Tous')
                //     ->trueLabel('Urgent uniquement')
                //     ->falseLabel('Non urgent'),

                // Tables\Filters\SelectFilter::make('assigne_a')
                //     ->label('Assigné à')
                //     ->relationship('assigneA', 'name')
                //     ->preload(),

                // Tables\Filters\Filter::make('en_retard')
                //     ->label('En retard')
                //     ->query(fn (Builder $query): Builder =>
                //         $query->where('date_limite_reponse', '<', now())
                //               ->whereNotIn('statut', ['resolu', 'ferme', 'annule'])
                //     )
                //     ->toggle(),
            ])
            ->actions([
                Tables\Actions\Action::make('open_dashboard')
                    ->label('Ouvrir Dashboard')
                    ->icon('heroicon-m-chat-bubble-left-right')
                    ->color('primary')
                    ->url(fn (Dispute $record): string =>
                        route('dashboard.disputes') . '?dispute=' . $record->id
                    )
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('assign')
                    ->label('Assigner')
                    ->icon('heroicon-m-user-plus')
                    ->color('info')
                    ->visible(fn (Dispute $record): bool => !$record->assigne_a)
                    ->form([
                        \Filament\Forms\Components\Select::make('assigne_a')
                            ->label('Assigner à')
                            ->options(\App\Models\User::where('role', 'Admin')
                                ->whereNotNull('name')
                                ->where('name', '!=', '')
                                ->pluck('name', 'id'))
                            ->required(),
                    ])
                    ->action(function (Dispute $record, array $data) {
                        $record->update([
                            'assigne_a' => $data['assigne_a'],
                            'statut' => 'en_cours',
                        ]);

                        \Filament\Notifications\Notification::make()
                            ->title('Litige assigné')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('escalate')
                    ->label('Escalader')
                    ->icon('heroicon-m-arrow-trending-up')
                    ->color('danger')
                    ->visible(fn (Dispute $record): bool => $record->statut !== 'escalade')
                    ->requiresConfirmation()
                    ->modalHeading('Escalader le litige')
                    ->modalDescription('Ce litige sera marqué comme escaladé et nécessitera une intervention de niveau supérieur.')
                    ->action(function (Dispute $record) {
                        $record->update([
                            'statut' => 'escalade',
                            'priorite' => 'critique',
                            'urgent' => true,
                        ]);

                        // Ajouter un message système
                        DisputeMessage::create([
                            'dispute_id' => $record->id,
                            'auteur_type' => 'system',
                            'auteur_nom' => 'Système',
                            'message' => 'Litige escaladé par ' . (Auth::user()->name ?? 'Admin'),
                            'type_message' => 'escalade',
                            'interne' => true,
                        ]);

                        \Filament\Notifications\Notification::make()
                            ->title('Litige escaladé')
                            ->warning()
                            ->send();
                    }),

                Tables\Actions\Action::make('resolve')
                    ->label('Résoudre')
                    ->icon('heroicon-m-check-circle')
                    ->color('success')
                    ->visible(fn (Dispute $record): bool => !in_array($record->statut, ['resolu', 'ferme']))
                    ->form([
                        \Filament\Forms\Components\Textarea::make('resolution_details')
                            ->label('Détails de la résolution')
                            ->required()
                            ->rows(4),
                        \Filament\Forms\Components\TextInput::make('montant_rembourse')
                            ->label('Montant remboursé (FCFA)')
                            ->numeric()
                            ->default(0),
                        \Filament\Forms\Components\TextInput::make('montant_compensation')
                            ->label('Compensation (FCFA)')
                            ->numeric()
                            ->default(0),
                    ])
                    ->action(function (Dispute $record, array $data) {
                        $record->update([
                            'statut' => 'resolu',
                            'resolution_details' => $data['resolution_details'],
                            'montant_rembourse' => $data['montant_rembourse'] ?? 0,
                            'montant_compensation' => $data['montant_compensation'] ?? 0,
                            'date_resolution' => now(),
                            'resolu_par' => Auth::id(),
                        ]);

                        // Ajouter un message de résolution
                        DisputeMessage::create([
                            'dispute_id' => $record->id,
                            'auteur_type' => 'admin',
                            'auteur_id' => Auth::id(),
                            'auteur_nom' => Auth::user()->name ?? 'Admin',
                            'message' => "Litige résolu :\n\n" . $data['resolution_details'],
                            'type_message' => 'resolution',
                            'interne' => false,
                        ]);

                        \Filament\Notifications\Notification::make()
                            ->title('Litige résolu')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('bulk_assign')
                        ->label('Assigner en masse')
                        ->icon('heroicon-m-user-group')
                        ->color('info')
                        ->form([
                            \Filament\Forms\Components\Select::make('assigne_a')
                                ->label('Assigner à')
                                ->options(\App\Models\User::where('role', 'Admin')
                                    ->whereNotNull('name')
                                    ->where('name', '!=', '')
                                    ->pluck('name', 'id'))
                                ->required(),
                        ])
                        ->action(function ($records, array $data) {
                            $records->each(function (Dispute $record) use ($data) {
                                $record->update([
                                    'assigne_a' => $data['assigne_a'],
                                    'statut' => 'en_cours',
                                ]);
                            });

                            \Filament\Notifications\Notification::make()
                                ->title('Litiges assignés')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->emptyStateHeading('🎉 Aucun litige en attente')
            ->emptyStateDescription('Tous les litiges sont traités ! Excellent travail de l\'équipe support.')
            ->emptyStateIcon('heroicon-o-face-smile')
            ->defaultSort('created_at', 'desc')
            ->striped()
            ->paginated([20, 50, 100])
            ->poll('30s'); // Actualisation automatique toutes les 30 secondes
    }
}
