<?php

namespace App\Filament\Widgets;

use App\Models\Marchand;
use App\Services\DashboardStatsService;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\AdminPermission;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Collection;

class TopMarchandsWidget extends BaseWidget
{
    use HasPermissionChecks;

    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = 2;

    protected static ?string $heading = 'Top Marchands ce mois';

    protected static ?string $model = Marchand::class;

    public static function canView(): bool
    {
        return static::canViewAdmin(AdminPermission::VIEW_MERCHANTS);
    }

    public function table(Table $table): Table
    {
        // Supprimez l'appel à DashboardStatsService ici, car getTableRecords() le fait.
        // La logique de récupération des données doit être unique et gérée par getTableRecords().

        return $table
            ->query(
                // CORRECTION ICI: Utilisez une requête Eloquent Builder valide et factice.
                // Cela satisfait le type attendu par Filament.
                // Remplacez 'App\Models\User' par n'importe quel modèle Eloquent existant dans votre application.
                // La condition `where('id', null)` s'assure qu'aucun enregistrement n'est réellement récupéré par cette query.
                Marchand::query()->where('id', null)
            )
            ->columns([
                Tables\Columns\TextColumn::make('rank')
                    ->label('#')
                    ->state(function ($record, $rowLoop): int {
                        // $record est requis par Filament même si non utilisé
                        unset($record); // Éviter l'avertissement IDE
                        return $rowLoop->iteration;
                    })
                    ->alignCenter()
                    ->badge()
                    ->color(fn ($state): string => match(true) {
                        $state === 1 => 'warning',
                        $state === 2 => 'gray',
                        $state === 3 => 'orange',
                        default => 'primary'
                    }),

                Tables\Columns\TextColumn::make('nomEntreprise')
                    ->label('Marchand')
                    ->searchable()
                    ->weight('bold')
                    ->icon('heroicon-m-building-storefront'),

                Tables\Columns\TextColumn::make('nombre_commandes')
                    ->label('Commandes')
                    ->numeric()
                    ->alignCenter()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('revenus_total')
                    ->label('Revenus')
                    ->money('FCFA')
                    ->sortable()
                    ->weight('bold')
                    ->color('success'),

                Tables\Columns\TextColumn::make('commission_estimee')
                    ->label('Commission estimée')
                    ->state(function ($record): float {
                        // Estimation à 5% de commission moyenne
                        return $record->revenus_total * $record->getTauxCommission() / 100;
                    })
                    ->money('FCFA')
                    ->color('info'),

                Tables\Columns\TextColumn::make('performance')
                    ->label('Performance')
                    ->state(function ($record): string {
                        $moyenne = $record->revenus_total / max($record->nombre_commandes, 1);
                        return number_format($moyenne, 0, ',', ' ') . ' FCFA/commande';
                    })
                    ->color('gray'),
            ])
            ->actions([
                Tables\Actions\Action::make('view_details')
                    ->label('Voir détails')
                    ->icon('heroicon-m-eye')
                    ->color('primary')
                    ->url(function ($record): string {
                        // Vérifier si c'est un objet stdClass ou un modèle
                        $marchandId = is_object($record) && property_exists($record, 'marchand_id')
                            ? $record->marchand_id
                            : (is_array($record) ? $record['marchand_id'] : $record);

                        return route('filament.admin.resources.marchands.view', ['record' => $marchandId]);
                    }),

                Tables\Actions\Action::make('view_orders')
                    ->label('Commandes')
                    ->icon('heroicon-m-shopping-cart')
                    ->color('info')
                    ->url(function ($record): string {
                        // Vérifier si c'est un objet stdClass ou un modèle
                        $marchandId = is_object($record) && property_exists($record, 'marchand_id')
                            ? $record->marchand_id
                            : (is_array($record) ? $record['marchand_id'] : $record);

                        return route('filament.admin.resources.commandes.index', [
                            'tableFilters[marchand_id][value]' => $marchandId
                        ]);
                    }),
            ])
            ->emptyStateHeading('Aucune donnée')
            ->emptyStateDescription('Les statistiques des marchands apparaîtront ici.')
            ->emptyStateIcon('heroicon-o-chart-bar')
            ->defaultSort('revenus_total', 'desc')
            ->paginated(false);

        }

    /**
     * Override pour injecter les données réelles
     *
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Contracts\Pagination\Paginator|\Illuminate\Contracts\Pagination\CursorPaginator
     */
    public function getTableRecords(): Collection // Le type de retour est maintenant Illuminate\Database\Eloquent\Collection
    {
        $dashboardService = new DashboardStatsService();
        $topMarchands = $dashboardService->getTopMarchands(
            now()->startOfMonth(),
            now()->endOfMonth(),
            10
        );
        $marchandsCollection = new Collection();
        foreach ($topMarchands as $data) {
            $marchand = Marchand::find($data->marchand_id);
            if ($marchand) {
                $marchand->nombre_commandes = $data->nombre_commandes;
                $marchand->revenus_total = $data->revenus_total;
                $marchandsCollection->add($marchand);
            }
        }

        return $marchandsCollection;
    }
}
