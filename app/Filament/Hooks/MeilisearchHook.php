<?php

namespace App\Filament\Hooks;

use App\Models\Produit;
use App\Models\Categorie;
use App\Services\MeilisearchService;
use Illuminate\Support\Facades\Log;

class MeilisearchHook
{
    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Hook appelé après la sauvegarde d'un produit dans Filament
     */
    public function afterSaveProduit(Produit $produit): void
    {
        try {
            // Recharger le produit avec ses relations
            $produitFrais = Produit::with(['categorie', 'marchand', 'reviews'])->find($produit->id);
            
            if ($produitFrais) {
                $this->meilisearchService->indexProduit($produitFrais);
                Log::info("Produit {$produit->id} réindexé dans Meilisearch via Filament hook");
            }
        } catch (\Exception $e) {
            Log::error("Erreur lors de la réindexation du produit {$produit->id} via Filament hook: " . $e->getMessage());
        }
    }

    /**
     * Hook appelé après la suppression d'un produit dans Filament
     */
    public function afterDeleteProduit(Produit $produit): void
    {
        try {
            $this->meilisearchService->removeProduit($produit->id);
            Log::info("Produit {$produit->id} supprimé de Meilisearch via Filament hook");
        } catch (\Exception $e) {
            Log::error("Erreur lors de la suppression du produit {$produit->id} de Meilisearch via Filament hook: " . $e->getMessage());
        }
    }

    /**
     * Hook appelé après la sauvegarde d'une catégorie dans Filament
     */
    public function afterSaveCategorie(Categorie $categorie): void
    {
        try {
            // Recharger la catégorie avec ses relations
            $categorieFraiche = Categorie::with(['categorieParent', 'produits'])->find($categorie->id);
            
            if ($categorieFraiche) {
                $this->meilisearchService->indexCategorie($categorieFraiche);
                Log::info("Catégorie {$categorie->id} réindexée dans Meilisearch via Filament hook");
            }
        } catch (\Exception $e) {
            Log::error("Erreur lors de la réindexation de la catégorie {$categorie->id} via Filament hook: " . $e->getMessage());
        }
    }

    /**
     * Hook appelé après la suppression d'une catégorie dans Filament
     */
    public function afterDeleteCategorie(Categorie $categorie): void
    {
        try {
            $this->meilisearchService->removeCategorie($categorie->id);
            Log::info("Catégorie {$categorie->id} supprimée de Meilisearch via Filament hook");
        } catch (\Exception $e) {
            Log::error("Erreur lors de la suppression de la catégorie {$categorie->id} de Meilisearch via Filament hook: " . $e->getMessage());
        }
    }
}
