<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DisputeResource\Pages;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\AdminPermission;
use App\Models\Dispute;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class DisputeResource extends Resource
{
    use HasPermissionChecks;

    protected static ?string $model = Dispute::class;

    protected static ?string $navigationIcon = 'heroicon-o-exclamation-triangle';

    protected static ?string $navigationLabel = 'Litiges';

    protected static ?string $modelLabel = 'Litige';

    protected static ?string $pluralModelLabel = 'Litiges';

    protected static ?string $navigationGroup = 'Support & Service Client';

    protected static ?int $navigationSort = 10;

    public static function canAccess(): bool
    {
        return static::canViewAdmin(AdminPermission::MANAGE_DISPUTES);
    }

    public static function canCreate(): bool
    {
        return static::canCreateAdmin(AdminPermission::MANAGE_DISPUTES);
    }

    public static function canEdit(Model $record): bool
    {
        return static::canEditAdmin(AdminPermission::MANAGE_DISPUTES);
    }

    public static function canDelete(Model $record): bool
    {
        return static::canDeleteAdmin(AdminPermission::MANAGE_DISPUTES);
    }

    public static function canViewAny(): bool
    {
        return static::canViewAdmin(AdminPermission::MANAGE_DISPUTES);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informations du litige')
                    ->schema([
                        Forms\Components\TextInput::make('numero_litige')
                            ->label('Numéro de litige')
                            ->disabled(),
                        
                        Forms\Components\Select::make('type_litige')
                            ->label('Type de litige')
                            ->options([
                                'produit_defectueux' => 'Produit défectueux',
                                'livraison_retard' => 'Retard de livraison',
                                'produit_non_conforme' => 'Produit non conforme',
                                'service_client' => 'Service client',
                                'remboursement' => 'Demande de remboursement',
                                'autre' => 'Autre',
                            ])
                            ->required(),
                        
                        Forms\Components\Select::make('statut')
                            ->label('Statut')
                            ->options([
                                'ouvert' => 'Ouvert',
                                'en_cours' => 'En cours de traitement',
                                'en_attente_client' => 'En attente client',
                                'en_attente_marchand' => 'En attente marchand',
                                'resolu' => 'Résolu',
                                'ferme' => 'Fermé',
                            ])
                            ->required(),
                        
                        Forms\Components\Select::make('priorite')
                            ->label('Priorité')
                            ->options([
                                'basse' => 'Basse',
                                'normale' => 'Normale',
                                'haute' => 'Haute',
                                'urgente' => 'Urgente',
                            ])
                            ->required(),
                    ]),
                
                Forms\Components\Section::make('Détails')
                    ->schema([
                        Forms\Components\Textarea::make('description')
                            ->label('Description du litige')
                            ->rows(4)
                            ->required(),
                        
                        Forms\Components\Textarea::make('resolution')
                            ->label('Résolution')
                            ->rows(3),
                        
                        Forms\Components\Textarea::make('notes_internes')
                            ->label('Notes internes')
                            ->rows(3),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('numero_litige')
                    ->label('Numéro')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('type_litige')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'produit_defectueux' => 'danger',
                        'livraison_retard' => 'warning',
                        'produit_non_conforme' => 'danger',
                        'service_client' => 'info',
                        'remboursement' => 'primary',
                        default => 'gray'
                    }),
                
                Tables\Columns\TextColumn::make('statut')
                    ->label('Statut')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'ouvert' => 'warning',
                        'en_cours' => 'primary',
                        'en_attente_client' => 'gray',
                        'en_attente_marchand' => 'gray',
                        'resolu' => 'success',
                        'ferme' => 'secondary',
                        default => 'gray'
                    }),
                
                Tables\Columns\TextColumn::make('priorite')
                    ->label('Priorité')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'basse' => 'gray',
                        'normale' => 'primary',
                        'haute' => 'warning',
                        'urgente' => 'danger',
                        default => 'gray'
                    }),
                
                Tables\Columns\TextColumn::make('client.user.name')
                    ->label('Client')
                    ->searchable(),
                
                Tables\Columns\TextColumn::make('marchand.nom_boutique')
                    ->label('Marchand')
                    ->searchable(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Créé le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type_litige')
                    ->label('Type')
                    ->options([
                        'produit_defectueux' => 'Produit défectueux',
                        'livraison_retard' => 'Retard de livraison',
                        'produit_non_conforme' => 'Produit non conforme',
                        'service_client' => 'Service client',
                        'remboursement' => 'Demande de remboursement',
                        'autre' => 'Autre',
                    ]),
                
                Tables\Filters\SelectFilter::make('statut')
                    ->options([
                        'ouvert' => 'Ouvert',
                        'en_cours' => 'En cours',
                        'resolu' => 'Résolu',
                        'ferme' => 'Fermé',
                    ]),
                
                Tables\Filters\SelectFilter::make('priorite')
                    ->options([
                        'basse' => 'Basse',
                        'normale' => 'Normale',
                        'haute' => 'Haute',
                        'urgente' => 'Urgente',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('dashboard')
                    ->label('Ouvrir Dashboard Litiges')
                    ->icon('heroicon-m-chat-bubble-left-right')
                    ->color('primary')
                    ->url('/dashboard/disputes')
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDisputes::route('/'),
            'create' => Pages\CreateDispute::route('/create'),
            'view' => Pages\ViewDispute::route('/{record}'),
            'edit' => Pages\EditDispute::route('/{record}/edit'),
        ];
    }
}
