<?php

namespace App\Filament\Resources\MerchantValidationResource\Pages;

use App\Filament\Resources\MerchantValidationResource;
use App\Models\MerchantValidation;
use App\Services\MerchantValidationService;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class ProcessMerchantValidation extends EditRecord
{
    protected static string $resource = MerchantValidationResource::class;

    protected static string $view = 'filament.resources.merchant-validation-resource.pages.process-merchant-validation';

    public function mount(int | string $record): void
    {
        parent::mount($record);

        $this->form->fill([
            'status' => $this->record->status,
            'rejection_reason' => $this->record->rejection_reason,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Décision de validation')
                    ->schema([
                        Forms\Components\Select::make('status')
                            ->label('Décision')
                            ->options([
                                'EN_ATTENTE_VALIDATION' => 'Remettre en attente',
                                'VALIDE' => 'Approuver le marchand',
                                'REJETE' => 'Rejeter la demande',
                            ])
                            ->required()
                            ->live()
                            ->default($this->record->status),
                        
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Raison du rejet')
                            ->required()
                            ->visible(fn (Forms\Get $get) => $get('status') === 'REJETE')
                            ->maxLength(1000)
                            ->rows(4)
                            ->placeholder('Expliquez clairement pourquoi cette demande est rejetée...'),
                    ])
                    ->columns(1),
            ])
            ->statePath('data');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('save')
                ->label('Enregistrer la décision')
                ->icon('heroicon-o-check')
                ->color('success')
                ->action('save')
                ->requiresConfirmation()
                ->modalHeading('Confirmer la décision')
                ->modalDescription(fn () => match($this->data['status'] ?? '') {
                    'VALIDE' => 'Êtes-vous sûr de vouloir approuver ce marchand ? Il recevra un accès complet à son dashboard.',
                    'REJETE' => 'Êtes-vous sûr de vouloir rejeter cette demande ? Le marchand sera notifié.',
                    default => 'Êtes-vous sûr de vouloir enregistrer cette décision ?'
                }),
            
            Actions\Action::make('back')
                ->label('Retour à la liste')
                ->icon('heroicon-o-arrow-left')
                ->color('gray')
                ->url($this->getResource()::getUrl('index')),
        ];
    }

    protected function handleRecordUpdate(\Illuminate\Database\Eloquent\Model $record, array $data): \Illuminate\Database\Eloquent\Model
    {
        /** @var MerchantValidation $validation */
        $validation = $record;

        try {
            $service = new MerchantValidationService();

            switch ($data['status']) {
                case 'VALIDE':
                    $result = $service->approuverMarchand($validation->id, auth()->id());
                    break;

                case 'REJETE':
                    $result = $service->rejeterMarchand(
                        $validation->id,
                        auth()->id(),
                        $data['rejection_reason']
                    );
                    break;

                default:
                    // Remettre en attente
                    $validation->update([
                        'status' => $data['status'],
                        'rejection_reason' => null,
                        'validated_by' => null,
                        'validated_at' => null,
                    ]);
                    $result = ['success' => true, 'message' => 'Statut mis à jour'];
                    break;
            }

            if (!$result['success']) {
                Notification::make()
                    ->title('Erreur')
                    ->body($result['message'] ?? 'Une erreur est survenue')
                    ->danger()
                    ->send();
            }

            return $validation->fresh();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Erreur')
                ->body('Une erreur inattendue est survenue: ' . $e->getMessage())
                ->danger()
                ->send();

            return $validation;
        }
    }

    public function save(bool $shouldRedirect = true, bool $shouldSendSavedNotification = true): void
    {
        $data = $this->form->getState();
        $this->handleRecordUpdate($this->record, $data);

        if ($shouldRedirect) {
            $this->redirect($this->getResource()::getUrl('index'));
        }
    }

    public function getTitle(): string
    {
        /** @var MerchantValidation $validation */
        $validation = $this->record;
        return 'Traiter la demande de ' . $validation->user->name;
    }

    public function getBreadcrumbs(): array
    {
        return [
            $this->getResource()::getUrl('index') => 'Validations marchands',
            '' => 'Traiter la demande',
        ];
    }
}
