<?php

namespace App\Filament\Resources\MerchantValidationResource\Pages;

use App\Filament\Resources\MerchantValidationResource;
use App\Services\MerchantValidationService;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class EditMerchantValidation extends EditRecord
{
    protected static string $resource = MerchantValidationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function afterSave(): void
    {
        // Si le statut a changé vers VALIDE ou REJETE, déclencher les actions appropriées
        if ($this->record->wasChanged('status')) {
            $service = new MerchantValidationService();
            
            if ($this->record->status === 'VALIDE') {
                $result = $service->approuverMarchand($this->record->id, Auth::id());
                
                if ($result['success']) {
                    Notification::make()
                        ->title('Marchand approuvé')
                        ->body('Le marchand a été notifié et peut maintenant accéder à son dashboard.')
                        ->success()
                        ->send();
                }
            } elseif ($this->record->status === 'REJETE' && $this->record->rejection_reason) {
                $result = $service->rejeterMarchand($this->record->id, Auth::id(), $this->record->rejection_reason);
                
                if ($result['success']) {
                    Notification::make()
                        ->title('Marchand rejeté')
                        ->body('Le marchand a été notifié de la raison du rejet.')
                        ->success()
                        ->send();
                }
            }
        }
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Ajouter l'ID du validateur et la date de validation si nécessaire
        if (isset($data['status'])) {
            if ($data['status'] === 'VALIDE') {
                $data['validated_by'] = Auth::id();
                $data['validated_at'] = now();
                $data['rejected_at'] = null;
            } elseif ($data['status'] === 'REJETE') {
                $data['validated_by'] = Auth::id();
                $data['rejected_at'] = now();
                $data['validated_at'] = null;
            }
        }

        return $data;
    }
}
