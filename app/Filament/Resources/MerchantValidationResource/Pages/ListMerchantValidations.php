<?php

namespace App\Filament\Resources\MerchantValidationResource\Pages;

use App\Filament\Resources\MerchantValidationResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListMerchantValidations extends ListRecords
{
    protected static string $resource = MerchantValidationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('Tous')
                ->badge(fn () => $this->getModel()::count()),
            
            'pending' => Tab::make('En attente de validation')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'EN_ATTENTE_VALIDATION'))
                ->badge(fn () => $this->getModel()::where('status', 'EN_ATTENTE_VALIDATION')->count())
                ->badgeColor('warning'),
            
            'approved' => Tab::make('Approuvés')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'VALIDE'))
                ->badge(fn () => $this->getModel()::where('status', 'VALIDE')->count())
                ->badgeColor('success'),
            
            'rejected' => Tab::make('Rejetés')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'REJETE'))
                ->badge(fn () => $this->getModel()::where('status', 'REJETE')->count())
                ->badgeColor('danger'),
            
            'incomplete' => Tab::make('Incomplets')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereIn('status', [
                    'EN_ATTENTE_SOUMISSION',
                    'PERSONAL_INFO_COMPLETED',
                    'BILLING_INFO_COMPLETED',
                    'STORE_INFO_COMPLETED',
                    'INFORMATIONS_SOUMISES',
                    'DOCUMENTS_SOUMIS'
                ]))
                ->badge(fn () => $this->getModel()::whereIn('status', [
                    'EN_ATTENTE_SOUMISSION',
                    'PERSONAL_INFO_COMPLETED',
                    'BILLING_INFO_COMPLETED',
                    'STORE_INFO_COMPLETED',
                    'INFORMATIONS_SOUMISES',
                    'DOCUMENTS_SOUMIS'
                ])->count())
                ->badgeColor('gray'),
        ];
    }

    public function getDefaultActiveTab(): string | int | null
    {
        return 'pending';
    }
}
