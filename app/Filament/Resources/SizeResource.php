<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SizeResource\Pages;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\AdminPermission;
use App\Models\Size;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SizeResource extends Resource
{
    use HasPermissionChecks;

    protected static ?string $model = Size::class;

    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?string $navigationGroup = 'Catalogue & Produits';

    protected static ?int $navigationSort = 4;

    public static function canAccess(): bool
    {
        return static::canViewAdmin(AdminPermission::MANAGE_PRODUCTS, 'operations');
    }

    public static function canCreate(): bool
    {
        return static::canCreateAdmin(AdminPermission::MANAGE_PRODUCTS, 'operations');
    }

    public static function canEdit(Model $record): bool
    {
        return static::canEditAdmin(AdminPermission::MANAGE_PRODUCTS, 'operations');
    }

    public static function canDelete(Model $record): bool
    {
        return static::canDeleteAdmin(AdminPermission::MANAGE_PRODUCTS, 'operations');
    }

    public static function canViewAny(): bool
    {
        return static::canViewAdmin(AdminPermission::MANAGE_PRODUCTS, 'operations');
    }

    public static function getNavigationLabel(): string
    {
        return __('Tailles (Sizes)');
    }

    public static function getModelLabel(): string
    {
        return __('Taille (Size)');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Tailles (Sizes)');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\TextInput::make('name_fr')
                            ->label('Nom (français)')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('name_en')
                            ->label('Nom (anglais)')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('code')
                            ->label('Code')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),

                        Forms\Components\Select::make('category')
                            ->label('Catégorie')
                            ->options([
                                'clothing' => 'Vêtements (Clothing)',
                                'shoes' => 'Chaussures (Shoes)',
                                'accessories' => 'Accessoires (Accessories)',
                                'women-clothing' => 'Vêtements femmes (Women Clothing)',
                                'men-clothing' => 'Vêtements hommes (Men Clothing)',
                                'kids-clothing' => 'Vêtements enfants (Kids Clothing)',
                                'women-shoes' => 'Chaussures femmes (Women Shoes)',
                                'men-shoes' => 'Chaussures hommes (Men Shoes)',
                                'hats' => 'Chapeaux (Hats)',
                                'gloves' => 'Gants (Gloves)',
                                'belts' => 'Ceintures (Belts)',
                                'other' => 'Autre (Other)',
                            ])
                            ->required(),

                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->maxLength(65535)
                            ->columnSpan(2),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Actif')
                            ->default(true),

                        Forms\Components\TextInput::make('order')
                            ->label('Ordre d\'affichage')
                            ->numeric()
                            ->helperText('Ordre d\'affichage de la taille (1, 2, 3, etc.)'),

                        Forms\Components\TextInput::make('foot_length_cm')
                            ->label('Longueur du pied (cm)')
                            ->numeric()
                            ->helperText('Pour les chaussures uniquement')
                            ->visible(fn (Forms\Get $get) => in_array($get('category'), ['shoes', 'women-shoes', 'men-shoes'])),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->label('Code')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('name_fr')
                    ->label('Nom (français)')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('name_en')
                    ->label('Nom (anglais)')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('category')
                    ->label('Catégorie')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'clothing' => 'Vêtements (Clothing)',
                        'shoes' => 'Chaussures (Shoes)',
                        'accessories' => 'Accessoires (Accessories)',
                        'women-clothing' => 'Vêtements femmes (Women Clothing)',
                        'men-clothing' => 'Vêtements hommes (Men Clothing)',
                        'kids-clothing' => 'Vêtements enfants (Kids Clothing)',
                        'women-shoes' => 'Chaussures femmes (Women Shoes)',
                        'men-shoes' => 'Chaussures hommes (Men Shoes)',
                        'hats' => 'Chapeaux (Hats)',
                        'gloves' => 'Gants (Gloves)',
                        'belts' => 'Ceintures (Belts)',
                        'other' => 'Autre (Other)',
                        default => $state,
                    })
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Actif')
                    ->boolean(),

                Tables\Columns\TextColumn::make('order')
                    ->label('Ordre')
                    ->sortable(),

                Tables\Columns\TextColumn::make('foot_length_cm')
                    ->label('Longueur pied (cm)')
                    ->sortable()
                    ->visible(function ($livewire) {
                        $filterState = $livewire->getTableFilterState('category');
                        return $filterState === 'shoes' ||
                               $filterState === 'women-shoes' ||
                               $filterState === 'men-shoes';
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Créé le')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Mis à jour le')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label('Catégorie')
                    ->options([
                        'clothing' => 'Vêtements (Clothing)',
                        'shoes' => 'Chaussures (Shoes)',
                        'accessories' => 'Accessoires (Accessories)',
                        'women-clothing' => 'Vêtements femmes (Women Clothing)',
                        'men-clothing' => 'Vêtements hommes (Men Clothing)',
                        'kids-clothing' => 'Vêtements enfants (Kids Clothing)',
                        'women-shoes' => 'Chaussures femmes (Women Shoes)',
                        'men-shoes' => 'Chaussures hommes (Men Shoes)',
                        'hats' => 'Chapeaux (Hats)',
                        'gloves' => 'Gants (Gloves)',
                        'belts' => 'Ceintures (Belts)',
                        'other' => 'Autre (Other)',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Actif')
                    ->indicator('Actif'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSizes::route('/'),
            'create' => Pages\CreateSize::route('/create'),
            'edit' => Pages\EditSize::route('/{record}/edit'),
        ];
    }
}
