<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SizeGuideResource\Pages;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\AdminPermission;
use App\Models\SizeGuide;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class SizeGuideResource extends Resource
{
    use HasPermissionChecks;

    protected static ?string $model = SizeGuide::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Catalogue & Produits';

    protected static ?int $navigationSort = 5;

    public static function canAccess(): bool
    {
        return static::canViewAdmin(AdminPermission::MANAGE_PRODUCTS, 'operations');
    }

    public static function canCreate(): bool
    {
        return static::canCreateAdmin(AdminPermission::MANAGE_PRODUCTS, 'operations');
    }

    public static function canEdit(Model $record): bool
    {
        return static::canEditAdmin(AdminPermission::MANAGE_PRODUCTS, 'operations');
    }

    public static function canDelete(Model $record): bool
    {
        return static::canDeleteAdmin(AdminPermission::MANAGE_PRODUCTS, 'operations');
    }

    public static function canViewAny(): bool
    {
        return static::canViewAdmin(AdminPermission::MANAGE_PRODUCTS, 'operations');
    }

    public static function getNavigationLabel(): string
    {
        return __('Guides des tailles (Size Guides)');
    }

    public static function getModelLabel(): string
    {
        return __('Guide des tailles (Size Guide)');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Guides des tailles (Size Guides)');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Onglets principaux pour organiser le formulaire
                Forms\Components\Tabs::make('Tabs')
                    ->tabs([
                        // Onglet 1: Informations générales
                        Forms\Components\Tabs\Tab::make('Informations générales')
                            ->icon('heroicon-o-information-circle')
                            ->schema([
                                Forms\Components\Section::make('Identification du guide')
                                    ->description('Informations de base du guide des tailles')
                                    ->schema([
                                        Forms\Components\Grid::make()
                                            ->schema([
                                                Forms\Components\TextInput::make('name_fr')
                                                    ->label('Nom (français)')
                                                    ->required()
                                                    ->maxLength(255),

                                                Forms\Components\TextInput::make('name_en')
                                                    ->label('Nom (anglais)')
                                                    ->required()
                                                    ->maxLength(255),
                                            ])
                                            ->columns(2),

                                        Forms\Components\Grid::make()
                                            ->schema([
                                                Forms\Components\Select::make('category')
                                                    ->label('Catégorie')
                                                    ->options([
                                                        'clothing' => 'Vêtements (Clothing)',
                                                        'shoes' => 'Chaussures (Shoes)',
                                                        'accessories' => 'Accessoires (Accessories)',
                                                        'women-clothing' => 'Vêtements femmes (Women Clothing)',
                                                        'men-clothing' => 'Vêtements hommes (Men Clothing)',
                                                        'kids-clothing' => 'Vêtements enfants (Kids Clothing)',
                                                        'women-shoes' => 'Chaussures femmes (Women Shoes)',
                                                        'men-shoes' => 'Chaussures hommes (Men Shoes)',
                                                        'hats' => 'Chapeaux (Hats)',
                                                        'gloves' => 'Gants (Gloves)',
                                                        'belts' => 'Ceintures (Belts)',
                                                        'other' => 'Autre (Other)',
                                                    ])
                                                    ->required(),

                                                Forms\Components\Select::make('categories')
                                                    ->label('Catégories de produits associées')
                                                    ->relationship('categories', 'nom')
                                                    ->multiple()
                                                    ->preload()
                                                    ->searchable(),
                                            ])
                                            ->columns(2),

                                        Forms\Components\Grid::make()
                                            ->schema([
                                                Forms\Components\FileUpload::make('image')
                                                    ->label('Image d\'illustration')
                                                    ->image()
                                                    ->imageEditor()
                                                    ->directory('size-guides')
                                                    ->visibility('public')
                                                    ->helperText('Image montrant comment prendre les mesures'),

                                                Forms\Components\Toggle::make('is_active')
                                                    ->label('Actif')
                                                    ->default(true)
                                                    ->helperText('Activer ou désactiver ce guide des tailles'),
                                            ])
                                            ->columns(2),
                                    ]),

                                Forms\Components\Section::make('Instructions de mesure')
                                    ->description('Instructions détaillées pour prendre les mesures')
                                    ->schema([
                                        Forms\Components\Tabs::make('Instructions')
                                            ->tabs([
                                                Forms\Components\Tabs\Tab::make('Français')
                                                    ->schema([
                                                        Forms\Components\RichEditor::make('instructions_fr')
                                                            ->label('Instructions de mesure (français)')
                                                            ->toolbarButtons([
                                                                'bold', 'italic', 'underline', 'strike', 'link', 'redo', 'undo',
                                                                'bulletList', 'orderedList',
                                                            ])
                                                            ->required(),
                                                    ]),

                                                Forms\Components\Tabs\Tab::make('English')
                                                    ->schema([
                                                        Forms\Components\RichEditor::make('instructions_en')
                                                            ->label('Instructions de mesure (anglais)')
                                                            ->toolbarButtons([
                                                                'bold', 'italic', 'underline', 'strike', 'link', 'redo', 'undo',
                                                                'bulletList', 'orderedList',
                                                            ])
                                                            ->required(),
                                                    ]),
                                            ]),
                                    ]),
                            ]),

                        // Onglet 2: Types de mesures
                        Forms\Components\Tabs\Tab::make('Types de mesures')
                            ->icon('heroicon-o-variable')
                            ->schema([
                                Forms\Components\Section::make('Types de mesures')
                                    ->description('Définir les différents types de mesures (tour de poitrine, tour de taille, etc.)')
                                    ->schema([
                                        Forms\Components\Repeater::make('measurement_types')
                                            ->label('Types de mesures')
                                            ->schema([
                                                Forms\Components\Grid::make()
                                                    ->schema([
                                                        Forms\Components\TextInput::make('code')
                                                            ->label('Code')
                                                            ->required()
                                                            ->helperText('Code unique pour ce type de mesure (ex: chest, waist, etc.)'),

                                                        Forms\Components\TextInput::make('unit')
                                                            ->label('Unité')
                                                            ->default('cm')
                                                            ->required(),
                                                    ])
                                                    ->columns(2),

                                                Forms\Components\Grid::make()
                                                    ->schema([
                                                        Forms\Components\TextInput::make('name_fr')
                                                            ->label('Nom (français)')
                                                            ->required(),

                                                        Forms\Components\TextInput::make('name_en')
                                                            ->label('Nom (anglais)')
                                                            ->required(),
                                                    ])
                                                    ->columns(2),

                                                Forms\Components\Grid::make()
                                                    ->schema([
                                                        Forms\Components\Textarea::make('description_fr')
                                                            ->label('Description (français)')
                                                            ->helperText('Comment prendre cette mesure'),

                                                        Forms\Components\Textarea::make('description_en')
                                                            ->label('Description (anglais)')
                                                            ->helperText('How to take this measurement'),
                                                    ])
                                                    ->columns(2),
                                            ])
                                            ->itemLabel(fn (array $state): ?string => $state['name_fr'] ?? null)
                                            ->collapsible()
                                            ->required(),
                                    ]),
                            ]),

                        // Onglet 3: Systèmes de tailles
                        Forms\Components\Tabs\Tab::make('Systèmes de tailles')
                            ->icon('heroicon-o-scale')
                            ->schema([
                                Forms\Components\Section::make('Systèmes de tailles')
                                    ->description('Définir les différents systèmes de tailles (standard, EU, UK, US, etc.)')
                                    ->schema([
                                        Forms\Components\Repeater::make('size_systems')
                                            ->label('Systèmes de tailles')
                                            ->schema([
                                                Forms\Components\TextInput::make('code')
                                                    ->label('Code')
                                                    ->required()
                                                    ->helperText('Code unique pour ce système (ex: standard, eu, uk, us, etc.)'),

                                                Forms\Components\TextInput::make('name_fr')
                                                    ->label('Nom (français)')
                                                    ->required(),

                                                Forms\Components\TextInput::make('name_en')
                                                    ->label('Nom (anglais)')
                                                    ->required(),
                                            ])
                                            ->columns(3)
                                            ->itemLabel(fn (array $state): ?string => $state['name_fr'] ?? null)
                                            ->collapsible()
                                            ->required(),
                                    ]),
                            ]),

                        // Onglet 4: Tableau des tailles
                        Forms\Components\Tabs\Tab::make('Tableau des tailles')
                            ->icon('heroicon-o-table-cells')
                            ->schema([
                                Forms\Components\Section::make('Tableau des tailles')
                                    ->description('Créer le tableau des tailles avec les correspondances entre les systèmes et les mesures')
                                    ->schema([
                                        Forms\Components\Repeater::make('size_chart')
                                            ->label('Entrées du tableau')
                                            ->schema([
                                                Forms\Components\TextInput::make('size_code')
                                                    ->label('Code de taille')
                                                    ->required()
                                                    ->helperText('Code unique pour cette taille (ex: s, m, l, 38, 40, etc.)'),

                                                Forms\Components\Section::make('Correspondances des systèmes')
                                                    ->schema([
                                                        Forms\Components\Repeater::make('system_values')
                                                            ->label('Valeurs par système')
                                                            ->schema([
                                                                Forms\Components\Select::make('system')
                                                                    ->label('Système')
                                                                    ->options(function (Forms\Get $get) {
                                                                        $systems = $get('../../size_systems') ?? [];
                                                                        $options = [];

                                                                        foreach ($systems as $system) {
                                                                            if (isset($system['code']) && isset($system['name_fr'])) {
                                                                                $options[$system['code']] = $system['name_fr'];
                                                                            }
                                                                        }

                                                                        return $options;
                                                                    })
                                                                    ->required(),

                                                                Forms\Components\TextInput::make('value')
                                                                    ->label('Valeur')
                                                                    ->required(),
                                                            ])
                                                            ->columns(2)
                                                            ->required(),
                                                    ])
                                                    ->collapsible(),

                                                Forms\Components\Section::make('Mesures')
                                                    ->schema([
                                                        Forms\Components\Repeater::make('measurements')
                                                            ->label('Mesures')
                                                            ->schema([
                                                                Forms\Components\Select::make('type')
                                                                    ->label('Type de mesure')
                                                                    ->options(function (Forms\Get $get) {
                                                                        $types = $get('../../measurement_types') ?? [];
                                                                        $options = [];

                                                                        foreach ($types as $type) {
                                                                            if (isset($type['code']) && isset($type['name_fr'])) {
                                                                                $options[$type['code']] = $type['name_fr'];
                                                                            }
                                                                        }

                                                                        return $options;
                                                                    })
                                                                    ->required(),

                                                                Forms\Components\TextInput::make('value')
                                                                    ->label('Valeur')
                                                                    ->numeric()
                                                                    ->required(),
                                                            ])
                                                            ->columns(2)
                                                            ->required(),
                                                    ])
                                                    ->collapsible(),
                                            ])
                                            ->itemLabel(fn (array $state): ?string => 'Taille: ' . ($state['size_code'] ?? 'Nouvelle taille'))
                                            ->collapsible()
                                            ->required(),
                                    ]),
                            ]),

                        // Onglet 5: Conseils d'ajustement
                        Forms\Components\Tabs\Tab::make('Conseils d\'ajustement')
                            ->icon('heroicon-o-light-bulb')
                            ->schema([
                                Forms\Components\Section::make('Conseils d\'ajustement')
                                    ->description('Ajouter des conseils pour aider les clients à choisir la bonne taille')
                                    ->schema([
                                        Forms\Components\Repeater::make('fitting_tips')
                                            ->label('Conseils d\'ajustement')
                                            ->schema([
                                                Forms\Components\Grid::make()
                                                    ->schema([
                                                        Forms\Components\TextInput::make('category')
                                                            ->label('Catégorie')
                                                            ->required()
                                                            ->helperText('Type de vêtement (ex: chemises, pantalons, robes)'),

                                                        Forms\Components\Grid::make()
                                                            ->schema([
                                                                Forms\Components\TextInput::make('name_fr')
                                                                    ->label('Nom (français)')
                                                                    ->required(),

                                                                Forms\Components\TextInput::make('name_en')
                                                                    ->label('Nom (anglais)')
                                                                    ->required(),
                                                            ])
                                                            ->columns(2),
                                                    ]),

                                                Forms\Components\Grid::make()
                                                    ->schema([
                                                        Forms\Components\Section::make('Conseils en français')
                                                            ->schema([
                                                                Forms\Components\Repeater::make('tips_fr')
                                                                    ->label('Conseils (français)')
                                                                    ->schema([
                                                                        Forms\Components\Textarea::make('tip')
                                                                            ->label('Conseil')
                                                                            ->required(),
                                                                    ]),
                                                            ]),

                                                        Forms\Components\Section::make('Conseils en anglais')
                                                            ->schema([
                                                                Forms\Components\Repeater::make('tips_en')
                                                                    ->label('Conseils (anglais)')
                                                                    ->schema([
                                                                        Forms\Components\Textarea::make('tip')
                                                                            ->label('Conseil')
                                                                            ->required(),
                                                                    ]),
                                                            ]),
                                                    ])
                                                    ->columns(2),
                                            ])
                                            ->itemLabel(fn (array $state): ?string => $state['category'] ?? 'Nouveau conseil')
                                            ->collapsible(),
                                    ]),
                            ]),
                    ])
                    ->persistTabInQueryString()
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_fr')
                    ->label('Nom (français)')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('name_en')
                    ->label('Nom (anglais)')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('category')
                    ->label('Catégorie')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'clothing' => 'Vêtements (Clothing)',
                        'shoes' => 'Chaussures (Shoes)',
                        'accessories' => 'Accessoires (Accessories)',
                        'women-clothing' => 'Vêtements femmes (Women Clothing)',
                        'men-clothing' => 'Vêtements hommes (Men Clothing)',
                        'kids-clothing' => 'Vêtements enfants (Kids Clothing)',
                        'women-shoes' => 'Chaussures femmes (Women Shoes)',
                        'men-shoes' => 'Chaussures hommes (Men Shoes)',
                        'hats' => 'Chapeaux (Hats)',
                        'gloves' => 'Gants (Gloves)',
                        'belts' => 'Ceintures (Belts)',
                        'other' => 'Autre (Other)',
                        default => $state,
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('categories.nom')
                    ->label('Catégories associées')
                    ->listWithLineBreaks()
                    ->limitList(3),

                Tables\Columns\ImageColumn::make('image')
                    ->label('Image')
                    ->circular(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Actif')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Créé le')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label('Catégorie')
                    ->options([
                        'clothing' => 'Vêtements (Clothing)',
                        'shoes' => 'Chaussures (Shoes)',
                        'accessories' => 'Accessoires (Accessories)',
                        'women-clothing' => 'Vêtements femmes (Women Clothing)',
                        'men-clothing' => 'Vêtements hommes (Men Clothing)',
                        'kids-clothing' => 'Vêtements enfants (Kids Clothing)',
                        'women-shoes' => 'Chaussures femmes (Women Shoes)',
                        'men-shoes' => 'Chaussures hommes (Men Shoes)',
                        'hats' => 'Chapeaux (Hats)',
                        'gloves' => 'Gants (Gloves)',
                        'belts' => 'Ceintures (Belts)',
                        'other' => 'Autre (Other)',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Actif')
                    ->indicator('Actif'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSizeGuides::route('/'),
            'create' => Pages\CreateSizeGuide::route('/create'),
            'edit' => Pages\EditSizeGuide::route('/{record}/edit'),
        ];
    }
}
