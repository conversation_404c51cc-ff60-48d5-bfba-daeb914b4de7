<?php

namespace App\Filament\Resources\MarchandResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DocumentsValidationRelationManager extends RelationManager
{
    protected static string $relationship = 'documentsValidation';

    protected static ?string $title = 'Documents de validation';

    protected static ?string $modelLabel = 'Document';

    protected static ?string $pluralModelLabel = 'Documents';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('document_type')
                    ->label('Type de document')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('document_type')
            ->columns([
                Tables\Columns\TextColumn::make('document_type')
                    ->label('Type de document')
                    ->formatStateUsing(function ($state) {
                        return match($state) {
                            'KBIS' => 'Extrait Kbis',
                            'PIECE_IDENTITE' => 'Pièce d\'identité',
                            'PHOTO_AVEC_PIECE' => 'Photo avec pièce d\'identité',
                            'STATUTS_ENTREPRISE' => 'Statuts d\'entreprise',
                            'RIB_BANCAIRE' => 'RIB bancaire',
                            'ATTESTATION_FISCALE' => 'Attestation fiscale',
                            'REGISTRE_COMMERCE' => 'Registre commerce',
                            'JUSTIFICATIF_DOMICILE' => 'Justificatif de domicile',
                            'RECEPISSE_DECLARATION' => 'Récépissé de déclaration',
                            'BILAN_COMPTABLE' => 'Bilan comptable',
                            'DECLARATION_FISCALE' => 'Déclaration fiscale',
                            'AUTRES' => 'Autre document',
                            default => $state,
                        };
                    }),
                Tables\Columns\TextColumn::make('original_name')
                    ->label('Nom du fichier')
                    ->limit(30),
                Tables\Columns\TextColumn::make('file_size')
                    ->label('Taille')
                    ->formatStateUsing(fn ($state) => $state ? number_format($state / 1024, 2) . ' KB' : 'N/A'),
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Statut')
                    ->colors([
                        'warning' => 'EN_ATTENTE',
                        'success' => 'VALIDE',
                        'danger' => 'REJETE',
                    ])
                    ->formatStateUsing(fn ($state) => match($state) {
                        'EN_ATTENTE' => 'En attente',
                        'VALIDE' => 'Validé',
                        'REJETE' => 'Rejeté',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Uploadé le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Statut')
                    ->options([
                        'EN_ATTENTE' => 'En attente',
                        'VALIDE' => 'Validé',
                        'REJETE' => 'Rejeté',
                    ]),
                Tables\Filters\SelectFilter::make('document_type')
                    ->label('Type de document')
                    ->options([
                        'KBIS' => 'Extrait Kbis',
                        'PIECE_IDENTITE' => 'Pièce d\'identité',
                        'PHOTO_AVEC_PIECE' => 'Photo avec pièce d\'identité',
                        'STATUTS_ENTREPRISE' => 'Statuts d\'entreprise',
                        'RIB_BANCAIRE' => 'RIB bancaire',
                        'ATTESTATION_FISCALE' => 'Attestation fiscale',
                        'REGISTRE_COMMERCE' => 'Registre commerce',
                        'JUSTIFICATIF_DOMICILE' => 'Justificatif de domicile',
                        'RECEPISSE_DECLARATION' => 'Récépissé de déclaration',
                        'BILAN_COMPTABLE' => 'Bilan comptable',
                        'DECLARATION_FISCALE' => 'Déclaration fiscale',
                        'AUTRES' => 'Autre document',
                    ]),
            ])
            ->headerActions([
                // Pas de création directe depuis cette interface
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('Visualiser')
                    ->icon('heroicon-m-eye')
                    ->url(function ($record) {
                        $validation = $record->validation;
                        if (!$validation) return null;

                        $extension = strtolower(pathinfo($record->original_name, PATHINFO_EXTENSION));
                        $viewableTypes = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'webp'];

                        if (!in_array($extension, $viewableTypes)) return null;

                        return route('admin.merchant-validation.view-document', [
                            'validation' => $validation->id,
                            'document' => $record->id
                        ]);
                    })
                    ->openUrlInNewTab()
                    ->visible(function ($record) {
                        if (!$record->file_path) return false;
                        $extension = strtolower(pathinfo($record->original_name, PATHINFO_EXTENSION));
                        return in_array($extension, ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'webp']);
                    }),
                Tables\Actions\Action::make('download')
                    ->label('Télécharger')
                    ->icon('heroicon-m-arrow-down-tray')
                    ->url(function ($record) {
                        $validation = $record->validation;
                        if (!$validation) return null;

                        return route('admin.merchant-validation.download-document', [
                            'validation' => $validation->id,
                            'document' => $record->id
                        ]);
                    })
                    ->openUrlInNewTab()
                    ->visible(fn ($record) => $record->file_path),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Pas d'actions en masse pour les documents
                ]),
            ])
            ->modifyQueryUsing(fn (Builder $query) => $query->with(['validation']))
            ->defaultSort('created_at', 'desc');
    }
}
