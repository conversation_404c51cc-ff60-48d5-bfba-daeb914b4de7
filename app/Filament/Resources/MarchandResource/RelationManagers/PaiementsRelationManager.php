<?php

namespace App\Filament\Resources\MarchandResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PaiementsRelationManager extends RelationManager
{
    protected static string $relationship = 'paiements';

    protected static ?string $recordTitleAttribute = 'id';

    protected static ?string $title = 'Paiements';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('commande.id')
                    ->label('Commande')
                    ->url(fn ($record) => $record->commande ? route('filament.admin.resources.commandes.view', ['record' => $record->commande]) : null)
                    ->sortable(),
                Tables\Columns\TextColumn::make('montant')
                    ->label('Montant')
                    ->money('EUR')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('statut')
                    ->label('Statut')
                    ->colors([
                        'primary' => 'EnAttente',
                        'success' => 'Complété',
                        'danger' => 'Échoué',
                        'warning' => 'Remboursé',
                    ]),
                Tables\Columns\TextColumn::make('methode')
                    ->label('Méthode')
                    ->sortable(),
                Tables\Columns\TextColumn::make('transaction_id')
                    ->label('ID de transaction')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Créé le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('statut')
                    ->label('Statut')
                    ->options([
                        'EnAttente' => 'En attente',
                        'Complété' => 'Complété',
                        'Échoué' => 'Échoué',
                        'Remboursé' => 'Remboursé',
                    ]),
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Créé depuis'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Créé jusqu\'à'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->headerActions([
                // Pas d'action de création ici car les paiements sont créés automatiquement
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Pas d'action de suppression en masse pour les paiements
                ]),
            ]);
    }
}
