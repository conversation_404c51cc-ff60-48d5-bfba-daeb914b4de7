<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MarchandResource\Pages;
use App\Filament\Resources\MarchandResource\RelationManagers;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\AdminPermission;
use App\Models\Marchand;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Support\Enums\FontWeight;
use Illuminate\Support\Collection;

class MarchandResource extends Resource
{
    use HasPermissionChecks;

    protected static ?string $model = Marchand::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';

    protected static ?string $navigationLabel = 'Marchands';

    protected static ?string $modelLabel = 'Marchand';

    protected static ?string $pluralModelLabel = 'Marchands';

    protected static ?string $navigationGroup = 'Gestion des Marchands';

    protected static ?int $navigationSort = 10;

    public static function canAccess(): bool
    {
        return static::canViewAdmin(AdminPermission::VIEW_MERCHANTS);
    }

    public static function canCreate(): bool
    {
        return static::canCreateAdmin(AdminPermission::CREATE_MERCHANTS);
    }

    public static function canEdit(Model $record): bool
    {
        return static::canEditAdmin(AdminPermission::EDIT_MERCHANTS);
    }

    public static function canDelete(Model $record): bool
    {
        return static::canDeleteAdmin(AdminPermission::DELETE_MERCHANTS);
    }

    public static function canViewAny(): bool
    {
        return static::canViewAdmin(AdminPermission::VIEW_MERCHANTS);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informations du compte')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('Utilisateur')
                            ->options(
                                User::where('role', 'Marchand')
                                    ->orWhereDoesntHave('marchand')
                                    ->whereNotNull('email')
                                    ->where('email', '!=', '')
                                    ->pluck('email', 'id')
                            )
                            ->searchable()
                            ->required()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->label('Nom')
                                    ->required(),
                                Forms\Components\TextInput::make('email')
                                    ->label('Email')
                                    ->email()
                                    ->required()
                                    ->unique('users', 'email'),
                                Forms\Components\TextInput::make('password')
                                    ->label('Mot de passe')
                                    ->password()
                                    ->required()
                                    ->confirmed(),
                                Forms\Components\TextInput::make('password_confirmation')
                                    ->label('Confirmation du mot de passe')
                                    ->password()
                                    ->required(),
                                Forms\Components\Hidden::make('role')
                                    ->default('Marchand'),
                                Forms\Components\Toggle::make('is_active')
                                    ->label('Actif')
                                    ->default(true),
                            ])
                            ->createOptionAction(function (Forms\Components\Actions\Action $action) {
                                return $action
                                    ->modalHeading('Créer un nouvel utilisateur')
                                    ->modalWidth('lg');
                            }),
                        Forms\Components\TextInput::make('nomEntreprise')
                            ->label('Nom de l\'entreprise')
                            ->required()
                            ->maxLength(255),
                    ]),
                Forms\Components\Section::make('Informations fiscales et bancaires')
                    ->schema([
                        Forms\Components\TextInput::make('idFiscal')
                            ->label('Identifiant fiscal')
                            ->maxLength(50),
                        Forms\Components\TextInput::make('banqueNom')
                            ->label('Nom de la banque')
                            ->maxLength(100),
                        Forms\Components\TextInput::make('banqueNumeroCompte')
                            ->label('Numéro de compte bancaire')
                            ->maxLength(50),
                    ]),
                Forms\Components\Section::make('Adresse')
                    ->schema([
                        Forms\Components\Select::make('adresse_id')
                            ->label('Adresse')
                            ->relationship('adresse', 'rue')
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('rue')
                                    ->label('Rue')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('ville')
                                    ->label('Ville')
                                    ->required()
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('etat')
                                    ->label('État/Province')
                                    ->required()
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('pays')
                                    ->label('Pays')
                                    ->required()
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('codePostal')
                                    ->label('Code postal')
                                    ->required()
                                    ->maxLength(20),
                                Forms\Components\Hidden::make('type')
                                    ->default('Entreprise'),
                            ])
                            ->createOptionAction(function (Forms\Components\Actions\Action $action) {
                                return $action
                                    ->modalHeading('Créer une nouvelle adresse')
                                    ->modalWidth('lg');
                            }),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('nomEntreprise')
                    ->label('Nom de l\'entreprise')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.email')
                    ->label('Email')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('produits_count')
                    ->label('Nombre de produits')
                    ->counts('produits')
                    ->sortable(),
                Tables\Columns\TextColumn::make('commandes_count')
                    ->label('Nombre de commandes')
                    ->counts('commandes')
                    ->sortable(),
                Tables\Columns\IconColumn::make('user.is_active')
                    ->label('Actif')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Créé le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('is_active')
                    ->label('Statut')
                    ->options([
                        '1' => 'Actif',
                        '0' => 'Inactif',
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when(
                                $data['value'] !== null,
                                fn (Builder $query): Builder => $query->whereHas(
                                    'user',
                                    fn (Builder $query): Builder => $query->where('is_active', $data['value'])
                                )
                            );
                    }),
                Tables\Filters\Filter::make('has_products')
                    ->label('Avec produits')
                    ->query(fn (Builder $query): Builder => $query->has('produits')),
                Tables\Filters\Filter::make('no_products')
                    ->label('Sans produits')
                    ->query(fn (Builder $query): Builder => $query->doesntHave('produits')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('toggle_active')
                    ->label(fn (Marchand $record): string => $record->user->is_active ? 'Désactiver' : 'Activer')
                    ->icon(fn (Marchand $record): string => $record->user->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn (Marchand $record): string => $record->user->is_active ? 'danger' : 'success')
                    ->requiresConfirmation()
                    ->action(function (Marchand $record): void {
                        $user = $record->user;
                        $user->is_active = !$user->is_active;
                        $user->save();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activer')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function (Collection $records): void {
                            foreach ($records as $record) {
                                $user = $record->user;
                                $user->is_active = true;
                                $user->save();
                            }
                        }),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Désactiver')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->action(function (Collection $records): void {
                            foreach ($records as $record) {
                                $user = $record->user;
                                $user->is_active = false;
                                $user->save();
                            }
                        }),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                // Informations générales
                Infolists\Components\Section::make('Informations générales')
                    ->schema([
                        Infolists\Components\TextEntry::make('nomEntreprise')
                            ->label('Nom de l\'entreprise')
                            ->weight(FontWeight::Bold)
                            ->size(Infolists\Components\TextEntry\TextEntrySize::Large),
                        Infolists\Components\TextEntry::make('user.name')
                            ->label('Nom du propriétaire'),
                        Infolists\Components\TextEntry::make('user.email')
                            ->label('Email'),
                        Infolists\Components\TextEntry::make('user.is_active')
                            ->label('Statut du compte')
                            ->badge()
                            ->color(fn (bool $state): string => $state ? 'success' : 'danger')
                            ->formatStateUsing(fn (bool $state): string => $state ? 'Actif' : 'Inactif'),
                        Infolists\Components\TextEntry::make('statut_validation')
                            ->label('Statut de validation')
                            ->badge()
                            ->color(fn (string $state): string => match($state) {
                                'valide' => 'success',
                                'rejete' => 'danger',
                                default => 'warning'
                            })
                            ->formatStateUsing(fn (string $state): string => match($state) {
                                'valide' => 'Validé',
                                'rejete' => 'Rejeté',
                                'en_attente' => 'En attente',
                                default => ucfirst($state)
                            }),
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Inscrit depuis')
                            ->dateTime('d/m/Y H:i'),
                        Infolists\Components\TextEntry::make('date_validation')
                            ->label('Validé le')
                            ->dateTime('d/m/Y H:i')
                            ->placeholder('Non validé'),
                    ])
                    ->columns(3),

                // Informations business
                Infolists\Components\Section::make('Informations business')
                    ->schema([
                        Infolists\Components\TextEntry::make('type_business')
                            ->label('Type d\'entreprise')
                            ->formatStateUsing(fn (string $state): string => match($state) {
                                'individuel' => 'Entreprise individuelle',
                                'entreprise' => 'Entreprise',
                                'association' => 'Association',
                                default => ucfirst($state)
                            }),
                        Infolists\Components\TextEntry::make('pays_business')
                            ->label('Pays'),
                        Infolists\Components\TextEntry::make('ville_business')
                            ->label('Ville'),
                        Infolists\Components\TextEntry::make('telephone_principal')
                            ->label('Téléphone principal'),
                        Infolists\Components\TextEntry::make('email_business')
                            ->label('Email business'),
                        Infolists\Components\TextEntry::make('site_web')
                            ->label('Site web')
                            ->url(fn ($state) => $state)
                            ->openUrlInNewTab(),
                        Infolists\Components\TextEntry::make('description_business')
                            ->label('Description')
                            ->columnSpanFull(),
                        Infolists\Components\TextEntry::make('categories_produits')
                            ->label('Catégories de produits')
                            ->formatStateUsing(fn ($state): string => is_array($state) ? implode(', ', $state) : ($state ?? 'Non spécifié'))
                            ->columnSpanFull(),
                    ])
                    ->columns(3),

                // Informations financières (visible uniquement aux super_admin)
                Infolists\Components\Section::make('Informations financières')
                    ->schema([
                        Infolists\Components\TextEntry::make('chiffre_affaires_estime')
                            ->label('Chiffre d\'affaires estimé')
                            ->money('XOF'),
                        Infolists\Components\TextEntry::make('nombre_employes')
                            ->label('Nombre d\'employés'),
                        Infolists\Components\TextEntry::make('methode_paiement_preferee')
                            ->label('Méthode de paiement préférée')
                            ->formatStateUsing(fn (string $state): string => match($state) {
                                'bancaire' => 'Virement bancaire',
                                'mobile_money' => 'Mobile Money',
                                'orange_money' => 'Orange Money',
                                'mtn_money' => 'MTN Money',
                                'paypal' => 'PayPal',
                                'stripe' => 'Stripe',
                                default => ucfirst($state)
                            }),
                    ])
                    ->columns(3)
                    ->visible(fn (): bool => auth()->user()?->isSuperAdmin() ?? false),

                // Informations bancaires (visible uniquement aux super_admin)
                Infolists\Components\Section::make('Informations bancaires')
                    ->schema([
                        Infolists\Components\TextEntry::make('idFiscal')
                            ->label('Identifiant fiscal'),
                        Infolists\Components\TextEntry::make('banqueNom')
                            ->label('Nom de la banque'),
                        Infolists\Components\TextEntry::make('banqueNumeroCompte')
                            ->label('Numéro de compte bancaire'),
                        Infolists\Components\TextEntry::make('nom_titulaire_compte')
                            ->label('Nom du titulaire'),
                        Infolists\Components\TextEntry::make('numero_orange_money')
                            ->label('Numéro Orange Money'),
                        Infolists\Components\TextEntry::make('numero_mtn_money')
                            ->label('Numéro MTN Money'),
                    ])
                    ->columns(3)
                    ->visible(fn (): bool => auth()->user()?->isSuperAdmin() ?? false),

                // Adresse
                Infolists\Components\Section::make('Adresse')
                    ->schema([
                        Infolists\Components\TextEntry::make('adresse.rue')
                            ->label('Rue'),
                        Infolists\Components\TextEntry::make('adresse.ville')
                            ->label('Ville'),
                        Infolists\Components\TextEntry::make('adresse.etat')
                            ->label('État/Province'),
                        Infolists\Components\TextEntry::make('adresse.pays')
                            ->label('Pays'),
                        Infolists\Components\TextEntry::make('adresse.codePostal')
                            ->label('Code postal'),
                    ])
                    ->columns(3),

                // Préférences
                Infolists\Components\Section::make('Préférences')
                    ->schema([
                        Infolists\Components\TextEntry::make('langue_preferee')
                            ->label('Langue préférée')
                            ->formatStateUsing(fn (string $state): string => match($state) {
                                'fr' => 'Français',
                                'en' => 'Anglais',
                                default => ucfirst($state)
                            }),
                        Infolists\Components\TextEntry::make('source_inscription')
                            ->label('Source d\'inscription')
                            ->formatStateUsing(fn (string $state): string => match($state) {
                                'direct' => 'Inscription directe',
                                'parrainage' => 'Parrainage',
                                'publicite_facebook' => 'Publicité Facebook',
                                'publicite_google' => 'Publicité Google',
                                'seller_platform' => 'Plateforme vendeur',
                                'autre' => 'Autre',
                                default => ucfirst($state)
                            }),
                        Infolists\Components\IconEntry::make('accepte_conditions')
                            ->label('Conditions acceptées')
                            ->boolean(),
                        Infolists\Components\IconEntry::make('accepte_newsletter')
                            ->label('Newsletter acceptée')
                            ->boolean(),
                    ])
                    ->columns(4),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ProduitsRelationManager::class,
            RelationManagers\CommandesRelationManager::class,
            RelationManagers\PaiementsRelationManager::class,
            RelationManagers\DocumentsValidationRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMarchands::route('/'),
            'create' => Pages\CreateMarchand::route('/create'),
            'view' => Pages\ViewMarchand::route('/{record}'),
            'edit' => Pages\EditMarchand::route('/{record}/edit'),
        ];
    }
}
