<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CommandeResource\Pages;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\AdminPermission;
use App\Models\CommandePrincipale;
use App\Models\Marchand;
use App\Models\Client;
use App\Services\CommandeAdapterService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class CommandeResource extends Resource
{
    use HasPermissionChecks;

    protected static ?string $model = CommandePrincipale::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';

    protected static ?string $navigationGroup = 'Commandes & Expéditions';

    protected static ?int $navigationSort = 1;

    protected static ?string $recordTitleAttribute = 'numero_commande';

    public static function canAccess(): bool
    {
        return static::canViewAdmin(AdminPermission::VIEW_ORDERS, 'operations');
    }

    public static function canCreate(): bool
    {
        return static::canCreateAdmin(AdminPermission::MANAGE_ORDERS, 'operations');
    }

    public static function canEdit(Model $record): bool
    {
        return static::canEditAdmin(AdminPermission::MANAGE_ORDERS, 'operations');
    }

    public static function canDelete(Model $record): bool
    {
        return static::canDeleteAdmin(AdminPermission::MANAGE_ORDERS, 'operations');
    }

    public static function canViewAny(): bool
    {
        return static::canViewAdmin(AdminPermission::VIEW_ORDERS, 'operations');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Informations de la Commande Principale')
                            ->schema([
                                Forms\Components\TextInput::make('numero_commande')
                                    ->label('Numéro de commande')
                                    ->disabled(),

                                Forms\Components\Select::make('client_id')
                                    ->label('Client')
                                    ->options(Client::with('user')->get()->mapWithKeys(function ($client) {
                                        $label = ($client->prenom ?? 'Prénom') . ' ' . ($client->nom ?? 'Nom');
                                        if ($client->user && $client->user->email) {
                                            $label .= ' (' . $client->user->email . ')';
                                        }
                                        return [$client->id => $label];
                                    }))
                                    ->searchable()
                                    ->disabled(), // Les commandes principales ne sont pas modifiables

                                Forms\Components\Select::make('statut_global')
                                    ->label('Statut Global')
                                    ->options([
                                        'EnAttente' => 'En attente',
                                        'PayementConfirme' => 'Paiement confirmé',
                                        'EnTraitement' => 'En traitement',
                                        'PartielExpedié' => 'Partiellement expédié',
                                        'TotalementExpedié' => 'Totalement expédié',
                                        'TotalementLivré' => 'Totalement livré',
                                        'Terminé' => 'Terminé',
                                    ])
                                    ->required(),

                                Forms\Components\Select::make('statut_paiement')
                                    ->label('Statut Paiement')
                                    ->options([
                                        'EnAttente' => 'En attente',
                                        'EnCours' => 'En cours',
                                        'Complété' => 'Complété',
                                        'Échoué' => 'Échoué',
                                        'Remboursé' => 'Remboursé',
                                    ])
                                    ->required(),

                                Forms\Components\TextInput::make('montant_total_ttc')
                                    ->label('Montant total TTC')
                                    ->numeric()
                                    ->prefix('€')
                                    ->disabled(),

                                Forms\Components\DatePicker::make('date_livraison_souhaitee')
                                    ->label('Date de livraison souhaitée'),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Statistiques')
                            ->schema([
                                Forms\Components\TextInput::make('nombre_marchands')
                                    ->label('Nombre de marchands')
                                    ->disabled(),

                                Forms\Components\TextInput::make('nombre_sous_commandes')
                                    ->label('Nombre de sous-commandes')
                                    ->disabled(),

                                Forms\Components\DateTimePicker::make('date_commande')
                                    ->label('Date de commande')
                                    ->disabled(),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('numero_commande')
                    ->label('N° Commande')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('Numéro copié!')
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('client.nom')
                    ->label('Client')
                    ->formatStateUsing(fn (CommandePrincipale $record): string =>
                        ($record->client->prenom ?? '') . ' ' . ($record->client->nom ?? 'Client supprimé')
                    )
                    ->searchable(),

                Tables\Columns\TextColumn::make('nombre_marchands')
                    ->label('Marchands')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('nombre_sous_commandes')
                    ->label('Sous-commandes')
                    ->badge()
                    ->color('warning'),

                Tables\Columns\TextColumn::make('montant_total_ttc')
                    ->label('Montant TTC')
                    ->money('XOF')
                    ->sortable(),

                Tables\Columns\SelectColumn::make('statut_global')
                    ->label('Statut Global')
                    ->options([
                        'EnAttente' => 'En attente',
                        'PayementConfirme' => 'Paiement confirmé',
                        'EnTraitement' => 'En traitement',
                        'PartielExpedié' => 'Partiellement expédié',
                        'TotalementExpedié' => 'Totalement expédié',
                        'TotalementLivré' => 'Totalement livré',
                        'Terminé' => 'Terminé',
                    ])
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('statut_paiement')
                    ->label('Paiement')
                    ->colors([
                        'danger' => 'EnAttente',
                        'warning' => 'EnCours',
                        'success' => 'Complété',
                        'danger' => 'Échoué',
                        'secondary' => 'Remboursé',
                    ]),

                Tables\Columns\TextColumn::make('date_commande')
                    ->label('Date de commande')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('statut_global')
                    ->label('Statut Global')
                    ->options([
                        'EnAttente' => 'En attente',
                        'PayementConfirme' => 'Paiement confirmé',
                        'EnTraitement' => 'En traitement',
                        'PartielExpedié' => 'Partiellement expédié',
                        'TotalementExpedié' => 'Totalement expédié',
                        'TotalementLivré' => 'Totalement livré',
                        'Terminé' => 'Terminé',
                    ]),

                Tables\Filters\SelectFilter::make('statut_paiement')
                    ->label('Statut Paiement')
                    ->options([
                        'EnAttente' => 'En attente',
                        'EnCours' => 'En cours',
                        'Complété' => 'Complété',
                        'Échoué' => 'Échoué',
                        'Remboursé' => 'Remboursé',
                    ]),

                Tables\Filters\Filter::make('date_commande')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Depuis le'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Jusqu\'au'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date_commande', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date_commande', '<=', $date),
                            );
                    }),

                Tables\Filters\Filter::make('montant_min')
                    ->form([
                        Forms\Components\TextInput::make('montant_min')
                            ->label('Montant minimum')
                            ->numeric()
                            ->prefix('€'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['montant_min'],
                            fn (Builder $query, $montant): Builder => $query->where('montant_total_ttc', '>=', $montant),
                        );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('update_status')
                        ->label('Mettre à jour le statut global')
                        ->form([
                            Forms\Components\Select::make('statut_global')
                                ->label('Nouveau statut global')
                                ->options([
                                    'EnAttente' => 'En attente',
                                    'PayementConfirme' => 'Paiement confirmé',
                                    'EnTraitement' => 'En traitement',
                                    'PartielExpedié' => 'Partiellement expédié',
                                    'TotalementExpedié' => 'Totalement expédié',
                                    'TotalementLivré' => 'Totalement livré',
                                    'Terminé' => 'Terminé',
                                ])
                                ->required(),
                        ])
                        ->action(function (array $data, array $records): void {
                            foreach ($records as $record) {
                                $record->update([
                                    'statut_global' => $data['statut_global'],
                                ]);
                            }
                        }),
                ]),
            ]);
    }

    /**
     * Utiliser notre service d'adaptation pour les requêtes
     */
    public static function getEloquentQuery(): Builder
    {
        return app(CommandeAdapterService::class)->getCommandesPrincipalesForAdmin();
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCommandes::route('/'),
            'create' => Pages\CreateCommande::route('/create'),
            'view' => Pages\ViewCommande::route('/{record}'),
            'edit' => Pages\EditCommande::route('/{record}/edit'),
        ];
    }
}
