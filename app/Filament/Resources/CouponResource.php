<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CouponResource\Pages;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\AdminPermission;
use App\Models\Coupon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class CouponResource extends Resource
{
    use HasPermissionChecks;

    protected static ?string $model = Coupon::class;

    protected static ?string $navigationIcon = 'heroicon-o-ticket';

    protected static ?string $navigationGroup = 'Marketing & Promotions';

    protected static ?int $navigationSort = 2;

    protected static ?string $recordTitleAttribute = 'code';

    public static function canAccess(): bool
    {
        return static::canViewAdmin(AdminPermission::MANAGE_PROMOTIONS, 'marketing');
    }

    public static function canCreate(): bool
    {
        return static::canCreateAdmin(AdminPermission::MANAGE_PROMOTIONS, 'marketing');
    }

    public static function canEdit(Model $record): bool
    {
        return static::canEditAdmin(AdminPermission::MANAGE_PROMOTIONS, 'marketing');
    }

    public static function canDelete(Model $record): bool
    {
        return static::canDeleteAdmin(AdminPermission::MANAGE_PROMOTIONS, 'marketing');
    }

    public static function canViewAny(): bool
    {
        return static::canViewAdmin(AdminPermission::MANAGE_PROMOTIONS, 'marketing');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informations du coupon')
                    ->schema([
                        Forms\Components\TextInput::make('code')
                            ->label('Code')
                            ->required()
                            ->maxLength(50)
                            ->unique(ignoreRecord: true),

                        Forms\Components\Select::make('type')
                            ->label('Type de réduction')
                            ->options([
                                'Pourcentage' => 'Pourcentage',
                                'MontantFixe' => 'Montant fixe',
                            ])
                            ->required()
                            ->default('Pourcentage'),

                        Forms\Components\TextInput::make('valeur')
                            ->label('Valeur')
                            ->required()
                            ->numeric()
                            ->minValue(0)
                            ->suffix(fn (callable $get) => $get('type') === 'Pourcentage' ? '%' : '€'),

                        Forms\Components\DateTimePicker::make('date_debut')
                            ->label('Date de début'),

                        Forms\Components\DateTimePicker::make('date_fin')
                            ->label('Date de fin'),

                        Forms\Components\TextInput::make('utilisation_max')
                            ->label('Nombre maximum d\'utilisations')
                            ->numeric()
                            ->minValue(1),

                        Forms\Components\TextInput::make('utilisation_compteur')
                            ->label('Nombre d\'utilisations actuel')
                            ->numeric()
                            ->minValue(0)
                            ->default(0)
                            ->disabled(),

                        Forms\Components\Toggle::make('est_actif')
                            ->label('Actif')
                            ->default(true),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Pourcentage' => 'info',
                        'MontantFixe' => 'success',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('valeur')
                    ->label('Valeur')
                    ->formatStateUsing(fn (string $state, Coupon $record): string =>
                        $record->type === 'Pourcentage' ? "{$state}%" : "{$state} €"
                    )
                    ->sortable(),

                Tables\Columns\TextColumn::make('date_debut')
                    ->label('Début')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('date_fin')
                    ->label('Fin')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('utilisation_compteur')
                    ->label('Utilisé')
                    ->formatStateUsing(fn (string $state, Coupon $record): string =>
                        $record->utilisation_max ? "{$state} / {$record->utilisation_max}" : $state
                    )
                    ->sortable(),

                Tables\Columns\IconColumn::make('est_actif')
                    ->label('Actif')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\Filter::make('active')
                    ->label('Coupons actifs')
                    ->query(fn (Builder $query): Builder => $query->where('est_actif', true)),

                Tables\Filters\Filter::make('current')
                    ->label('Coupons en cours')
                    ->query(function (Builder $query): Builder {
                        return $query
                            ->where('est_actif', true)
                            ->where(function (Builder $query) {
                                $query->whereNull('date_debut')
                                    ->orWhere('date_debut', '<=', now());
                            })
                            ->where(function (Builder $query) {
                                $query->whereNull('date_fin')
                                    ->orWhere('date_fin', '>=', now());
                            })
                            ->where(function (Builder $query) {
                                $query->whereNull('utilisation_max')
                                    ->orWhereRaw('utilisation_compteur < utilisation_max');
                            });
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activer')
                        ->icon('heroicon-o-check-circle')
                        ->action(fn (array $records) => Coupon::whereIn('id', $records)->update(['est_actif' => true])),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Désactiver')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(fn (array $records) => Coupon::whereIn('id', $records)->update(['est_actif' => false])),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCoupons::route('/'),
            'create' => Pages\CreateCoupon::route('/create'),
            'edit' => Pages\EditCoupon::route('/{record}/edit'),
        ];
    }
}
