<?php

namespace App\Filament\Resources\BoutiqueReviewResource\Pages;

use App\Filament\Resources\BoutiqueReviewResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ListBoutiqueReviews extends ListRecords
{
    protected static string $resource = BoutiqueReviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Pas d'action de création car les avis sont créés par les clients
        ];
    }

    public function getTabs(): array
    {
        $user = Auth::user();
        
        $tabs = [
            'all' => Tab::make('Tous les avis'),
        ];

        // Onglets spécifiques aux admins
        if ($user?->adminUser) {
            $tabs['pending'] = Tab::make('En attente')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_approved', false))
                ->badge(fn () => static::getResource()::getModel()::where('is_approved', false)->count())
                ->badgeColor('warning');

            $tabs['approved'] = Tab::make('Approuvés')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_approved', true));

            $tabs['reported'] = Tab::make('Signalés')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_reported', true))
                ->badge(fn () => static::getResource()::getModel()::where('is_reported', true)->count())
                ->badgeColor('danger');

            $tabs['verified'] = Tab::make('Achats vérifiés')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_verified', true));
        }

        // Onglets spécifiques aux marchands
        if ($user?->marchandUser) {
            $tabs['without_response'] = Tab::make('Sans réponse')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->where('marchand_id', $user->marchandUser->marchand_id)
                    ->where('is_approved', true)
                    ->whereNull('marchand_response')
                )
                ->badge(fn () => static::getResource()::getModel()::where('marchand_id', $user->marchandUser->marchand_id)
                    ->where('is_approved', true)
                    ->whereNull('marchand_response')
                    ->count())
                ->badgeColor('warning');

            $tabs['with_response'] = Tab::make('Avec réponse')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->where('marchand_id', $user->marchandUser->marchand_id)
                    ->whereNotNull('marchand_response')
                );
        }

        return $tabs;
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // Optionnel : Ajouter des widgets de statistiques
        ];
    }
}
