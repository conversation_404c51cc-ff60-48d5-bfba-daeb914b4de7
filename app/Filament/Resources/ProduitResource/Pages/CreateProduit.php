<?php

namespace App\Filament\Resources\ProduitResource\Pages;

use App\Filament\Resources\ProduitResource;
use App\Filament\Traits\HandlesImageStorage;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Session;

class CreateProduit extends CreateRecord
{
    use HandlesImageStorage;
    protected static string $resource = ProduitResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Définir la date de création
        $data['creeLe'] = now();

        // Définir la date de mise à jour
        $data['misAJourLe'] = now();

        // S'assurer que le slug est défini
        if (empty($data['slug']) && isset($data['nom']['fr'])) {
            $data['slug'] = \Illuminate\Support\Str::slug($data['nom']['fr']);
        }

        // Valeurs par défaut pour les champs requis
        if (!isset($data['stock']) || $data['stock'] === null) {
            $data['stock'] = 0;
        }

        if (!isset($data['marque']) || $data['marque'] === null) {
            $data['marque'] = null; // Peut être null
        }

        // S'assurer que les champs JSON sont bien formatés
        if (!isset($data['attributs'])) {
            $data['attributs'] = [];
        }

        if (!isset($data['images'])) {
            $data['images'] = [];
        }

        return $data;
    }

    protected function afterCreate(): void
    {
        // Déplacer les images du dossier temporaire vers le dossier basé sur l'ID
        self::moveImagesAfterCreate($this->record, 'products');

        // Définir une variable de session pour indiquer qu'un produit vient d'être créé
        Session::put('product_created', true);

        // Afficher une notification de succès
        \Filament\Notifications\Notification::make()
            ->title('Produit créé')
            ->body('Le produit a été créé avec succès. Vous pouvez maintenant ajouter des variantes.')
            ->success()
            ->send();
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('edit', ['record' => $this->record->id]);
    }
}
