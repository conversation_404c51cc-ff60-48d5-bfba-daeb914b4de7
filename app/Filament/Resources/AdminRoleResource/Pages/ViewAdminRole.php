<?php

namespace App\Filament\Resources\AdminRoleResource\Pages;

use App\Filament\Resources\AdminRoleResource;
use App\Enums\AdminPermission;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewAdminRole extends ViewRecord
{
    protected static string $resource = AdminRoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn (): bool =>
                    (!$this->record->is_system_role || auth()->user()?->isSuperAdmin()) &&
                    (auth()->user()?->isSuperAdmin() ||
                     (auth()->user()?->hasAdminPermission(AdminPermission::MANAGE_ROLES) ?? false))
                ),
            Actions\DeleteAction::make()
                ->visible(fn (): bool => 
                    $this->record->canBeDeleted() && 
                    (auth()->user()?->isSuperAdmin() ?? false)
                )
                ->requiresConfirmation()
                ->modalHeading('Supprimer ce rôle')
                ->modalDescription('Êtes-vous sûr de vouloir supprimer ce rôle ? Cette action est irréversible.')
                ->modalSubmitActionLabel('Supprimer'),
        ];
    }
}
