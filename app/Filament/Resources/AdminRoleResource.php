<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AdminRoleResource\Pages;
use App\Models\AdminRole;
use App\Enums\AdminPermission;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;

class AdminRoleResource extends Resource
{
    protected static ?string $model = AdminRole::class;

    protected static ?string $navigationIcon = 'heroicon-o-key';

    protected static ?string $navigationLabel = 'Rôles Admin';

    protected static ?string $modelLabel = 'Rôle Admin';

    protected static ?string $pluralModelLabel = 'Rôles Admin';

    protected static ?string $navigationGroup = 'Gestion des Utilisateurs';

    protected static ?int $navigationSort = 3;

    public static function canAccess(): bool
    {
        return static::checkAdminPermission(AdminPermission::MANAGE_ROLES);
    }

    /**
     * Helper pour vérifier les permissions admin avec fallback pour super admin
     */
    private static function checkAdminPermission(AdminPermission $permission): bool
    {
        $user = auth()->user();

        // Permettre l'accès aux super admins même sans AdminUser (pour la configuration initiale)
        if ($user?->isSuperAdmin()) {
            return true;
        }

        return $user?->hasAdminPermission($permission) ?? false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informations du rôle')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nom du rôle')
                            ->required()
                            ->maxLength(100)
                            ->unique(ignoreRecord: true),

                        Forms\Components\TextInput::make('slug')
                            ->label('Identifiant (slug)')
                            ->required()
                            ->maxLength(100)
                            ->unique(ignoreRecord: true)
                            ->helperText('Identifiant unique pour le rôle (ex: admin, manager)')
                            ->regex('/^[a-z0-9_-]+$/'),

                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Permissions')
                    ->schema([
                        Forms\Components\CheckboxList::make('permissions')
                            ->label('Permissions')
                            ->options(function () {
                                $grouped = AdminPermission::getGrouped();
                                $options = [];
                                foreach ($grouped as $category => $permissions) {
                                    foreach ($permissions as $permission) {
                                        $options[$permission->value] = $permission->label();
                                    }
                                }
                                return $options;
                            })
                            ->columns(2)
                            ->gridDirection('row')
                            ->required()
                            ->helperText('Sélectionnez les permissions accordées à ce rôle'),
                    ]),

                Forms\Components\Section::make('Paramètres')
                    ->schema([
                        Forms\Components\TextInput::make('priority')
                            ->label('Priorité')
                            ->numeric()
                            ->default(1)
                            ->minValue(1)
                            ->maxValue(100)
                            ->helperText('Plus le nombre est élevé, plus le rôle a de priorité'),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Rôle actif')
                            ->default(true),

                        Forms\Components\Toggle::make('is_system_role')
                            ->label('Rôle système')
                            ->helperText('Les rôles système ne peuvent pas être supprimés')
                            ->disabled(fn (?AdminRole $record): bool => $record?->is_system_role ?? false),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nom')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('slug')
                    ->label('Identifiant')
                    ->searchable()
                    ->sortable()
                    ->fontFamily('mono'),

                Tables\Columns\TextColumn::make('description')
                    ->label('Description')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),

                Tables\Columns\TextColumn::make('users_count')
                    ->label('Utilisateurs')
                    ->counts('adminUsers')
                    ->sortable(),

                Tables\Columns\TextColumn::make('priority')
                    ->label('Priorité')
                    ->sortable()
                    ->badge()
                    ->color(fn (int $state): string => match(true) {
                        $state >= 90 => 'danger',
                        $state >= 70 => 'warning',
                        $state >= 50 => 'success',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_system_role')
                    ->label('Système')
                    ->boolean()
                    ->trueIcon('heroicon-o-lock-closed')
                    ->falseIcon('heroicon-o-lock-open'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Actif')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Créé le')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_system_role')
                    ->label('Rôle système'),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Actif'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn (AdminRole $record): bool => 
                        !$record->is_system_role || auth()->user()?->isSuperAdmin()
                    ),
                Tables\Actions\Action::make('toggle_active')
                    ->label(fn (AdminRole $record): string => $record->is_active ? 'Désactiver' : 'Activer')
                    ->icon(fn (AdminRole $record): string => $record->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn (AdminRole $record): string => $record->is_active ? 'danger' : 'success')
                    ->requiresConfirmation()
                    ->visible(fn (AdminRole $record): bool => 
                        !$record->is_system_role || auth()->user()?->isSuperAdmin()
                    )
                    ->action(function (AdminRole $record): void {
                        $record->update(['is_active' => !$record->is_active]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn (): bool => auth()->user()?->isSuperAdmin() ?? false),
                ]),
            ])
            ->defaultSort('priority', 'desc');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informations du rôle')
                    ->schema([
                        Infolists\Components\TextEntry::make('name')
                            ->label('Nom'),
                        Infolists\Components\TextEntry::make('slug')
                            ->label('Identifiant')
                            ->fontFamily('mono'),
                        Infolists\Components\TextEntry::make('description')
                            ->label('Description')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Paramètres')
                    ->schema([
                        Infolists\Components\TextEntry::make('priority')
                            ->label('Priorité')
                            ->badge(),
                        Infolists\Components\IconEntry::make('is_system_role')
                            ->label('Rôle système')
                            ->boolean(),
                        Infolists\Components\IconEntry::make('is_active')
                            ->label('Actif')
                            ->boolean(),
                        Infolists\Components\TextEntry::make('users_count')
                            ->label('Nombre d\'utilisateurs')
                            ->state(fn (AdminRole $record): int => $record->adminUsers()->count()),
                    ])
                    ->columns(4),

                Infolists\Components\Section::make('Permissions')
                    ->schema([
                        Infolists\Components\TextEntry::make('permissions_list')
                            ->label('Permissions accordées')
                            ->state(function (AdminRole $record): string {
                                if (empty($record->permissions)) {
                                    return 'Aucune permission';
                                }

                                $grouped = AdminPermission::getGrouped();
                                $result = [];

                                foreach ($grouped as $category => $permissions) {
                                    $categoryPermissions = [];
                                    foreach ($permissions as $permission) {
                                        if (in_array($permission->value, $record->permissions)) {
                                            $categoryPermissions[] = $permission->label();
                                        }
                                    }

                                    if (!empty($categoryPermissions)) {
                                        $result[] = "**{$category}:**\n• " . implode("\n• ", $categoryPermissions);
                                    }
                                }

                                return implode("\n\n", $result);
                            })
                            ->markdown()
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Audit')
                    ->schema([
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Créé le')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Modifié le')
                            ->dateTime(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAdminRoles::route('/'),
            'create' => Pages\CreateAdminRole::route('/create'),
            'view' => Pages\ViewAdminRole::route('/{record}'),
            'edit' => Pages\EditAdminRole::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->withCount('adminUsers');
    }
}
