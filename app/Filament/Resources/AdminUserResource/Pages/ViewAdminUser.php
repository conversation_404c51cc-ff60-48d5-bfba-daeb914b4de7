<?php

namespace App\Filament\Resources\AdminUserResource\Pages;

use App\Filament\Resources\AdminUserResource;
use App\Enums\AdminPermission;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewAdminUser extends ViewRecord
{
    protected static string $resource = AdminUserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn (): bool =>
                    auth()->user()?->isSuperAdmin() ||
                    (auth()->user()?->hasAdminPermission(AdminPermission::EDIT_ADMINS) ?? false)
                ),
            Actions\DeleteAction::make()
                ->visible(fn (): bool =>
                    auth()->user()?->isSuperAdmin() ||
                    (auth()->user()?->hasAdminPermission(AdminPermission::DELETE_ADMINS) ?? false)
                )
                ->requiresConfirmation()
                ->modalHeading('Supprimer cet utilisateur admin')
                ->modalDescription('Êtes-vous sûr de vouloir supprimer cet utilisateur admin ? Cette action est irréversible.')
                ->modalSubmitActionLabel('Supprimer'),
        ];
    }
}
