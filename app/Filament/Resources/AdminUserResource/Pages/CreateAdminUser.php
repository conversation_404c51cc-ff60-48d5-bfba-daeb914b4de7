<?php

namespace App\Filament\Resources\AdminUserResource\Pages;

use App\Filament\Resources\AdminUserResource;
use App\Models\User;
use App\Models\AdminUser;
use App\Services\UserInvitationService;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CreateAdminUser extends CreateRecord
{
    protected static string $resource = AdminUserResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        return DB::transaction(function () use ($data) {
            // Extraire les données utilisateur
            $userData = $data['user'] ?? [];
            $userData['is_admin'] = true;

            // Vérifier si on envoie une invitation
            $sendInvitation = $data['send_invitation'] ?? true;

            if ($sendInvitation) {
                // Si invitation ET pas de mot de passe fourni
                if (empty($userData['password'])) {
                    $userData['email_verification_token'] = Str::random(60);
                    $userData['password'] = \Illuminate\Support\Facades\Hash::make(Str::random(32)); // Mot de passe temporaire
                } else {
                    // Mot de passe fourni même avec invitation
                    $userData['password'] = \Illuminate\Support\Facades\Hash::make($userData['password']);
                    $userData['email_verified_at'] = now();
                }
            } else {
                // Si pas d'invitation, s'assurer qu'il y a un mot de passe
                if (empty($userData['password'])) {
                    throw new \Exception('Un mot de passe est requis si aucune invitation n\'est envoyée.');
                }
                $userData['password'] = \Illuminate\Support\Facades\Hash::make($userData['password']);
                $userData['email_verified_at'] = now(); // Marquer comme vérifié
            }

            // Créer l'utilisateur
            $user = User::create($userData);

            // Créer l'AdminUser
            $adminUser = AdminUser::create([
                'user_id' => $user->id,
                'role_id' => $data['role_id'],
                'permissions' => $data['permissions'] ?? [],
                'department' => $data['department'] ?? null,
                'access_level' => $data['access_level'] ?? 'read',
                'is_active' => $data['is_active'] ?? true,
                'notes' => $data['notes'] ?? null,
                'created_by' => auth()->id(),
            ]);

            // Envoyer l'invitation par email si demandé
            if ($sendInvitation) {
                app(UserInvitationService::class)->sendAdminInvitation($user, $adminUser);

                // Log pour debug
                \Log::info('Invitation admin envoyée', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'admin_user_id' => $adminUser->id,
                ]);
            }

            return $adminUser;
        });
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // S'assurer que les permissions sont un tableau
        if (isset($data['permissions']) && is_string($data['permissions'])) {
            $data['permissions'] = json_decode($data['permissions'], true) ?? [];
        }

        return $data;
    }
}
