<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BoutiqueReviewResource\Pages;
use App\Models\BoutiqueReview;
use App\Models\Marchand;
use App\Enums\AdminPermission;
use App\Enums\MarchandPermission;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class BoutiqueReviewResource extends Resource
{
    protected static ?string $model = BoutiqueReview::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    protected static ?string $navigationLabel = 'Avis Boutiques';

    protected static ?string $modelLabel = 'Avis Boutique';

    protected static ?string $pluralModelLabel = 'Avis Boutiques';

    protected static ?string $navigationGroup = 'Modération';

    protected static ?int $navigationSort = 3;

    public static function canViewAny(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Super admin peut tout faire
        if ($user->adminUser && $user->adminUser->access_level === 'super_admin') {
            return true;
        }

        // Vérification des permissions admin (seulement pour les non-super admin)
        if ($user->adminUser) {
            return $user->adminUser->hasPermission(AdminPermission::VIEW_REVIEWS) ||
                   $user->adminUser->hasPermission(AdminPermission::MODERATE_CONTENT);
        }

        return false;
    }

    public static function canCreate(): bool
    {
        return false; // Les avis sont créés par les clients, pas par l'admin
    }

    public static function canEdit(Model $record): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Super admin peut tout faire
        if ($user->adminUser && $user->adminUser->access_level === 'super_admin') {
            return true;
        }

        // Vérification des permissions admin (seulement pour les non-super admin)
        if ($user->adminUser) {
            return $user->adminUser->hasPermission(AdminPermission::MODERATE_CONTENT);
        }

        return false;
    }

    public static function canDelete(Model $record): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Super admin peut tout faire
        if ($user->adminUser && $user->adminUser->access_level === 'super_admin') {
            return true;
        }

        // Vérification des permissions admin (seulement pour les non-super admin)
        if ($user->adminUser) {
            return $user->adminUser->hasPermission(AdminPermission::DELETE_REVIEWS);
        }

        return false;
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();
        // Dans le panel admin, on affiche tous les avis
        // Pas de filtrage par marchand ici
        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informations de l\'avis')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nom du client')
                            ->disabled(),

                        Forms\Components\TextInput::make('email')
                            ->label('Email du client')
                            ->disabled(),

                        Forms\Components\Select::make('rating')
                            ->label('Note')
                            ->options([
                                1 => '1 étoile',
                                2 => '2 étoiles',
                                3 => '3 étoiles',
                                4 => '4 étoiles',
                                5 => '5 étoiles',
                            ])
                            ->disabled(),

                        Forms\Components\TextInput::make('title')
                            ->label('Titre')
                            ->disabled(),

                        Forms\Components\Textarea::make('comment')
                            ->label('Commentaire')
                            ->disabled()
                            ->rows(4),
                    ]),

                Forms\Components\Section::make('Modération')
                    ->schema([
                        Forms\Components\Select::make('is_approved')
                            ->label('Statut')
                            ->options([
                                true => 'Approuvé',
                                false => 'Rejeté',
                            ])
                            ->boolean()
                            ->visible(fn () =>
                                Auth::user()?->adminUser?->access_level === 'super_admin' ||
                                Auth::user()?->adminUser?->hasPermission(AdminPermission::MODERATE_CONTENT)
                            ),

                        Forms\Components\Textarea::make('report_reason')
                            ->label('Raison de modération')
                            ->rows(3)
                            ->visible(fn () =>
                                Auth::user()?->adminUser?->access_level === 'super_admin' ||
                                Auth::user()?->adminUser?->hasPermission(AdminPermission::MODERATE_CONTENT)
                            ),
                    ])
                    ->visible(fn () => Auth::user()?->adminUser),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('marchand.nomEntreprise')
                    ->label('Boutique')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('name')
                    ->label('Client')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('rating')
                    ->label('Note')
                    ->formatStateUsing(fn (int $state): string => str_repeat('⭐', $state))
                    ->sortable(),

                Tables\Columns\TextColumn::make('title')
                    ->label('Titre')
                    ->limit(30)
                    ->searchable(),

                Tables\Columns\TextColumn::make('comment')
                    ->label('Commentaire')
                    ->limit(50)
                    ->searchable(),

                Tables\Columns\IconColumn::make('is_approved')
                    ->label('Approuvé')
                    ->boolean(),

                Tables\Columns\TextColumn::make('likes')
                    ->label('👍')
                    ->sortable(),

                Tables\Columns\TextColumn::make('dislikes')
                    ->label('👎')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_verified')
                    ->label('Vérifié')
                    ->boolean(),

                Tables\Columns\IconColumn::make('is_reported')
                    ->label('Signalé')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('is_approved')
                    ->label('Statut')
                    ->options([
                        true => 'Approuvé',
                        false => 'Rejeté',
                    ])
                    ->placeholder('Tous les statuts'),

                SelectFilter::make('rating')
                    ->label('Note')
                    ->options([
                        1 => '1 étoile',
                        2 => '2 étoiles',
                        3 => '3 étoiles',
                        4 => '4 étoiles',
                        5 => '5 étoiles',
                    ]),

                SelectFilter::make('marchand_id')
                    ->label('Boutique')
                    ->options(Marchand::pluck('nomEntreprise', 'id')),

                Filter::make('verified')
                    ->label('Achats vérifiés')
                    ->query(fn (Builder $query): Builder => $query->where('is_verified', true)),

                Filter::make('reported')
                    ->label('Signalés')
                    ->query(fn (Builder $query): Builder => $query->where('is_reported', true)),

                Filter::make('with_images')
                    ->label('Avec images')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('images')),
            ])
            ->actions([
                Action::make('approve')
                    ->label('Approuver')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->action(function (BoutiqueReview $record) {
                        $record->update(['is_approved' => true]);
                        Notification::make()
                            ->title('Avis approuvé')
                            ->success()
                            ->send();
                    })
                    ->visible(fn (BoutiqueReview $record) =>
                        !$record->is_approved && (
                            Auth::user()?->adminUser?->access_level === 'super_admin' ||
                            Auth::user()?->adminUser?->hasPermission(AdminPermission::MODERATE_CONTENT)
                        )
                    ),

                Action::make('reject')
                    ->label('Rejeter')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->action(function (BoutiqueReview $record) {
                        $record->update(['is_approved' => false]);
                        Notification::make()
                            ->title('Avis rejeté')
                            ->success()
                            ->send();
                    })
                    ->visible(fn (BoutiqueReview $record) =>
                        $record->is_approved && (
                            Auth::user()?->adminUser?->access_level === 'super_admin' ||
                            Auth::user()?->adminUser?->hasPermission(AdminPermission::MODERATE_CONTENT)
                        )
                    ),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () =>
                            Auth::user()?->adminUser?->access_level === 'super_admin' ||
                            Auth::user()?->adminUser?->hasPermission(AdminPermission::DELETE_REVIEWS)
                        ),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBoutiqueReviews::route('/'),
            'view' => Pages\ViewBoutiqueReview::route('/{record}'),
            'edit' => Pages\EditBoutiqueReview::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        $user = Auth::user();

        // Dans le panel admin, afficher le nombre d'avis en attente de modération
        if ($user?->adminUser) {
            return static::getModel()::where('is_approved', false)->count();
        }

        return null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return static::getNavigationBadge() > 0 ? 'warning' : null;
    }
}
