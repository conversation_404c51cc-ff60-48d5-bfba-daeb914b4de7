<?php

namespace App\Filament\Marchand\Widgets;

use App\Models\MarchandAbonnement;
use Filament\Widgets\Widget;
use Illuminate\Support\Carbon;

class TrialAlertWidget extends Widget
{
    protected static string $view = 'filament.marchand.widgets.trial-alert';
    
    protected int | string | array $columnSpan = 'full';
    
    protected static ?int $sort = -1; // Afficher en premier

    public function getViewData(): array
    {
        $marchand = auth()->user()->marchand;
        
        if (!$marchand) {
            return ['showAlert' => false];
        }

        $subscription = MarchandAbonnement::where('marchand_id', $marchand->id)
            ->where('statut', 'actif')
            ->first();

        if (!$subscription || $subscription->type_abonnement !== 'trial') {
            return ['showAlert' => false];
        }

        $trialEndDate = $subscription->fin_periode_essai;
        $daysRemaining = now()->diffInDays($trialEndDate, false);
        
        // Ne pas afficher si le trial est expiré
        if ($daysRemaining < 0) {
            return ['showAlert' => false];
        }

        return [
            'showAlert' => true,
            'daysRemaining' => $daysRemaining,
            'trialEndDate' => $trialEndDate,
            'subscription' => $subscription,
            'isLastDay' => $daysRemaining <= 1,
            'isLastWeek' => $daysRemaining <= 7,
        ];
    }
}
