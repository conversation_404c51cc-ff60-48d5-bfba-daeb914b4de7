<?php

namespace App\Filament\Marchand\Widgets;

use App\Models\BoutiqueReview;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class BoutiqueReviewsStatsWidget extends BaseWidget
{
    protected static ?int $sort = 3;

    protected function getStats(): array
    {
        $user = Auth::user();
        
        if (!$user || !$user->marchandUser) {
            return [];
        }

        $marchandId = $user->marchandUser->marchand_id;
        
        $totalReviews = BoutiqueReview::where('marchand_id', $marchandId)->count();
        $approvedReviews = BoutiqueReview::where('marchand_id', $marchandId)
            ->where('is_approved', true)->count();
        $withoutResponse = BoutiqueReview::where('marchand_id', $marchandId)
            ->where('is_approved', true)
            ->whereNull('marchand_response')->count();
        $averageRating = BoutiqueReview::where('marchand_id', $marchandId)
            ->where('is_approved', true)->avg('rating');

        return [
            Stat::make('Total des avis', $totalReviews)
                ->description('Avis reçus')
                ->descriptionIcon('heroicon-m-star')
                ->color('primary')
                ->url(route('filament.marchand.resources.boutique-reviews.index')),

            Stat::make('Avis approuvés', $approvedReviews)
                ->description('Visibles publiquement')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success')
                ->url(route('filament.marchand.resources.boutique-reviews.index', ['activeTab' => 'approved'])),

            Stat::make('Sans réponse', $withoutResponse)
                ->description('Avis en attente de réponse')
                ->descriptionIcon('heroicon-m-chat-bubble-left-ellipsis')
                ->color($withoutResponse > 0 ? 'warning' : 'success')
                ->url(route('filament.marchand.resources.boutique-reviews.index', ['activeTab' => 'without_response'])),

            Stat::make('Note moyenne', number_format($averageRating ?? 0, 1) . '/5')
                ->description('Votre boutique')
                ->descriptionIcon('heroicon-m-star')
                ->color($this->getRatingColor($averageRating ?? 0)),
        ];
    }

    protected function getRatingColor(float $rating): string
    {
        if ($rating >= 4.5) {
            return 'success';
        } elseif ($rating >= 3.5) {
            return 'warning';
        } elseif ($rating >= 2.5) {
            return 'danger';
        } else {
            return 'gray';
        }
    }

    public static function canView(): bool
    {
        $user = Auth::user();
        
        if (!$user || !$user->marchandUser) {
            return false;
        }

        // Marchand propriétaire peut tout voir
        if ($user->marchandUser->access_level === 'owner') {
            return true;
        }

        // Vérification des permissions pour les autres
        return $user->marchandUser->hasPermission(\App\Enums\MarchandPermission::VIEW_REVIEWS);
    }
}
