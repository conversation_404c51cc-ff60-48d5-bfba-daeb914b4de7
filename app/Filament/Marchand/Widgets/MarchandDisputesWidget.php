<?php

namespace App\Filament\Marchand\Widgets;

use App\Models\Dispute;
use App\Models\DisputeMessage;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class MarchandDisputesWidget extends BaseWidget
{
    protected int | string | array $columnSpan = 'full';
    
    protected static ?int $sort = 6;

    protected static ?string $heading = '💬 Messages et Litiges Clients';

    protected static ?string $description = 'Répondez aux questions et litiges de vos clients';

    public function table(Table $table): Table
    {
        $marchandId = auth()->user()->marchands->first()->id ?? null;
        
        return $table
            ->query(
                Dispute::query()
                    ->where('marchand_id', $marchandId)
                    ->with(['client', 'commandePrincipale', 'sousCommande', 'messages' => function($query) {
                        $query->publics()->latest()->limit(1);
                    }])
                    ->whereIn('statut', ['ouvert', 'en_cours', 'attente_marchand'])
                    ->latest('created_at')
                    ->limit(15)
            )
            ->columns([
                Tables\Columns\TextColumn::make('numero_litige')
                    ->label('N° Litige')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('Numéro copié!')
                    ->copyMessageDuration(1500)
                    ->weight('bold')
                    ->color('primary'),
                
                Tables\Columns\BadgeColumn::make('priorite')
                    ->label('Priorité')
                    ->colors([
                        'gray' => 'basse',
                        'primary' => 'normale',
                        'warning' => 'haute',
                        'danger' => 'critique',
                    ])
                    ->icons([
                        'heroicon-m-minus' => 'basse',
                        'heroicon-m-equals' => 'normale',
                        'heroicon-m-plus' => 'haute',
                        'heroicon-m-exclamation-triangle' => 'critique',
                    ])
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'basse' => 'Basse',
                        'normale' => 'Normale',
                        'haute' => 'Haute',
                        'critique' => 'CRITIQUE',
                        default => ucfirst($state)
                    }),
                
                Tables\Columns\TextColumn::make('type_litige')
                    ->label('Type')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'produit_non_conforme' => 'Non conforme',
                        'produit_defectueux' => 'Défectueux',
                        'livraison_retard' => 'Retard livraison',
                        'livraison_non_recue' => 'Non reçu',
                        'remboursement' => 'Remboursement',
                        'service_client' => 'Service client',
                        'facturation' => 'Facturation',
                        'autre' => 'Autre',
                        default => ucfirst($state)
                    })
                    ->colors([
                        'warning' => ['produit_non_conforme', 'produit_defectueux'],
                        'danger' => ['livraison_retard', 'livraison_non_recue'],
                        'info' => ['remboursement', 'facturation'],
                        'primary' => ['service_client', 'autre'],
                    ]),
                
                Tables\Columns\TextColumn::make('sujet')
                    ->label('Sujet')
                    ->limit(50)
                    ->tooltip(function (Dispute $record): string {
                        return $record->sujet . "\n\n" . substr($record->description, 0, 200) . '...';
                    })
                    ->weight('bold'),
                
                Tables\Columns\TextColumn::make('client.nom')
                    ->label('Client')
                    ->formatStateUsing(function (Dispute $record): string {
                        return $record->client ? 
                            $record->client->prenom . ' ' . $record->client->nom : 
                            'Client supprimé';
                    })
                    ->icon('heroicon-m-user')
                    ->color('primary'),
                
                Tables\Columns\TextColumn::make('commandePrincipale.numero_commande')
                    ->label('Commande')
                    ->prefix('CP-')
                    ->color('info')
                    ->icon('heroicon-m-shopping-cart'),
                
                Tables\Columns\BadgeColumn::make('statut')
                    ->label('Statut')
                    ->colors([
                        'warning' => 'ouvert',
                        'primary' => 'en_cours',
                        'info' => 'attente_marchand',
                        'gray' => 'attente_client',
                        'success' => 'resolu',
                    ])
                    ->icons([
                        'heroicon-m-exclamation-circle' => 'ouvert',
                        'heroicon-m-arrow-path' => 'en_cours',
                        'heroicon-m-clock' => 'attente_marchand',
                        'heroicon-m-pause' => 'attente_client',
                        'heroicon-m-check-circle' => 'resolu',
                    ])
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'ouvert' => 'Nouveau',
                        'en_cours' => 'En cours',
                        'attente_marchand' => 'Votre réponse requise',
                        'attente_client' => 'Attente client',
                        'resolu' => 'Résolu',
                        default => ucfirst($state)
                    }),
                
                Tables\Columns\TextColumn::make('montant_conteste')
                    ->label('Montant')
                    ->money('FCFA')
                    ->sortable()
                    ->color('danger'),
                
                Tables\Columns\TextColumn::make('delai_restant')
                    ->label('Délai réponse')
                    ->badge()
                    ->color(function (Dispute $record): string {
                        if (!$record->date_limite_reponse) return 'gray';
                        $heures = now()->diffInHours($record->date_limite_reponse, false);
                        if ($heures < 0) return 'danger';
                        if ($heures < 24) return 'warning';
                        return 'success';
                    })
                    ->icon(function (Dispute $record): string {
                        if (!$record->date_limite_reponse) return 'heroicon-m-minus';
                        $heures = now()->diffInHours($record->date_limite_reponse, false);
                        if ($heures < 0) return 'heroicon-m-exclamation-triangle';
                        return 'heroicon-m-clock';
                    }),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ouvert le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('messages_non_lus')
                    ->label('💬')
                    ->getStateUsing(function (Dispute $record): bool {
                        return $record->messages()
                            ->publics()
                            ->nonLusPar('marchand')
                            ->exists();
                    })
                    ->boolean()
                    ->trueIcon('heroicon-s-chat-bubble-left-ellipsis')
                    ->falseIcon('heroicon-o-chat-bubble-left')
                    ->trueColor('danger')
                    ->falseColor('gray')
                    ->tooltip('Nouveaux messages'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('statut')
                    ->options([
                        'ouvert' => 'Nouveau',
                        'en_cours' => 'En cours',
                        'attente_marchand' => 'Votre réponse requise',
                        'attente_client' => 'Attente client',
                    ]),
                
                Tables\Filters\SelectFilter::make('type_litige')
                    ->label('Type')
                    ->options([
                        'produit_non_conforme' => 'Produit non conforme',
                        'produit_defectueux' => 'Produit défectueux',
                        'livraison_retard' => 'Retard de livraison',
                        'livraison_non_recue' => 'Livraison non reçue',
                        'remboursement' => 'Remboursement',
                        'service_client' => 'Service client',
                        'facturation' => 'Facturation',
                        'autre' => 'Autre',
                    ]),
                
                Tables\Filters\SelectFilter::make('priorite')
                    ->options([
                        'basse' => 'Basse',
                        'normale' => 'Normale',
                        'haute' => 'Haute',
                        'critique' => 'Critique',
                    ]),
                
                Tables\Filters\Filter::make('messages_non_lus')
                    ->label('Messages non lus')
                    ->query(function (Builder $query): Builder {
                        return $query->whereHas('messages', function($q) {
                            $q->publics()->nonLusPar('marchand');
                        });
                    })
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\Action::make('respond')
                    ->label('Répondre')
                    ->icon('heroicon-m-chat-bubble-left-right')
                    ->color('primary')
                    ->modalHeading(fn (Dispute $record): string => "💬 Répondre au litige {$record->numero_litige}")
                    ->modalWidth('6xl')
                    ->modalContent(fn (Dispute $record): \Illuminate\Contracts\View\View => 
                        view('filament.modals.marchand-dispute-chat', ['dispute' => $record])
                    )
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Fermer')
                    ->after(function (Dispute $record) {
                        // Marquer les messages comme lus
                        $record->messages()->publics()->nonLusPar('marchand')->update(['lu_par_marchand' => true]);
                    }),
                
                Tables\Actions\Action::make('quick_response')
                    ->label('Réponse rapide')
                    ->icon('heroicon-m-paper-airplane')
                    ->color('success')
                    ->form([
                        \Filament\Forms\Components\Select::make('template')
                            ->label('Modèle de réponse')
                            ->options([
                                'excuse_retard' => 'Excuses pour le retard',
                                'demande_info' => 'Demande d\'informations supplémentaires',
                                'solution_proposee' => 'Proposition de solution',
                                'remboursement_accepte' => 'Remboursement accepté',
                                'echange_propose' => 'Échange proposé',
                            ])
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                $templates = [
                                    'excuse_retard' => 'Bonjour, nous nous excusons sincèrement pour le retard de livraison. Nous mettons tout en œuvre pour résoudre cette situation rapidement.',
                                    'demande_info' => 'Bonjour, pour mieux vous aider, pourriez-vous nous fournir des informations supplémentaires concernant votre commande ?',
                                    'solution_proposee' => 'Bonjour, nous avons analysé votre demande et souhaitons vous proposer une solution adaptée.',
                                    'remboursement_accepte' => 'Bonjour, nous acceptons votre demande de remboursement. Le processus sera initié sous 24h.',
                                    'echange_propose' => 'Bonjour, nous vous proposons un échange de votre produit. Merci de nous confirmer votre accord.',
                                ];
                                $set('message', $templates[$state] ?? '');
                            }),
                        \Filament\Forms\Components\Textarea::make('message')
                            ->label('Message')
                            ->required()
                            ->rows(4),
                    ])
                    ->action(function (Dispute $record, array $data) {
                        DisputeMessage::create([
                            'dispute_id' => $record->id,
                            'auteur_type' => 'marchand',
                            'auteur_id' => auth()->user()->marchands->first()->id,
                            'auteur_nom' => auth()->user()->marchands->first()->nomEntreprise,
                            'message' => $data['message'],
                            'type_message' => 'message',
                            'interne' => false,
                        ]);
                        
                        $record->update([
                            'statut' => 'attente_client',
                            'date_premiere_reponse' => $record->date_premiere_reponse ?? now(),
                        ]);
                        
                        \Filament\Notifications\Notification::make()
                            ->title('Réponse envoyée')
                            ->success()
                            ->send();
                    }),
                
                Tables\Actions\Action::make('propose_solution')
                    ->label('Proposer solution')
                    ->icon('heroicon-m-light-bulb')
                    ->color('warning')
                    ->form([
                        \Filament\Forms\Components\Select::make('type_solution')
                            ->label('Type de solution')
                            ->options([
                                'remboursement_partiel' => 'Remboursement partiel',
                                'remboursement_total' => 'Remboursement total',
                                'echange_produit' => 'Échange du produit',
                                'avoir_boutique' => 'Avoir en boutique',
                                'reduction_prochaine_commande' => 'Réduction prochaine commande',
                                'compensation_financiere' => 'Compensation financière',
                            ])
                            ->required(),
                        \Filament\Forms\Components\TextInput::make('montant_propose')
                            ->label('Montant proposé (FCFA)')
                            ->numeric()
                            ->default(0),
                        \Filament\Forms\Components\Textarea::make('details_solution')
                            ->label('Détails de la solution')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function (Dispute $record, array $data) {
                        $message = "🔧 **Solution proposée** : " . match($data['type_solution']) {
                            'remboursement_partiel' => 'Remboursement partiel',
                            'remboursement_total' => 'Remboursement total',
                            'echange_produit' => 'Échange du produit',
                            'avoir_boutique' => 'Avoir en boutique',
                            'reduction_prochaine_commande' => 'Réduction prochaine commande',
                            'compensation_financiere' => 'Compensation financière',
                            default => $data['type_solution']
                        };
                        
                        if ($data['montant_propose'] > 0) {
                            $message .= "\n💰 **Montant** : " . number_format($data['montant_propose'], 0, ',', ' ') . " FCFA";
                        }
                        
                        $message .= "\n\n📝 **Détails** :\n" . $data['details_solution'];
                        
                        DisputeMessage::create([
                            'dispute_id' => $record->id,
                            'auteur_type' => 'marchand',
                            'auteur_id' => auth()->user()->marchands->first()->id,
                            'auteur_nom' => auth()->user()->marchands->first()->nomEntreprise,
                            'message' => $message,
                            'type_message' => 'solution_proposee',
                            'interne' => false,
                        ]);
                        
                        $record->update([
                            'statut' => 'attente_client',
                            'solution_souhaitee' => $data['details_solution'],
                            'montant_compensation' => $data['montant_propose'],
                        ]);
                        
                        \Filament\Notifications\Notification::make()
                            ->title('Solution proposée')
                            ->success()
                            ->send();
                    }),
            ])
            ->emptyStateHeading('🎉 Aucun litige en cours')
            ->emptyStateDescription('Tous vos clients sont satisfaits ! Continuez votre excellent travail.')
            ->emptyStateIcon('heroicon-o-face-smile')
            ->defaultSort('created_at', 'desc')
            ->striped()
            ->paginated([15, 25, 50])
            ->poll('60s'); // Actualisation automatique toutes les 60 secondes
    }
}
