<?php

namespace App\Filament\Marchand\Widgets;

use App\Services\CommandeAdapterService;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\MarchandPermission;
use App\Helpers\CurrencyHelper;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class MarchandStatsOverview extends BaseWidget
{
    use HasPermissionChecks;

    public static function canView(): bool
    {
        return static::canViewMarchand(MarchandPermission::VIEW_ANALYTICS);
    }

    protected function getStats(): array
    {
        $marchandId = auth()->user()->marchand?->id;

        if (!$marchandId) {
            return [];
        }

        $commandeService = new CommandeAdapterService();
        $stats = $commandeService->getStatistiquesMarchand($marchandId);

        // Calculer l'évolution par rapport au mois dernier
        $evolutionCommandes = $stats['commandes_mois_dernier'] > 0
            ? round((($stats['commandes_ce_mois'] - $stats['commandes_mois_dernier']) / $stats['commandes_mois_dernier']) * 100, 1)
            : 0;

        $evolutionRevenus = $stats['revenus_mois_dernier'] > 0
            ? round((($stats['revenus_ce_mois'] - $stats['revenus_mois_dernier']) / $stats['revenus_mois_dernier']) * 100, 1)
            : 0;

        return [
            Stat::make('Total commandes', $stats['total_commandes'])
                ->description('Toutes vos sous-commandes')
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('primary'),

            Stat::make('Commandes ce mois', $stats['commandes_ce_mois'])
                ->description($evolutionCommandes >= 0 ? '+' . $evolutionCommandes . '% vs mois dernier' : $evolutionCommandes . '% vs mois dernier')
                ->descriptionIcon($evolutionCommandes >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($evolutionCommandes >= 0 ? 'success' : 'danger')
                ->chart([
                    $stats['commandes_mois_dernier'],
                    $stats['commandes_ce_mois']
                ])
                ->chartColor($evolutionCommandes >= 0 ? 'success' : 'danger'),

            Stat::make('En attente', $stats['commandes_en_attente'])
                ->description('À confirmer')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('Confirmées', $stats['commandes_confirmees'])
                ->description('En préparation')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('info'),

            Stat::make('Expédiées', $stats['commandes_expediees'])
                ->description('En transit')
                ->descriptionIcon('heroicon-m-truck')
                ->color('primary'),

            Stat::make('Livrées', $stats['commandes_livrees'])
                ->description('Complétées')
                ->descriptionIcon('heroicon-m-check-badge')
                ->color('success'),

            Stat::make('Revenus ce mois', CurrencyHelper::format($stats['revenus_ce_mois']))
                ->description($evolutionRevenus >= 0 ? '+' . $evolutionRevenus . '% vs mois dernier' : $evolutionRevenus . '% vs mois dernier')
                ->descriptionIcon($evolutionRevenus >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($evolutionRevenus >= 0 ? 'success' : 'danger'),

            Stat::make('Versements en attente', CurrencyHelper::format($stats['versements_en_attente']))
                ->description('À recevoir')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('warning'),
        ];
    }
}
