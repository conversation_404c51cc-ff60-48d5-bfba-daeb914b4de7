<?php

namespace App\Filament\Marchand\Resources;

use App\Filament\Marchand\Resources\ProfileResource\Pages;
use App\Models\Marchand;
use App\Models\User;
use App\Models\MerchantValidation;
use App\Models\MerchantValidationDocument;
use App\Models\MarchandDocument;
use App\Services\AvatarService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class ProfileResource extends Resource
{
    protected static ?string $model = Marchand::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';

    protected static ?string $navigationLabel = 'Mon Profil';

    protected static ?int $navigationSort = 10;

    protected static ?string $slug = 'mon-profil';

    protected static bool $shouldRegisterNavigation = true;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Logo de la boutique')
                            ->schema([
                                Forms\Components\FileUpload::make('logo')
                                    ->label('Logo de votre boutique')
                                    ->image()
                                    ->disk('public')
                                    ->directory('avatars/marchand')
                                    ->visibility('public')
                                    ->imageResizeMode('cover')
                                    ->imageCropAspectRatio('1:1')
                                    ->imageResizeTargetWidth('300')
                                    ->imageResizeTargetHeight('300')
                                    ->maxSize(2048) // 2MB max
                                    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                                    ->helperText('Formats acceptés: JPEG, PNG, WebP. Taille maximale: 2MB. Recommandé: 300x300px.')
                                    ->columnSpanFull(),
                            ])
                            ->collapsible(),

                        Forms\Components\Section::make('Informations de l\'entreprise')
                            ->schema([
                                Forms\Components\TextInput::make('nomEntreprise')
                                    ->label('Nom de l\'entreprise')
                                    ->required()
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('idFiscal')
                                    ->label('Numéro d\'identification fiscale')
                                    ->maxLength(50),
                            ]),

                        Forms\Components\Section::make('Informations bancaires')
                            ->schema([
                                Forms\Components\TextInput::make('banqueNom')
                                    ->label('Nom de la banque')
                                    ->maxLength(100),

                                Forms\Components\TextInput::make('banqueNumeroCompte')
                                    ->label('Numéro de compte')
                                    ->maxLength(50),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Photo de profil')
                            ->schema([
                                Forms\Components\FileUpload::make('user.avatar')
                                    ->label('Photo de profil')
                                    ->image()
                                    ->disk('public')
                                    ->directory('avatars/user')
                                    ->visibility('public')
                                    ->imageResizeMode('cover')
                                    ->imageCropAspectRatio('1:1')
                                    ->imageResizeTargetWidth('300')
                                    ->imageResizeTargetHeight('300')
                                    ->maxSize(2048) // 2MB max
                                    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                                    ->helperText('Formats acceptés: JPEG, PNG, WebP. Taille maximale: 2MB.')
                                    ->columnSpanFull(),
                            ])
                            ->collapsible(),

                        Forms\Components\Section::make('Compte utilisateur')
                            ->relationship('user')
                            ->schema([
                                Forms\Components\TextInput::make('email')
                                    ->label('Email')
                                    ->email()
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true, table: 'users', column: 'email')
                                    ->disabled(),

                                Forms\Components\TextInput::make('password')
                                    ->label('Nouveau mot de passe')
                                    ->password()
                                    ->dehydrated(fn (?string $state): bool => filled($state))
                                    ->dehydrateStateUsing(fn (string $state): string => Hash::make($state))
                                    ->live(debounce: 500)
                                    ->same('passwordConfirmation'),

                                Forms\Components\TextInput::make('passwordConfirmation')
                                    ->label('Confirmer le mot de passe')
                                    ->password()
                                    ->dehydrated(false),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                // Section Logo et informations de base
                Infolists\Components\Section::make('Informations de la boutique')
                    ->schema([
                        Infolists\Components\Split::make([
                            Infolists\Components\Group::make([
                                Infolists\Components\ImageEntry::make('logo')
                                    ->label('Logo de la boutique')
                                    ->disk('public')
                                    ->height(150)
                                    ->width(150)
                                    ->defaultImageUrl(fn ($record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->nomEntreprise) . '&background=3b82f6&color=ffffff&size=300')
                                    ->extraAttributes(['class' => 'rounded-lg']),
                            ])->grow(false),

                            Infolists\Components\Group::make([
                                Infolists\Components\TextEntry::make('nomEntreprise')
                                    ->label('Nom de l\'entreprise')
                                    ->size(Infolists\Components\TextEntry\TextEntrySize::Large)
                                    ->weight('bold'),

                                Infolists\Components\TextEntry::make('slug')
                                    ->label('URL de la boutique')
                                    ->prefix('/boutique/')
                                    ->copyable()
                                    ->copyMessage('URL copiée!')
                                    ->color('primary'),

                                Infolists\Components\TextEntry::make('statut_validation')
                                    ->label('Statut de validation')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'valide' => 'success',
                                        'en_attente' => 'warning',
                                        'rejete' => 'danger',
                                        default => 'gray',
                                    }),

                                Infolists\Components\TextEntry::make('type_business')
                                    ->label('Type de business')
                                    ->formatStateUsing(fn (string $state): string => match ($state) {
                                        'individuel' => 'Entrepreneur individuel',
                                        'entreprise' => 'Entreprise',
                                        'cooperative' => 'Coopérative',
                                        'grande_entreprise' => 'Grande entreprise',
                                        default => $state,
                                    }),
                            ])->grow(),
                        ]),
                    ])
                    ->columns(1),

                // Section Informations détaillées
                Infolists\Components\Section::make('Informations détaillées')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('idFiscal')
                                    ->label('Numéro fiscal'),

                                Infolists\Components\TextEntry::make('telephone_principal')
                                    ->label('Téléphone principal'),

                                Infolists\Components\TextEntry::make('email_business')
                                    ->label('Email professionnel'),

                                Infolists\Components\TextEntry::make('site_web')
                                    ->label('Site web')
                                    ->url(fn ($state) => $state ? (str_starts_with($state, 'http') ? $state : 'https://' . $state) : null)
                                    ->openUrlInNewTab(),

                                Infolists\Components\TextEntry::make('pays_business')
                                    ->label('Pays'),

                                Infolists\Components\TextEntry::make('ville_business')
                                    ->label('Ville'),
                            ]),
                    ])
                    ->collapsible(),

                // Section Informations bancaires
                Infolists\Components\Section::make('Informations bancaires')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('banqueNom')
                                    ->label('Nom de la banque'),

                                Infolists\Components\TextEntry::make('banqueNumeroCompte')
                                    ->label('Numéro de compte')
                                    ->formatStateUsing(fn ($state) => $state ? '****' . substr($state, -4) : null),

                                Infolists\Components\TextEntry::make('nom_titulaire_compte')
                                    ->label('Titulaire du compte'),

                                Infolists\Components\TextEntry::make('methode_paiement_preferee')
                                    ->label('Méthode de paiement préférée'),
                            ]),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nomEntreprise')
                    ->label('Nom de l\'entreprise'),

                Tables\Columns\TextColumn::make('user.email')
                    ->label('Email'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ViewProfile::route('/'),
            'edit' => Pages\EditProfile::route('/edit'),
        ];
    }

    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        // Ne montrer que le profil du marchand connecté
        return parent::getEloquentQuery()
            ->where('user_id', auth()->id());
    }
}
