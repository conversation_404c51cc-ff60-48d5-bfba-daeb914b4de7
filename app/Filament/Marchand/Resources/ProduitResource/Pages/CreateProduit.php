<?php

namespace App\Filament\Marchand\Resources\ProduitResource\Pages;

use App\Filament\Marchand\Resources\ProduitResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class CreateProduit extends CreateRecord
{
    protected static string $resource = ProduitResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ajouter automatiquement l'ID du marchand connecté
        $data['marchand_id'] = Auth::user()->marchands->first()->id ?? null;

        // Définir la date de création
        $data['creeLe'] = now();

        // Définir la date de mise à jour
        $data['misAJourLe'] = now();

        // Gérer les champs traduisibles (nom et description)
        if (isset($data['nom']) && is_array($data['nom'])) {
            // Les champs traduisibles sont déjà au bon format pour Spatie\Translatable
            // Pas besoin de conversion JSON ici, le trait s'en charge
        }

        if (isset($data['description']) && is_array($data['description'])) {
            // Les champs traduisibles sont déjà au bon format pour Spatie\Translatable
            // Pas besoin de conversion JSON ici, le trait s'en charge
        }

        // S'assurer que le slug est défini
        if (empty($data['slug']) && isset($data['nom']['fr'])) {
            $data['slug'] = \Illuminate\Support\Str::slug($data['nom']['fr']);
        }

        // Valeurs par défaut pour les champs requis
        if (!isset($data['stock']) || $data['stock'] === null) {
            $data['stock'] = 0;
        }

        if (!isset($data['marque']) || $data['marque'] === null) {
            $data['marque'] = null; // Peut être null
        }

        // S'assurer que les champs JSON sont bien formatés
        if (!isset($data['attributs'])) {
            $data['attributs'] = [];
        }

        if (!isset($data['images'])) {
            $data['images'] = [];
        }

        return $data;
    }

    protected function afterCreate(): void
    {
        // Définir une variable de session pour indiquer qu'un produit vient d'être créé
        Session::put('product_created', true);

        // Afficher une notification de succès
        \Filament\Notifications\Notification::make()
            ->title('Produit créé')
            ->body('Le produit a été créé avec succès. Vous pouvez maintenant ajouter des variantes.')
            ->success()
            ->send();
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('edit', ['record' => $this->record->id]);
    }
}
