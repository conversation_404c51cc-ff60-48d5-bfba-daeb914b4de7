<?php

namespace App\Filament\Marchand\Resources\MarchandTeamResource\Pages;

use App\Filament\Marchand\Resources\MarchandTeamResource;
use App\Models\User;
use App\Models\MarchandUser;
use App\Services\UserInvitationService;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CreateMarchandTeam extends CreateRecord
{
    protected static string $resource = MarchandTeamResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        return DB::transaction(function () use ($data) {
            $marchandId = auth()->user()->marchand->id;
            $sendInvitation = $data['send_invitation'] ?? true;
            
            // Extraire les données utilisateur
            $userData = $data['user'] ?? [];

            if ($sendInvitation) {
                // Si invitation ET pas de mot de passe fourni
                if (empty($userData['password'])) {
                    $userData['email_verification_token'] = Str::random(60);
                    $userData['password'] = \Illuminate\Support\Facades\Hash::make(Str::random(32)); // Mot de passe temporaire
                } else {
                    // Mot de passe fourni même avec invitation
                    $userData['password'] = \Illuminate\Support\Facades\Hash::make($userData['password']);
                    $userData['email_verified_at'] = now();
                }
            } else {
                // Si pas d'invitation, s'assurer qu'il y a un mot de passe
                if (empty($userData['password'])) {
                    throw new \Exception('Un mot de passe est requis si aucune invitation n\'est envoyée.');
                }
                $userData['password'] = \Illuminate\Support\Facades\Hash::make($userData['password']);
                $userData['email_verified_at'] = now(); // Marquer comme vérifié
            }

            // Créer l'utilisateur
            $user = User::create($userData);

            // Créer le MarchandUser
            $marchandUser = MarchandUser::create([
                'user_id' => $user->id,
                'marchand_id' => $marchandId,
                'role_id' => $data['role_id'],
                'permissions' => $data['permissions'] ?? [],
                'access_level' => $data['access_level'] ?? 'employee',
                'is_active' => $data['is_active'] ?? true,
                'notes' => $data['notes'] ?? null,
                'invited_by' => auth()->id(),
            ]);

            // Envoyer l'invitation par email si demandé
            if ($sendInvitation) {
                app(UserInvitationService::class)->sendMarchandTeamInvitation(
                    $user,
                    $marchandUser,
                    auth()->user()->marchand,
                    auth()->user()
                );

                // Log pour debug
                \Log::info('Invitation marchand envoyée', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'marchand_user_id' => $marchandUser->id,
                    'marchand_id' => auth()->user()->marchand->id,
                ]);
            }

            return $marchandUser;
        });
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // S'assurer que les permissions sont un tableau
        if (isset($data['permissions']) && is_string($data['permissions'])) {
            $data['permissions'] = json_decode($data['permissions'], true) ?? [];
        }

        return $data;
    }
}
