<?php

namespace App\Filament\Marchand\Resources\MarchandTeamResource\Pages;

use App\Filament\Marchand\Resources\MarchandTeamResource;
use App\Enums\MarchandPermission;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMarchandTeam extends ListRecords
{
    protected static string $resource = MarchandTeamResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->visible(fn (): bool =>
                    auth()->user()?->marchand &&
                    (auth()->user()->id === auth()->user()->marchand->user_id ||
                     (auth()->user()?->hasMarchandPermission(
                         MarchandPermission::INVITE_USERS,
                         auth()->user()?->marchand
                     ) ?? false))
                ),
        ];
    }
}
