<?php

namespace App\Filament\Marchand\Resources\MarchandTeamResource\Pages;

use App\Filament\Marchand\Resources\MarchandTeamResource;
use App\Enums\MarchandPermission;
use App\Services\UserInvitationService;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewMarchandTeam extends ViewRecord
{
    protected static string $resource = MarchandTeamResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn (): bool =>
                    auth()->user()?->marchand &&
                    (auth()->user()->id === auth()->user()->marchand->user_id ||
                     (auth()->user()?->hasMarchandPermission(
                         MarchandPermission::MANAGE_TEAM,
                         auth()->user()?->marchand
                     ) ?? false))
                ),
            Actions\Action::make('resend_invitation')
                ->label('Renvoyer invitation')
                ->icon('heroicon-o-envelope')
                ->color('warning')
                ->visible(fn (): bool =>
                    $this->record->invitation_token &&
                    auth()->user()?->marchand &&
                    (auth()->user()->id === auth()->user()->marchand->user_id ||
                     (auth()->user()?->hasMarchandPermission(
                         MarchandPermission::MANAGE_TEAM,
                         auth()->user()?->marchand
                     ) ?? false))
                )
                ->requiresConfirmation()
                ->modalHeading('Renvoyer l\'invitation')
                ->modalDescription('Êtes-vous sûr de vouloir renvoyer l\'invitation à ce membre ?')
                ->modalSubmitActionLabel('Renvoyer')
                ->action(function (): void {
                    app(UserInvitationService::class)->resendMarchandInvitation($this->record);
                    $this->notify('success', 'Invitation renvoyée avec succès.');
                }),
            Actions\DeleteAction::make()
                ->visible(fn (): bool =>
                    auth()->user()?->marchand &&
                    (auth()->user()->id === auth()->user()->marchand->user_id ||
                     (auth()->user()?->hasMarchandPermission(
                         MarchandPermission::REMOVE_USERS,
                         auth()->user()?->marchand
                     ) ?? false))
                )
                ->requiresConfirmation()
                ->modalHeading('Retirer ce membre de l\'équipe')
                ->modalDescription('Êtes-vous sûr de vouloir retirer ce membre de l\'équipe ? Cette action est irréversible.')
                ->modalSubmitActionLabel('Retirer'),
        ];
    }
}
