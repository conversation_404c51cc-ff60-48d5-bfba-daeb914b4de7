<?php

namespace App\Filament\Marchand\Resources\ProfileResource\Pages;

use App\Filament\Marchand\Resources\ProfileResource;
use App\Models\MerchantValidation;
use App\Models\MerchantValidationDocument;
use App\Models\MarchandDocument;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Storage;

class ViewProfile extends ViewRecord
{
    protected static string $resource = ProfileResource::class;

    public function mount($record = null): void
    {
        // Récupérer l'ID du marchand connecté
        $marchandId = auth()->user()->marchand?->id ?? null;

        if (!$marchandId) {
            abort(404, 'Aucun profil marchand trouvé pour cet utilisateur.');
        }

        // Appeler le mount parent avec l'ID du marchand
        parent::mount($marchandId);
    }

    public function getTitle(): string|Htmlable
    {
        return 'Mon profil complet';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('Modifier mon profil')
                ->icon('heroicon-o-pencil'),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                // Section Logo et informations de base
                Infolists\Components\Section::make('Informations de la boutique')
                    ->schema([
                        Infolists\Components\Split::make([
                            Infolists\Components\Group::make([
                                Infolists\Components\ImageEntry::make('logo')
                                    ->label('Logo de la boutique')
                                    ->disk('public')
                                    ->height(150)
                                    ->width(150)
                                    ->defaultImageUrl(fn ($record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->nomEntreprise) . '&background=3b82f6&color=ffffff&size=300')
                                    ->extraAttributes(['class' => 'rounded-lg']),
                            ])->grow(false),

                            Infolists\Components\Group::make([
                                Infolists\Components\TextEntry::make('nomEntreprise')
                                    ->label('Nom de l\'entreprise')
                                    ->size(Infolists\Components\TextEntry\TextEntrySize::Large)
                                    ->weight('bold'),

                                Infolists\Components\TextEntry::make('slug')
                                    ->label('URL de la boutique')
                                    ->prefix('/boutique/')
                                    ->copyable()
                                    ->copyMessage('URL copiée!')
                                    ->color('primary'),

                                Infolists\Components\TextEntry::make('statut_validation')
                                    ->label('Statut de validation')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'valide' => 'success',
                                        'en_attente' => 'warning',
                                        'rejete' => 'danger',
                                        default => 'gray',
                                    }),

                                Infolists\Components\TextEntry::make('type_business')
                                    ->label('Type de business')
                                    ->formatStateUsing(fn (?string $state): string => match ($state) {
                                        'individuel' => 'Entrepreneur individuel',
                                        'entreprise' => 'Entreprise',
                                        'cooperative' => 'Coopérative',
                                        'grande_entreprise' => 'Grande entreprise',
                                        default => $state ?? 'Non défini',
                                    }),
                            ])->grow(),
                        ]),
                    ])
                    ->columns(1),

                // Section Informations détaillées
                Infolists\Components\Section::make('Informations détaillées')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('idFiscal')
                                    ->label('Numéro fiscal')
                                    ->placeholder('Non renseigné'),

                                Infolists\Components\TextEntry::make('telephone_principal')
                                    ->label('Téléphone principal')
                                    ->placeholder('Non renseigné'),

                                Infolists\Components\TextEntry::make('email_business')
                                    ->label('Email professionnel')
                                    ->placeholder('Non renseigné'),

                                Infolists\Components\TextEntry::make('site_web')
                                    ->label('Site web')
                                    ->url(fn ($state) => $state ? (str_starts_with($state, 'http') ? $state : 'https://' . $state) : null)
                                    ->openUrlInNewTab()
                                    ->placeholder('Non renseigné'),

                                Infolists\Components\TextEntry::make('pays_business')
                                    ->label('Pays')
                                    ->placeholder('Non renseigné'),

                                Infolists\Components\TextEntry::make('ville_business')
                                    ->label('Ville')
                                    ->placeholder('Non renseigné'),
                            ]),
                    ])
                    ->collapsible(),

                // Section Informations bancaires
                Infolists\Components\Section::make('Informations bancaires')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('banqueNom')
                                    ->label('Nom de la banque')
                                    ->placeholder('Non renseigné'),

                                Infolists\Components\TextEntry::make('banqueNumeroCompte')
                                    ->label('Numéro de compte')
                                    ->formatStateUsing(fn ($state) => $state ? '****' . substr($state, -4) : 'Non renseigné'),

                                Infolists\Components\TextEntry::make('nom_titulaire_compte')
                                    ->label('Titulaire du compte')
                                    ->placeholder('Non renseigné'),

                                Infolists\Components\TextEntry::make('methode_paiement_preferee')
                                    ->label('Méthode de paiement préférée')
                                    ->placeholder('Non renseigné'),
                            ]),
                    ])
                    ->collapsible(),

                // Section Documents de validation
                Infolists\Components\Section::make('Documents de validation')
                    ->headerActions([
                        Infolists\Components\Actions\Action::make('download_all_validation')
                            ->label('Télécharger tous les documents')
                            ->icon('heroicon-m-arrow-down-tray')
                            ->color('primary')
                            ->url(function ($record) {
                                $validation = MerchantValidation::where('user_id', $record->user_id)->first();
                                if ($validation && $validation->documents->count() > 0) {
                                    return route('marchand.merchant-validation.download-all-documents', ['validation' => $validation->id]);
                                }
                                return null;
                            })
                            ->openUrlInNewTab()
                            ->visible(function ($record) {
                                $validation = MerchantValidation::where('user_id', $record->user_id)->first();
                                return $validation && $validation->documents->count() > 0;
                            }),
                    ])
                    ->schema([
                        Infolists\Components\Group::make()
                            ->schema(function ($record) {
                                $validation = MerchantValidation::where('user_id', $record->user_id)->first();

                                if (!$validation || $validation->documents->count() === 0) {
                                    return [
                                        Infolists\Components\TextEntry::make('no_documents')
                                            ->label('')
                                            ->state('Aucun document de validation n\'a encore été soumis.')
                                            ->color('gray')
                                            ->icon('heroicon-o-document')
                                    ];
                                }

                                return [
                                    Infolists\Components\RepeatableEntry::make('validation_documents')
                                        ->label('')
                                        ->state(function ($record) use ($validation) {
                                            return $validation->documents->map(function ($document) use ($validation) {
                                                return [
                                                    'id' => $document->id,
                                                    'type' => $document->getReadableType(),
                                                    'status' => $document->status,
                                                    'file_size' => $document->getReadableFileSize(),
                                                    'uploaded_at' => $document->created_at?->format('d/m/Y H:i'),
                                                    'original_name' => $document->original_name,
                                                    'mime_type' => $document->mime_type,
                                                    'download_url' => $document->file_path ? route('marchand.merchant-validation.download-document', [
                                                        'validation' => $validation->id,
                                                        'document' => $document->id
                                                    ]) : null,
                                                    'view_url' => $document->file_path && in_array(strtolower(pathinfo($document->original_name, PATHINFO_EXTENSION)), ['pdf', 'jpg', 'jpeg', 'png', 'gif'])
                                                        ? route('marchand.merchant-validation.view-document', [
                                                            'validation' => $validation->id,
                                                            'document' => $document->id
                                                        ]) : null,
                                                ];
                                            })->toArray();
                                        })
                            ->schema([
                                Infolists\Components\Grid::make(4)
                                    ->schema([
                                        Infolists\Components\TextEntry::make('type')
                                            ->label('Type de document')
                                            ->weight('bold'),

                                        Infolists\Components\TextEntry::make('status')
                                            ->label('Statut')
                                            ->badge()
                                            ->color(fn (string $state): string => match ($state) {
                                                'VALIDE' => 'success',
                                                'EN_ATTENTE' => 'warning',
                                                'REJETE' => 'danger',
                                                default => 'gray',
                                            }),

                                        Infolists\Components\TextEntry::make('file_size')
                                            ->label('Taille'),

                                        Infolists\Components\TextEntry::make('uploaded_at')
                                            ->label('Uploadé le'),
                                    ]),

                                Infolists\Components\Actions::make([
                                    Infolists\Components\Actions\Action::make('view')
                                        ->label('Visualiser')
                                        ->icon('heroicon-m-eye')
                                        ->color('info')
                                        ->url(function ($record, $state) {
                                            return is_array($state) ? ($state['view_url'] ?? null) : null;
                                        })
                                        ->openUrlInNewTab()
                                        ->visible(function ($record, $state) {
                                            return is_array($state) && !empty($state['view_url']);
                                        }),

                                    Infolists\Components\Actions\Action::make('download')
                                        ->label('Télécharger')
                                        ->icon('heroicon-m-arrow-down-tray')
                                        ->color('success')
                                        ->url(function ($record, $state) {
                                            return is_array($state) ? ($state['download_url'] ?? null) : null;
                                        })
                                        ->openUrlInNewTab()
                                        ->visible(function ($record, $state) {
                                            return is_array($state) && !empty($state['download_url']);
                                        }),
                                ]),
                                        ])
                                        ->contained(false),
                                ];
                            }),
                    ])
                    ->collapsible(),

                // Section Documents généraux
                Infolists\Components\Section::make('Documents de la boutique')
                    ->schema([
                        Infolists\Components\Group::make()
                            ->schema(function ($record) {
                                if ($record->documents->count() === 0) {
                                    return [
                                        Infolists\Components\TextEntry::make('no_general_documents')
                                            ->label('')
                                            ->state('Aucun document général n\'a encore été ajouté à votre boutique.')
                                            ->color('gray')
                                            ->icon('heroicon-o-folder')
                                    ];
                                }

                                return [
                                    Infolists\Components\RepeatableEntry::make('general_documents')
                                        ->label('')
                                        ->state(function ($record) {
                                            // Récupérer les documents généraux du marchand
                                            return $record->documents->map(function ($document) {
                                                return [
                                                    'id' => $document->id,
                                                    'type' => $document->type_document,
                                                    'nom_original' => $document->nom_original,
                                                    'description' => $document->description,
                                                    'file_size' => $document->getReadableFileSize(),
                                                    'uploaded_at' => $document->created_at?->format('d/m/Y H:i'),
                                                    'est_crypte' => $document->est_crypte,
                                                    'download_url' => route('seller.download-document', ['documentId' => $document->id]),
                                                    'view_url' => !$document->est_crypte && in_array(strtolower($document->extension), ['pdf', 'jpg', 'jpeg', 'png', 'gif'])
                                                        ? Storage::disk('public')->url($document->chemin_fichier) : null,
                                                ];
                                            })->toArray();
                                        })
                                        ->schema([
                                Infolists\Components\Grid::make(4)
                                    ->schema([
                                        Infolists\Components\TextEntry::make('type')
                                            ->label('Type de document')
                                            ->weight('bold'),

                                        Infolists\Components\TextEntry::make('nom_original')
                                            ->label('Nom du fichier'),

                                        Infolists\Components\TextEntry::make('file_size')
                                            ->label('Taille'),

                                        Infolists\Components\TextEntry::make('uploaded_at')
                                            ->label('Uploadé le'),
                                    ]),

                                Infolists\Components\TextEntry::make('description')
                                    ->label('Description')
                                    ->placeholder('Aucune description')
                                    ->columnSpanFull(),

                                Infolists\Components\Actions::make([
                                    Infolists\Components\Actions\Action::make('view')
                                        ->label('Visualiser')
                                        ->icon('heroicon-m-eye')
                                        ->color('info')
                                        ->url(function ($record, $state) {
                                            return is_array($state) ? ($state['view_url'] ?? null) : null;
                                        })
                                        ->openUrlInNewTab()
                                        ->visible(function ($record, $state) {
                                            return is_array($state) && !empty($state['view_url']);
                                        }),

                                    Infolists\Components\Actions\Action::make('download')
                                        ->label('Télécharger')
                                        ->icon('heroicon-m-arrow-down-tray')
                                        ->color('success')
                                        ->url(function ($record, $state) {
                                            return is_array($state) ? ($state['download_url'] ?? null) : null;
                                        })
                                        ->openUrlInNewTab(),
                                ]),
                                        ])
                                        ->contained(false),
                                ];
                            }),
                    ])
                    ->collapsible(),

                // Section Informations du compte utilisateur
                Infolists\Components\Section::make('Compte utilisateur')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\ImageEntry::make('user.avatar')
                                    ->label('Photo de profil')
                                    ->disk('public')
                                    ->height(100)
                                    ->width(100)
                                    ->defaultImageUrl(fn ($record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->user->name ?? $record->user->email) . '&background=6366f1&color=ffffff&size=300')
                                    ->extraAttributes(['class' => 'rounded-full']),

                                Infolists\Components\Group::make([
                                    Infolists\Components\TextEntry::make('user.email')
                                        ->label('Email')
                                        ->copyable()
                                        ->copyMessage('Email copié!'),

                                    Infolists\Components\TextEntry::make('user.created_at')
                                        ->label('Membre depuis')
                                        ->dateTime('d/m/Y'),

                                    Infolists\Components\TextEntry::make('user.last_login_at')
                                        ->label('Dernière connexion')
                                        ->dateTime('d/m/Y H:i')
                                        ->placeholder('Jamais connecté'),
                                ]),
                            ]),
                    ])
                    ->collapsible(),
            ]);
    }
}
