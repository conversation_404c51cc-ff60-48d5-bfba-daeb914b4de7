<?php

namespace App\Filament\Marchand\Resources\ProfileResource\Pages;

use App\Filament\Marchand\Resources\ProfileResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;

class EditProfile extends EditRecord
{
    protected static string $resource = ProfileResource::class;

    public function mount($record = null): void
    {
        // Récupérer l'ID du marchand connecté
        $marchandId = auth()->user()->marchands->first()->id ?? null;

        if (!$marchandId) {
            abort(404);
        }

        // Appeler le mount parent avec l'ID du marchand
        parent::mount($marchandId);
    }

    public function getTitle(): string|Htmlable
    {
        return 'Mon profil';
    }


    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('view')
                ->label('Voir le profil complet')
                ->icon('heroicon-o-eye')
                ->url($this->getResource()::getUrl('index'))
                ->color('info'),
        ];
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Profil mis à jour')
            ->body('Votre profil a été mis à jour avec succès.');
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Séparer les données du marchand et de l'utilisateur
        $userData = [];
        $marchandData = [];

        foreach ($data as $key => $value) {
            if (str_starts_with($key, 'user.')) {
                $userKey = str_replace('user.', '', $key);
                $userData[$userKey] = $value;
            } else {
                $marchandData[$key] = $value;
            }
        }

        // Mettre à jour l'utilisateur si des données utilisateur sont présentes
        if (!empty($userData)) {
            $record->user->update($userData);
        }

        // Mettre à jour le marchand
        if (!empty($marchandData)) {
            $record->update($marchandData);
        }

        return $record;
    }
}
