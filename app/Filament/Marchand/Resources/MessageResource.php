<?php

namespace App\Filament\Marchand\Resources;

use App\Filament\Marchand\Resources\MessageResource\Pages;
use App\Models\ClientMarchandConversation;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class MessageResource extends Resource
{
    protected static ?string $model = ClientMarchandConversation::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';

    protected static ?string $navigationLabel = 'Messages';

    protected static ?string $modelLabel = 'Conversation';

    protected static ?string $pluralModelLabel = 'Conversations';

    protected static ?string $navigationGroup = 'Communication';

    protected static ?int $navigationSort = 20;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informations de la conversation')
                    ->schema([
                        Forms\Components\TextInput::make('sujet')
                            ->label('Sujet')
                            ->required(),

                        Forms\Components\Select::make('type_conversation')
                            ->label('Type')
                            ->options([
                                'commande' => 'Commande',
                                'produit' => 'Produit',
                                'livraison' => 'Livraison',
                                'retour' => 'Retour/Échange',
                                'general' => 'Général',
                            ])
                            ->required(),

                        Forms\Components\Select::make('statut')
                            ->label('Statut')
                            ->options([
                                'active' => 'Active',
                                'fermee' => 'Fermée',
                                'archivee' => 'Archivée',
                            ])
                            ->required(),

                        Forms\Components\Select::make('priorite')
                            ->label('Priorité')
                            ->options([
                                'basse' => 'Basse',
                                'normale' => 'Normale',
                                'haute' => 'Haute',
                            ])
                            ->required(),
                    ]),

                Forms\Components\Section::make('Participants')
                    ->schema([
                        Forms\Components\Select::make('client_id')
                            ->label('Client')
                            ->relationship('client.user', 'name')
                            ->getOptionLabelFromRecordUsing(fn ($record) => $record->name ?? $record->email ?? 'Client sans nom')
                            ->searchable()
                            ->required(),

                        Forms\Components\TextInput::make('marchand_id')
                            ->label('Marchand')
                            ->disabled()
                            ->default(fn () => Auth::user()->marchand?->id),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                // Filtrer par marchand connecté
                $marchandId = Auth::user()->marchand?->id;
                if ($marchandId) {
                    $query->where('marchand_id', $marchandId);
                }
            })
            ->columns([
                Tables\Columns\TextColumn::make('sujet')
                    ->label('Sujet')
                    ->searchable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('client.user.name')
                    ->label('Client')
                    ->searchable(),

                Tables\Columns\TextColumn::make('type_conversation')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'commande' => 'primary',
                        'produit' => 'info',
                        'livraison' => 'warning',
                        'retour' => 'danger',
                        'general' => 'gray',
                        default => 'gray'
                    }),

                Tables\Columns\TextColumn::make('statut')
                    ->label('Statut')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'active' => 'success',
                        'fermee' => 'gray',
                        'archivee' => 'secondary',
                        default => 'gray'
                    }),

                Tables\Columns\TextColumn::make('priorite')
                    ->label('Priorité')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'basse' => 'gray',
                        'normale' => 'primary',
                        'haute' => 'danger',
                        default => 'gray'
                    }),

                Tables\Columns\TextColumn::make('messages_count')
                    ->label('Messages')
                    ->counts('messages')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('date_creation')
                    ->label('Créée le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('date_dernier_message')
                    ->label('Dernière activité')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type_conversation')
                    ->label('Type')
                    ->options([
                        'commande' => 'Commande',
                        'produit' => 'Produit',
                        'livraison' => 'Livraison',
                        'retour' => 'Retour/Échange',
                        'general' => 'Général',
                    ]),

                Tables\Filters\SelectFilter::make('statut')
                    ->options([
                        'active' => 'Active',
                        'fermee' => 'Fermée',
                        'archivee' => 'Archivée',
                    ]),

                Tables\Filters\SelectFilter::make('priorite')
                    ->options([
                        'basse' => 'Basse',
                        'normale' => 'Normale',
                        'haute' => 'Haute',
                    ]),
            ])
            ->actions([
                Tables\Actions\Action::make('dashboard')
                    ->label('Ouvrir Dashboard Messages')
                    ->icon('heroicon-m-chat-bubble-left-right')
                    ->color('primary')
                    ->url('/dashboard/messages')
                    ->openUrlInNewTab(),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),

                Tables\Actions\Action::make('fermer')
                    ->label('Fermer')
                    ->icon('heroicon-m-lock-closed')
                    ->color('warning')
                    ->action(function ($record) {
                        $record->update(['statut' => 'fermee']);
                    })
                    ->requiresConfirmation()
                    ->visible(fn ($record) => $record->statut === 'active'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('date_dernier_message', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMessages::route('/'),
            'create' => Pages\CreateMessage::route('/create'),
            'view' => Pages\ViewMessage::route('/{record}'),
            'edit' => Pages\EditMessage::route('/{record}/edit'),
        ];
    }
}
