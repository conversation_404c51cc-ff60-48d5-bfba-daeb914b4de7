<?php

namespace App\Filament\Marchand\Resources;

use App\Filament\Marchand\Resources\MarchandTeamResource\Pages;
use App\Models\MarchandUser;
use App\Models\MarchandRole;
use App\Models\User;
use App\Enums\MarchandPermission;
use App\Services\UserInvitationService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class MarchandTeamResource extends Resource
{
    protected static ?string $model = MarchandUser::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationLabel = 'Équipe';

    protected static ?string $modelLabel = 'Membre de l\'équipe';

    protected static ?string $pluralModelLabel = 'Membres de l\'équipe';

    protected static ?string $navigationGroup = 'Équipe & Collaboration';

    protected static ?int $navigationSort = 1;

    public static function canAccess(): bool
    {
        return static::checkMarchandPermission(MarchandPermission::VIEW_TEAM);
    }

    /**
     * Helper pour vérifier les permissions marchand avec fallback pour propriétaire
     */
    private static function checkMarchandPermission(MarchandPermission $permission): bool
    {
        $user = auth()->user();
        $marchand = $user?->marchand;

        // Permettre l'accès aux propriétaires de marchand même sans MarchandUser (pour la configuration initiale)
        if ($marchand && $user?->id === $marchand->user_id) {
            return true;
        }

        return $user?->hasMarchandPermission($permission, $marchand) ?? false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informations utilisateur')
                    ->schema([
                        Forms\Components\TextInput::make('user.name')
                            ->label('Nom complet')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('user.email')
                            ->label('Email')
                            ->email()
                            ->required()
                            ->unique(
                                table: User::class,
                                column: 'email',
                                modifyRuleUsing: function ($rule, $livewire) {
                                    if ($livewire instanceof \Filament\Resources\Pages\EditRecord) {
                                        return $rule->ignore($livewire->record->user_id, 'id');
                                    }
                                    return $rule;
                                }
                            ),

                        Forms\Components\TextInput::make('user.password')
                            ->label('Mot de passe')
                            ->password()
                            ->dehydrateStateUsing(fn (?string $state): ?string =>
                                filled($state) ? Hash::make($state) : null
                            )
                            ->dehydrated(fn (?string $state): bool => filled($state))
                            ->required(fn (Forms\Get $get, string $operation): bool =>
                                $operation === 'create' && !$get('send_invitation')
                            )
                            ->helperText(fn (Forms\Get $get): string =>
                                $get('send_invitation')
                                    ? 'Laissez vide si vous envoyez une invitation'
                                    : 'Mot de passe requis'
                            ),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Rôle et permissions')
                    ->schema([
                        Forms\Components\Select::make('role_id')
                            ->label('Rôle')
                            ->options(MarchandRole::active()->pluck('name', 'id'))
                            ->required()
                            ->searchable()
                            ->preload(),

                        Forms\Components\Select::make('access_level')
                            ->label('Niveau d\'accès')
                            ->options([
                                'owner' => 'Propriétaire',
                                'manager' => 'Gestionnaire',
                                'employee' => 'Employé',
                                'read' => 'Lecture seule',
                                'write' => 'Lecture/Écriture',
                            ])
                            ->default('employee')
                            ->required(),

                        Forms\Components\CheckboxList::make('permissions')
                            ->label('Permissions spécifiques')
                            ->options(function () {
                                $grouped = MarchandPermission::getGrouped();
                                $options = [];
                                foreach ($grouped as $category => $permissions) {
                                    foreach ($permissions as $permission) {
                                        $options[$permission->value] = $permission->label();
                                    }
                                }
                                return $options;
                            })
                            ->columns(3)
                            ->helperText('Permissions supplémentaires en plus de celles du rôle'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Paramètres')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Membre actif')
                            ->default(true),

                        Forms\Components\Toggle::make('send_invitation')
                            ->label('Envoyer une invitation par email')
                            ->default(true)
                            ->visible(fn (string $operation): bool => $operation === 'create')
                            ->helperText('Si activé, un email d\'invitation sera envoyé au lieu de créer le mot de passe directement'),

                        Forms\Components\Textarea::make('notes')
                            ->label('Notes internes')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Nom')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.email')
                    ->label('Email')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('role.name')
                    ->label('Rôle')
                    ->badge()
                    ->color(fn (MarchandUser $record): string => match($record->role?->slug) {
                        'owner' => 'danger',
                        'manager' => 'warning',
                        'product_manager' => 'success',
                        'order_manager' => 'info',
                        'accountant' => 'primary',
                        'employee' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('access_level')
                    ->label('Niveau d\'accès')
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'owner' => 'Propriétaire',
                        'manager' => 'Gestionnaire',
                        'employee' => 'Employé',
                        'read' => 'Lecture',
                        'write' => 'Écriture',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'owner' => 'danger',
                        'manager' => 'warning',
                        'employee' => 'success',
                        'read' => 'gray',
                        'write' => 'info',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Actif')
                    ->boolean(),

                Tables\Columns\TextColumn::make('invitation_status')
                    ->label('Statut invitation')
                    ->state(function (MarchandUser $record): string {
                        if ($record->invitation_token && $record->invitation_expires_at > now()) {
                            return 'En attente';
                        } elseif ($record->invitation_token && $record->invitation_expires_at <= now()) {
                            return 'Expirée';
                        } elseif ($record->user->email_verified_at) {
                            return 'Acceptée';
                        }
                        return 'Aucune';
                    })
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'Acceptée' => 'success',
                        'En attente' => 'warning',
                        'Expirée' => 'danger',
                        'Aucune' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('last_login_at')
                    ->label('Dernière connexion')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Jamais'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ajouté le')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role_id')
                    ->label('Rôle')
                    ->options(MarchandRole::active()->pluck('name', 'id')),

                Tables\Filters\SelectFilter::make('access_level')
                    ->label('Niveau d\'accès')
                    ->options([
                        'owner' => 'Propriétaire',
                        'manager' => 'Gestionnaire',
                        'employee' => 'Employé',
                        'read' => 'Lecture',
                        'write' => 'Écriture',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Actif'),

                Tables\Filters\Filter::make('pending_invitation')
                    ->label('Invitation en attente')
                    ->query(fn (Builder $query): Builder => 
                        $query->whereNotNull('invitation_token')
                              ->where('invitation_expires_at', '>', now())
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn (MarchandUser $record): bool =>
                        static::checkMarchandPermission(MarchandPermission::MANAGE_TEAM)
                    ),
                Tables\Actions\Action::make('resend_invitation')
                    ->label('Renvoyer invitation')
                    ->icon('heroicon-o-envelope')
                    ->color('warning')
                    ->visible(fn (MarchandUser $record): bool =>
                        $record->invitation_token &&
                        static::checkMarchandPermission(MarchandPermission::MANAGE_TEAM)
                    )
                    ->requiresConfirmation()
                    ->action(function (MarchandUser $record): void {
                        app(UserInvitationService::class)->resendMarchandInvitation($record);
                    }),
                Tables\Actions\Action::make('toggle_active')
                    ->label(fn (MarchandUser $record): string => $record->is_active ? 'Désactiver' : 'Activer')
                    ->icon(fn (MarchandUser $record): string => $record->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn (MarchandUser $record): string => $record->is_active ? 'danger' : 'success')
                    ->requiresConfirmation()
                    ->visible(fn (MarchandUser $record): bool =>
                        static::checkMarchandPermission(MarchandPermission::MANAGE_TEAM)
                    )
                    ->action(function (MarchandUser $record): void {
                        $record->update(['is_active' => !$record->is_active]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn (): bool =>
                            static::checkMarchandPermission(MarchandPermission::REMOVE_USERS)
                        ),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informations utilisateur')
                    ->schema([
                        Infolists\Components\TextEntry::make('user.name')
                            ->label('Nom complet'),
                        Infolists\Components\TextEntry::make('user.email')
                            ->label('Email'),
                        Infolists\Components\IconEntry::make('user.email_verified_at')
                            ->label('Email vérifié')
                            ->boolean(),
                        Infolists\Components\TextEntry::make('user.created_at')
                            ->label('Compte créé le')
                            ->dateTime(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Rôle et permissions')
                    ->schema([
                        Infolists\Components\TextEntry::make('role.name')
                            ->label('Rôle')
                            ->badge(),
                        Infolists\Components\TextEntry::make('access_level')
                            ->label('Niveau d\'accès')
                            ->formatStateUsing(fn (string $state): string => match($state) {
                                'owner' => 'Propriétaire',
                                'manager' => 'Gestionnaire',
                                'employee' => 'Employé',
                                'read' => 'Lecture seule',
                                'write' => 'Lecture/Écriture',
                                default => $state,
                            })
                            ->badge(),
                        Infolists\Components\IconEntry::make('is_active')
                            ->label('Membre actif')
                            ->boolean(),
                    ])
                    ->columns(3),

                Infolists\Components\Section::make('Statut d\'invitation')
                    ->schema([
                        Infolists\Components\TextEntry::make('invitation_status')
                            ->label('Statut')
                            ->state(function (MarchandUser $record): string {
                                if ($record->invitation_token && $record->invitation_expires_at > now()) {
                                    return 'Invitation en attente';
                                } elseif ($record->invitation_token && $record->invitation_expires_at <= now()) {
                                    return 'Invitation expirée';
                                } elseif ($record->user->email_verified_at) {
                                    return 'Invitation acceptée';
                                }
                                return 'Aucune invitation';
                            })
                            ->badge(),
                        Infolists\Components\TextEntry::make('invitation_expires_at')
                            ->label('Expiration invitation')
                            ->dateTime()
                            ->placeholder('N/A')
                            ->visible(fn (MarchandUser $record): bool => !empty($record->invitation_token)),
                    ])
                    ->columns(2)
                    ->visible(fn (MarchandUser $record): bool => !empty($record->invitation_token)),

                Infolists\Components\Section::make('Activité')
                    ->schema([
                        Infolists\Components\TextEntry::make('last_login_at')
                            ->label('Dernière connexion')
                            ->dateTime()
                            ->placeholder('Jamais connecté'),
                        Infolists\Components\TextEntry::make('invited_by')
                            ->label('Invité par')
                            ->formatStateUsing(fn (?int $state): string => 
                                $state ? User::find($state)?->name ?? 'Utilisateur supprimé' : 'Système'
                            ),
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Ajouté le')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Modifié le')
                            ->dateTime(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Notes')
                    ->schema([
                        Infolists\Components\TextEntry::make('notes')
                            ->label('Notes internes')
                            ->placeholder('Aucune note')
                            ->columnSpanFull(),
                    ])
                    ->visible(fn (MarchandUser $record): bool => !empty($record->notes)),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMarchandTeam::route('/'),
            'create' => Pages\CreateMarchandTeam::route('/create'),
            'view' => Pages\ViewMarchandTeam::route('/{record}'),
            'edit' => Pages\EditMarchandTeam::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $marchandId = auth()->user()?->marchand?->id;
        
        return parent::getEloquentQuery()
            ->with(['user', 'role', 'marchand'])
            ->when($marchandId, fn($query) => $query->where('marchand_id', $marchandId));
    }
}
