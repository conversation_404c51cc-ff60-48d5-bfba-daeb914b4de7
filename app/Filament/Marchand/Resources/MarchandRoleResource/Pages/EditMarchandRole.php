<?php

namespace App\Filament\Marchand\Resources\MarchandRoleResource\Pages;

use App\Filament\Marchand\Resources\MarchandRoleResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditMarchandRole extends EditRecord
{
    protected static string $resource = MarchandRoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make()
                ->visible(fn (): bool => 
                    $this->record->canBeDeleted() && 
                    (auth()->user()?->isOwner(auth()->user()?->marchand) ?? false)
                ),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // S'assurer que les permissions sont un tableau
        if (isset($data['permissions']) && is_string($data['permissions'])) {
            $data['permissions'] = json_decode($data['permissions'], true) ?? [];
        }

        // Empêcher la modification du slug pour les rôles système
        if ($this->record->is_system_role && !auth()->user()?->isOwner(auth()->user()?->marchand)) {
            unset($data['slug']);
            unset($data['is_system_role']);
        }

        return $data;
    }
}
