<?php

namespace App\Filament\Marchand\Resources\BoutiqueReviewResource\Pages;

use App\Filament\Marchand\Resources\BoutiqueReviewResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditBoutiqueReview extends EditRecord
{
    protected static string $resource = BoutiqueReviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Réponse enregistrée')
            ->body('Votre réponse à l\'avis a été enregistrée et sera visible publiquement.');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Ajouter la date de réponse si une réponse est fournie
        if (!empty($data['marchand_response'])) {
            $data['marchand_response_at'] = now();
        }
        
        return $data;
    }

    protected function afterSave(): void
    {
        $record = $this->record;
        
        // Log de l'activité
        if ($record->marchand_response) {
            activity()
                ->performedOn($record)
                ->causedBy(auth()->user())
                ->withProperties([
                    'action' => 'merchant_response',
                    'response_length' => strlen($record->marchand_response),
                ])
                ->log('Réponse ajoutée à un avis client');
        }
    }
}
