<?php

namespace App\Filament\Marchand\Resources\BoutiqueReviewResource\Pages;

use App\Filament\Marchand\Resources\BoutiqueReviewResource;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ListBoutiqueReviews extends ListRecords
{
    protected static string $resource = BoutiqueReviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Pas d'action de création car les avis sont créés par les clients
        ];
    }

    public function getTabs(): array
    {
        $user = Auth::user();
        
        if (!$user || !$user->marchandUser) {
            return [];
        }

        $marchandId = $user->marchandUser->marchand_id;

        return [
            'all' => Tab::make('Tous les avis'),

            'approved' => Tab::make('Approuvés')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_approved', true))
                ->badge(fn () => static::getResource()::getModel()::where('marchand_id', $marchandId)
                    ->where('is_approved', true)->count())
                ->badgeColor('success'),

            'pending' => Tab::make('En attente')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_approved', false))
                ->badge(fn () => static::getResource()::getModel()::where('marchand_id', $marchandId)
                    ->where('is_approved', false)->count())
                ->badgeColor('warning'),

            'without_response' => Tab::make('Sans réponse')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->where('is_approved', true)
                    ->whereNull('marchand_response')
                )
                ->badge(fn () => static::getResource()::getModel()::where('marchand_id', $marchandId)
                    ->where('is_approved', true)
                    ->whereNull('marchand_response')
                    ->count())
                ->badgeColor('danger'),

            'with_response' => Tab::make('Avec réponse')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereNotNull('marchand_response')
                ),

            'verified' => Tab::make('Achats vérifiés')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_verified', true))
                ->badge(fn () => static::getResource()::getModel()::where('marchand_id', $marchandId)
                    ->where('is_verified', true)->count())
                ->badgeColor('info'),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // Optionnel : Ajouter des widgets de statistiques pour les marchands
        ];
    }
}
