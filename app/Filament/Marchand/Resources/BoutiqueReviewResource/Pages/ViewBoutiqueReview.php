<?php

namespace App\Filament\Marchand\Resources\BoutiqueReviewResource\Pages;

use App\Filament\Marchand\Resources\BoutiqueReviewResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\RepeatableEntry;

class ViewBoutiqueReview extends ViewRecord
{
    protected static string $resource = BoutiqueReviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('Répondre à l\'avis')
                ->icon('heroicon-o-chat-bubble-left-right')
                ->visible(fn () => empty($this->record->marchand_response)),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Avis client')
                    ->schema([
                        TextEntry::make('name')
                            ->label('Nom du client'),

                        TextEntry::make('rating')
                            ->label('Note')
                            ->formatStateUsing(fn (int $state): string => str_repeat('⭐', $state) . " ({$state}/5)"),

                        TextEntry::make('title')
                            ->label('Titre de l\'avis'),

                        TextEntry::make('comment')
                            ->label('Commentaire')
                            ->columnSpanFull(),

                        TextEntry::make('created_at')
                            ->label('Date de l\'avis')
                            ->dateTime('d/m/Y à H:i'),
                    ])
                    ->columns(2),

                Section::make('Images de l\'avis')
                    ->schema([
                        RepeatableEntry::make('image_urls')
                            ->label('')
                            ->schema([
                                ImageEntry::make('url')
                                    ->label('')
                                    ->height(150)
                                    ->width(150),
                            ])
                            ->columns(4),
                    ])
                    ->visible(fn ($record) => !empty($record->image_urls)),

                Section::make('Statut de l\'avis')
                    ->schema([
                        IconEntry::make('is_approved')
                            ->label('Approuvé par l\'admin')
                            ->boolean(),

                        IconEntry::make('is_verified')
                            ->label('Achat vérifié')
                            ->boolean(),

                        TextEntry::make('likes')
                            ->label('Likes')
                            ->badge()
                            ->color('success'),

                        TextEntry::make('dislikes')
                            ->label('Dislikes')
                            ->badge()
                            ->color('danger'),
                    ])
                    ->columns(2),

                Section::make('Votre réponse')
                    ->schema([
                        TextEntry::make('marchand_response')
                            ->label('Réponse')
                            ->columnSpanFull()
                            ->placeholder('Aucune réponse pour le moment'),

                        TextEntry::make('marchand_response_at')
                            ->label('Date de réponse')
                            ->dateTime('d/m/Y à H:i')
                            ->visible(fn ($record) => !empty($record->marchand_response)),
                    ])
                    ->headerActions([
                        Actions\Action::make('respond')
                            ->label('Répondre')
                            ->icon('heroicon-o-chat-bubble-left-right')
                            ->url(fn () => static::getResource()::getUrl('edit', ['record' => $this->record]))
                            ->visible(fn () => empty($this->record->marchand_response)),
                    ]),
            ]);
    }
}
