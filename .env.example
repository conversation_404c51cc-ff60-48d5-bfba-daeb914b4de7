APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

# URL du projet lorrelei pour accéder aux fichiers
LORRELEI_URL=http://localhost:8000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Meilisearch Configuration
MEILISEARCH_HOST=http://127.0.0.1:7700
MEILISEARCH_KEY=
MEILISEARCH_PRODUITS_INDEX=produits
MEILISEARCH_CATEGORIES_INDEX=categories
MEILISEARCH_SEARCH_LIMIT=20
MEILISEARCH_FALLBACK_ENABLED=true
MEILISEARCH_TIMEOUT=5
MEILISEARCH_RETRY_ATTEMPTS=3

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

PAYPAL_MODE=sandbox
#PayPal Setting & API Credentials - sandbox
PAYPAL_SANDBOX_CLIENT_ID=
PAYPAL_SANDBOX_CLIENT_SECRET=

#PayPal Setting & API Credentials - live
PAYPAL_LIVE_CLIENT_ID=
PAYPAL_LIVE_CLIENT_SECRET=
PAYPAL_LIVE_APP_ID=

#Payment Action. Can only be 'Sale', 'Authorization' or 'Order'
PAYPAL_PAYMENT_ACTION=Sale

#Currency. Default is USD. If you need to update it, then set the value through the PAYPAL_CURRENCY environment variable.
PAYPAL_CURRENCY=EUR

#Validate SSL when creating api client. By default, the value is great. To disable validation set to false.
PAYPAL_VALIDATE_SSL=false

# URL du CDN pour les images
CDN_IMAGE_URL=https://marchandportail.lorrelei.com

LORRELEI_URL=https://lorrelei.com
