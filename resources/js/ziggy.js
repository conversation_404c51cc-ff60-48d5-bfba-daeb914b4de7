const Ziggy = {"url":"http:\/\/localhost:8001","port":8001,"defaults":{},"routes":{"filament.exports.download":{"uri":"filament\/exports\/{export}\/download","methods":["GET","HEAD"],"parameters":["export"],"bindings":{"export":"id"}},"filament.imports.failed-rows.download":{"uri":"filament\/imports\/{import}\/failed-rows\/download","methods":["GET","HEAD"],"parameters":["import"],"bindings":{"import":"id"}},"filament.admin.auth.login":{"uri":"admin\/login","methods":["GET","HEAD"]},"filament.admin.auth.logout":{"uri":"admin\/logout","methods":["POST"]},"filament.admin.pages.dashboard":{"uri":"admin","methods":["GET","HEAD"]},"filament.admin.resources.banners.index":{"uri":"admin\/banners","methods":["GET","HEAD"]},"filament.admin.resources.banners.create":{"uri":"admin\/banners\/create","methods":["GET","HEAD"]},"filament.admin.resources.banners.edit":{"uri":"admin\/banners\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.categories.index":{"uri":"admin\/categories","methods":["GET","HEAD"]},"filament.admin.resources.categories.create":{"uri":"admin\/categories\/create","methods":["GET","HEAD"]},"filament.admin.resources.categories.edit":{"uri":"admin\/categories\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.commandes.index":{"uri":"admin\/commandes","methods":["GET","HEAD"]},"filament.admin.resources.commandes.create":{"uri":"admin\/commandes\/create","methods":["GET","HEAD"]},"filament.admin.resources.commandes.edit":{"uri":"admin\/commandes\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.coupons.index":{"uri":"admin\/coupons","methods":["GET","HEAD"]},"filament.admin.resources.coupons.create":{"uri":"admin\/coupons\/create","methods":["GET","HEAD"]},"filament.admin.resources.coupons.edit":{"uri":"admin\/coupons\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.currencies.index":{"uri":"admin\/currencies","methods":["GET","HEAD"]},"filament.admin.resources.currencies.create":{"uri":"admin\/currencies\/create","methods":["GET","HEAD"]},"filament.admin.resources.currencies.edit":{"uri":"admin\/currencies\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.importations.index":{"uri":"admin\/importations","methods":["GET","HEAD"]},"filament.admin.resources.marchands.index":{"uri":"admin\/marchands","methods":["GET","HEAD"]},"filament.admin.resources.marchands.create":{"uri":"admin\/marchands\/create","methods":["GET","HEAD"]},"filament.admin.resources.marchands.view":{"uri":"admin\/marchands\/{record}","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.marchands.edit":{"uri":"admin\/marchands\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.paiements.index":{"uri":"admin\/paiements","methods":["GET","HEAD"]},"filament.admin.resources.paiements.create":{"uri":"admin\/paiements\/create","methods":["GET","HEAD"]},"filament.admin.resources.paiements.edit":{"uri":"admin\/paiements\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.produits.index":{"uri":"admin\/produits","methods":["GET","HEAD"]},"filament.admin.resources.produits.create":{"uri":"admin\/produits\/create","methods":["GET","HEAD"]},"filament.admin.resources.produits.edit":{"uri":"admin\/produits\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.size-guides.index":{"uri":"admin\/size-guides","methods":["GET","HEAD"]},"filament.admin.resources.size-guides.create":{"uri":"admin\/size-guides\/create","methods":["GET","HEAD"]},"filament.admin.resources.size-guides.edit":{"uri":"admin\/size-guides\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.sizes.index":{"uri":"admin\/sizes","methods":["GET","HEAD"]},"filament.admin.resources.sizes.create":{"uri":"admin\/sizes\/create","methods":["GET","HEAD"]},"filament.admin.resources.sizes.edit":{"uri":"admin\/sizes\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.users.index":{"uri":"admin\/users","methods":["GET","HEAD"]},"filament.admin.resources.users.create":{"uri":"admin\/users\/create","methods":["GET","HEAD"]},"filament.admin.resources.users.view":{"uri":"admin\/users\/{record}","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.users.edit":{"uri":"admin\/users\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.zone-livraisons.index":{"uri":"admin\/zone-livraisons","methods":["GET","HEAD"]},"filament.admin.resources.zone-livraisons.create":{"uri":"admin\/zone-livraisons\/create","methods":["GET","HEAD"]},"filament.admin.resources.zone-livraisons.edit":{"uri":"admin\/zone-livraisons\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.marchand.auth.login":{"uri":"marchand\/login","methods":["GET","HEAD"]},"filament.marchand.auth.logout":{"uri":"marchand\/logout","methods":["POST"]},"filament.marchand.pages.dashboard":{"uri":"marchand","methods":["GET","HEAD"]},"filament.marchand.resources.categories.index":{"uri":"marchand\/categories","methods":["GET","HEAD"]},"filament.marchand.resources.categories.view":{"uri":"marchand\/categories\/{record}","methods":["GET","HEAD"],"parameters":["record"]},"filament.marchand.resources.commandes.index":{"uri":"marchand\/commandes","methods":["GET","HEAD"]},"filament.marchand.resources.commandes.create":{"uri":"marchand\/commandes\/create","methods":["GET","HEAD"]},"filament.marchand.resources.commandes.edit":{"uri":"marchand\/commandes\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.marchand.resources.importations.index":{"uri":"marchand\/importations","methods":["GET","HEAD"]},"filament.marchand.resources.marchand-zone-livraisons.index":{"uri":"marchand\/marchand-zone-livraisons","methods":["GET","HEAD"]},"filament.marchand.resources.marchand-zone-livraisons.create":{"uri":"marchand\/marchand-zone-livraisons\/create","methods":["GET","HEAD"]},"filament.marchand.resources.marchand-zone-livraisons.edit":{"uri":"marchand\/marchand-zone-livraisons\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.marchand.resources.paiements.index":{"uri":"marchand\/paiements","methods":["GET","HEAD"]},"filament.marchand.resources.paiements.view":{"uri":"marchand\/paiements\/{record}","methods":["GET","HEAD"],"parameters":["record"]},"filament.marchand.resources.produits.index":{"uri":"marchand\/produits","methods":["GET","HEAD"]},"filament.marchand.resources.produits.create":{"uri":"marchand\/produits\/create","methods":["GET","HEAD"]},"filament.marchand.resources.produits.edit":{"uri":"marchand\/produits\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.marchand.resources.mon-profil.index":{"uri":"marchand\/mon-profil","methods":["GET","HEAD"]},"livewire.update":{"uri":"livewire\/update","methods":["POST"]},"livewire.upload-file":{"uri":"livewire\/upload-file","methods":["POST"]},"livewire.preview-file":{"uri":"livewire\/preview-file\/{filename}","methods":["GET","HEAD"],"parameters":["filename"]},"home":{"uri":"\/","methods":["GET","HEAD"]},"seller.welcome":{"uri":"seller\/welcome","methods":["GET","HEAD"]},"seller.information":{"uri":"seller\/information","methods":["GET","HEAD"]},"seller.information.store":{"uri":"seller\/information","methods":["POST"]},"seller.billing":{"uri":"seller\/billing","methods":["GET","HEAD"]},"seller.billing.store":{"uri":"seller\/billing","methods":["POST"]},"seller.store":{"uri":"seller\/store","methods":["GET","HEAD"]},"seller.store.store":{"uri":"seller\/store","methods":["POST"]},"seller.documents":{"uri":"seller\/documents","methods":["GET","HEAD"]},"seller.documents.upload":{"uri":"seller\/documents\/upload","methods":["POST"]},"seller.finalize":{"uri":"seller\/finalize","methods":["POST"]},"seller.rejected":{"uri":"seller\/rejected","methods":["GET","HEAD"]},"seller.suspended":{"uri":"seller\/suspended","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"dashboard.profile":{"uri":"dashboard\/profile","methods":["GET","HEAD"]},"dashboard.profile.update":{"uri":"dashboard\/profile","methods":["PATCH"]},"dashboard.subscriptions":{"uri":"dashboard\/subscriptions","methods":["GET","HEAD"]},"dashboard.documents":{"uri":"dashboard\/documents","methods":["GET","HEAD"]},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"admin.import.index":{"uri":"admin\/import","methods":["GET","HEAD"]},"admin.import.categories":{"uri":"admin\/import\/categories","methods":["POST"]},"admin.import.preview-categories":{"uri":"admin\/import\/categories\/preview","methods":["GET","HEAD"]},"admin.import.confirm-categories":{"uri":"admin\/import\/categories\/confirm","methods":["POST"]},"admin.import.products":{"uri":"admin\/import\/products","methods":["POST"]},"admin.import.categories.template":{"uri":"admin\/import\/categories\/template","methods":["GET","HEAD"]},"admin.import.products.template":{"uri":"admin\/import\/products\/template","methods":["GET","HEAD"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
