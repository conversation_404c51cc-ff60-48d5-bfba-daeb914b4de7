import React, { useState, useRef } from 'react';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
    Upload,
    FileText,
    Image,
    X,
    CheckCircle,
    AlertCircle,
    Loader2
} from 'lucide-react';

interface SimpleFileUploadProps {
    typeDocument: string;
    acceptedTypes?: string[];
    maxSize?: number; // en bytes
    onSuccess?: () => void;
    onError?: (error: string) => void;
    disabled?: boolean;
    existingFile?: {
        nom_original: string;
        statut_validation: string;
        date_upload: string;
    };
}

export default function SimpleFileUpload({
    typeDocument,
    acceptedTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'],
    maxSize = 10 * 1024 * 1024, // 10MB par défaut
    onSuccess,
    onError,
    disabled = false,
    existingFile
}: SimpleFileUploadProps) {
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [dragOver, setDragOver] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);

    const { data, setData, post, processing, errors, reset } = useForm({
        document: null as File | null,
        type: typeDocument
    });

    const getFileIcon = (fileName: string) => {
        const extension = fileName.split('.').pop()?.toLowerCase();
        if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) {
            return <Image className="h-5 w-5 text-blue-500" />;
        }
        return <FileText className="h-5 w-5 text-gray-500" />;
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const validateFile = (file: File): string | null => {
        // Vérifier la taille
        if (file.size > maxSize) {
            return `Le fichier est trop volumineux. Taille maximale: ${formatFileSize(maxSize)}`;
        }

        // Vérifier le type
        const extension = '.' + file.name.split('.').pop()?.toLowerCase();
        if (!acceptedTypes.includes(extension)) {
            return `Type de fichier non autorisé. Types acceptés: ${acceptedTypes.join(', ')}`;
        }

        return null;
    };

    const handleFileSelect = (file: File) => {
        const error = validateFile(file);
        if (error) {
            onError?.(error);
            return;
        }

        setData('document', file);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setDragOver(false);

        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    };

    const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files && files.length > 0) {
            handleFileSelect(files[0]);
        }
    };

    const handleUpload = () => {
        if (!data.document) return;
        post(route('seller.upload-document'), {
            onSuccess: () => {
                reset();
                setUploadProgress(0);
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
                // Appeler onSuccess après un petit délai pour éviter les problèmes de timing
                setTimeout(() => {
                    onSuccess?.();
                }, 100);
            },
            onError: (errors) => {
                const errorMessage = errors.document || errors.type || 'Erreur lors de l\'upload';
                onError?.(errorMessage);
            },
            onProgress: (progress) => {
                setUploadProgress(progress?.percentage || 0);
            }
        });
    };

    const removeFile = () => {
        setData('document', null);
        setUploadProgress(0);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const getStatusBadge = () => {
        if (!existingFile) return null;

        // Normaliser le statut
        const status = existingFile.statut_validation?.toLowerCase();

        switch (status) {
            case 'valide':
            case 'validé':
                return (
                    <div className="flex items-center gap-2 text-green-600 bg-green-50 px-3 py-1 rounded-full text-sm">
                        <CheckCircle className="h-4 w-4" />
                        Validé
                    </div>
                );
            case 'rejete':
            case 'rejeté':
                return (
                    <div className="flex items-center gap-2 text-red-600 bg-red-50 px-3 py-1 rounded-full text-sm">
                        <AlertCircle className="h-4 w-4" />
                        Rejeté
                    </div>
                );
            case 'en_attente':
            case 'pending':
            default:
                return (
                    <div className="flex items-center gap-2 text-yellow-600 bg-yellow-50 px-3 py-1 rounded-full text-sm">
                        <Loader2 className="h-4 w-4" />
                        En attente
                    </div>
                );
        }
    };

    return (
        <div className="space-y-4">
            {/* Fichier existant */}
            {existingFile && (
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center gap-3">
                        {getFileIcon(existingFile.nom_original)}
                        <div>
                            <p className="font-medium">{existingFile.nom_original}</p>
                            <p className="text-sm text-gray-500">
                                Uploadé le {new Date(existingFile.date_upload).toLocaleDateString()}
                            </p>
                        </div>
                    </div>
                    {getStatusBadge()}
                </div>
            )}

            {/* Zone d'upload */}
            <div
                className={`
                    border-2 border-dashed rounded-lg p-8 text-center transition-colors
                    ${dragOver ? 'border-primary bg-primary/5' : 'border-gray-300 dark:border-gray-600'}
                    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-primary hover:bg-primary/5'}
                `}

            >
                <Input
                    ref={fileInputRef}
                    type="file"
                    accept={acceptedTypes.join(',')}
                    onChange={handleFileInputChange}
                    className="hidden"
                    disabled={disabled}
                />

                {data.document ? (
                    <div className="space-y-4">
                        <div className="flex items-center justify-center gap-3">
                            {getFileIcon(data.document.name)}
                            <div>
                                <p className="font-medium">{data.document.name}</p>
                                <p className="text-sm text-gray-500">{formatFileSize(data.document.size)}</p>
                            </div>
                            <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    removeFile();
                                }}
                                disabled={processing}
                            >
                                <X className="h-4 w-4" />
                            </Button>
                        </div>

                        {processing && (
                            <div className="space-y-2">
                                <Progress value={uploadProgress} className="w-full" />
                                <p className="text-sm text-gray-500">Upload en cours... {uploadProgress}%</p>
                            </div>
                        )}

                        <Button
                            type="button"
                            onClick={handleUpload}
                            disabled={processing}
                            className="w-full cursor-pointer"
                        >
                            {processing ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Upload en cours...
                                </>
                            ) : (
                                <div className="flex items-center gap-2">
                                    <Upload className="mr-2 h-4 w-4" />
                                    Uploader le fichier
                                </div>
                            )}
                        </Button>
                    </div>
                ) : (
                    <div className="space-y-4"
                        onDrop={handleDrop}
                        onDragOver={(e) => {
                            e.preventDefault();
                            setDragOver(true);
                        }}
                        onDragLeave={() => setDragOver(false)}
                        onClick={() => !disabled && fileInputRef.current?.click()}
                    >
                        <Upload className="mx-auto h-12 w-12 text-gray-400" />
                        <div>
                            <p className="text-lg font-medium">
                                Glissez-déposez votre fichier ici
                            </p>
                            <p className="text-sm text-gray-500">
                                ou cliquez pour parcourir
                            </p>
                        </div>
                        <div className="text-xs text-gray-500">
                            <p>Types acceptés: {acceptedTypes.join(', ')}</p>
                            <p>Taille maximale: {formatFileSize(maxSize)}</p>
                        </div>
                    </div>
                )}
            </div>

            {/* Erreurs */}
            {errors.document && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{errors.document}</AlertDescription>
                </Alert>
            )}
        </div>
    );
}
