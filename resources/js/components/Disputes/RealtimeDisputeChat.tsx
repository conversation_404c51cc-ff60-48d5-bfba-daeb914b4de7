import React, { useState, useEffect, useRef } from 'react';
import { useReverbWebSocket } from '../../hooks/useReverbWebSocket';

interface DisputeMessage {
    id: number;
    message: string;
    auteur_type: string;
    auteur_nom: string;
    type_message: 'message' | 'resolution' | 'escalation';
    created_at: string;
    pieces_jointes?: string[];
    interne?: boolean;
}

interface Dispute {
    id: number;
    numero_litige: string;
    sujet: string;
    statut: string;
    priorite: string;
    urgent: boolean;
}

interface RealtimeDisputeChatProps {
    disputeId: string;
    dispute: Dispute;
    initialMessages: DisputeMessage[];
    currentUserId: string;
    currentUserType: 'client' | 'marchand' | 'admin';
    onSendMessage: (message: string, type: string, files?: File[]) => Promise<void>;
    onStatusChange?: (newStatus: string) => Promise<void>;
    className?: string;
}

export const RealtimeDisputeChat: React.FC<RealtimeDisputeChatProps> = ({
    disputeId,
    dispute,
    initialMessages,
    currentUserId,
    currentUserType,
    onSendMessage,
    onStatusChange,
    className = '',
}) => {
    const [messages, setMessages] = useState<DisputeMessage[]>(initialMessages);
    const [newMessage, setNewMessage] = useState('');
    const [messageType, setMessageType] = useState<'message' | 'resolution' | 'escalation'>('message');
    const [isLoading, setIsLoading] = useState(false);
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    const [disputeInfo, setDisputeInfo] = useState<Dispute>(dispute);
    
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Hook WebSocket pour les litiges
    const {
        typingUsers,
        formatTypingUsers,
        useTypingInput,
        requestNotificationPermission,
    } = useReverbWebSocket({
        disputeId,
        onMessage: (data) => {
            // Ajouter le nouveau message à la liste
            const newMsg: DisputeMessage = {
                id: parseInt(data.message.id),
                message: data.message.message,
                auteur_type: data.message.auteur_type,
                auteur_nom: data.message.auteur_nom,
                type_message: data.message.type_message,
                created_at: data.message.created_at,
                pieces_jointes: data.message.pieces_jointes,
                interne: data.message.interne,
            };
            setMessages(prev => [...prev, newMsg]);
            scrollToBottom();
        },
        onStatusChange: (data) => {
            // Mettre à jour les informations du litige
            setDisputeInfo(prev => ({
                ...prev,
                ...data.dispute,
            }));
        },
        onNotification: (data) => {
            console.log('Notification litige reçue:', data);
        },
    });

    // Hook pour gérer la frappe automatiquement
    const { handleInputChange, handleInputBlur } = useTypingInput();

    // Synchroniser les messages avec les props
    useEffect(() => {
        setMessages(initialMessages);
    }, [initialMessages]);

    // Faire défiler vers le bas automatiquement
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    // Demander la permission pour les notifications au montage
    useEffect(() => {
        requestNotificationPermission();
    }, [requestNotificationPermission]);

    // Gérer les changements dans l'input
    const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const value = e.target.value;
        setNewMessage(value);
        handleInputChange(value);
    };

    // Gérer l'envoi du message
    const handleSendMessage = async () => {
        if (!newMessage.trim() && selectedFiles.length === 0) return;

        setIsLoading(true);
        try {
            await onSendMessage(newMessage, messageType, selectedFiles);
            setNewMessage('');
            setSelectedFiles([]);
            setMessageType('message');
            handleInputBlur(); // Arrêter l'indicateur de frappe
        } catch (error) {
            console.error('Erreur envoi message litige:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // Gérer l'appui sur Entrée
    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    // Gérer la sélection de fichiers
    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        setSelectedFiles(prev => [...prev, ...files]);
    };

    // Supprimer un fichier sélectionné
    const removeFile = (index: number) => {
        setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    };

    // Formater la date
    const formatMessageTime = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleTimeString('fr-FR', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    };

    // Obtenir la couleur du statut
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'ouvert': return 'bg-yellow-100 text-yellow-800';
            case 'en_cours': return 'bg-blue-100 text-blue-800';
            case 'resolu': return 'bg-green-100 text-green-800';
            case 'ferme': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    // Obtenir la couleur de priorité
    const getPriorityColor = (priorite: string) => {
        switch (priorite) {
            case 'haute': return 'bg-red-100 text-red-800';
            case 'moyenne': return 'bg-yellow-100 text-yellow-800';
            case 'basse': return 'bg-green-100 text-green-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    // Obtenir la couleur du type de message
    const getMessageTypeColor = (type: string) => {
        switch (type) {
            case 'resolution': return 'border-l-4 border-green-500 bg-green-50';
            case 'escalation': return 'border-l-4 border-red-500 bg-red-50';
            default: return '';
        }
    };

    // Filtrer les utilisateurs qui tapent (exclure l'utilisateur actuel)
    const otherTypingUsers = typingUsers.filter(user => user.user_id.toString() !== currentUserId);

    return (
        <div className={`flex flex-col h-full bg-white dark:bg-gray-900 ${className}`}>
            {/* Header avec informations du litige */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Litige #{disputeInfo.numero_litige}
                    </h3>
                </div>
                
                <div className="flex items-center space-x-4 text-sm">
                    <span className={`px-2 py-1 rounded-full ${getStatusColor(disputeInfo.statut)}`}>
                        {disputeInfo.statut}
                    </span>
                    <span className={`px-2 py-1 rounded-full ${getPriorityColor(disputeInfo.priorite)}`}>
                        Priorité {disputeInfo.priorite}
                    </span>
                    {disputeInfo.urgent && (
                        <span className="px-2 py-1 rounded-full bg-red-100 text-red-800 font-medium">
                            🚨 URGENT
                        </span>
                    )}
                </div>
                
                <p className="text-gray-600 dark:text-gray-400 mt-2">{disputeInfo.sujet}</p>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((message) => (
                    <div
                        key={message.id}
                        className={`flex ${
                            message.auteur_type === currentUserType ? 'justify-end' : 'justify-start'
                        }`}
                    >
                        <div
                            className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                message.auteur_type === currentUserType
                                    ? 'bg-blue-500 text-white'
                                    : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                            } ${getMessageTypeColor(message.type_message)}`}
                        >
                            {message.auteur_type !== currentUserType && (
                                <div className="text-sm font-medium mb-1 opacity-75">
                                    {message.auteur_nom}
                                    {message.interne && (
                                        <span className="ml-2 text-xs bg-yellow-200 text-yellow-800 px-1 rounded">
                                            Interne
                                        </span>
                                    )}
                                </div>
                            )}
                            
                            {message.type_message !== 'message' && (
                                <div className="text-xs font-medium mb-1 opacity-75">
                                    {message.type_message === 'resolution' ? '✅ Résolution' : '⚠️ Escalade'}
                                </div>
                            )}
                            
                            <div className="whitespace-pre-wrap">{message.message}</div>
                            
                            <div className="text-xs opacity-75 mt-1">
                                {formatMessageTime(message.created_at)}
                            </div>
                        </div>
                    </div>
                ))}
                
                {/* Indicateur "en train d'écrire" */}
                {otherTypingUsers.length > 0 && (
                    <div className="flex justify-start">
                        <div className="bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-4 py-2 rounded-lg text-sm animate-pulse">
                            <div className="flex items-center space-x-2">
                                <div className="flex space-x-1">
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                </div>
                                <span>{formatTypingUsers(otherTypingUsers)}</span>
                            </div>
                        </div>
                    </div>
                )}
                
                <div ref={messagesEndRef} />
            </div>

            {/* Zone de saisie */}
            <div className="border-t border-gray-200 dark:border-gray-700 p-4">
                {/* Sélecteur de type de message */}
                {currentUserType === 'admin' && (
                    <div className="mb-3">
                        <select
                            value={messageType}
                            onChange={(e) => setMessageType(e.target.value as any)}
                            className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                        >
                            <option value="message">Message normal</option>
                            <option value="resolution">Proposition de résolution</option>
                            <option value="escalation">Escalade</option>
                        </select>
                    </div>
                )}
                
                <div className="flex space-x-2">
                    <div className="flex-1">
                        <textarea
                            value={newMessage}
                            onChange={handleMessageChange}
                            onKeyPress={handleKeyPress}
                            onBlur={handleInputBlur}
                            placeholder="Tapez votre message..."
                            className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 resize-none bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            rows={2}
                            disabled={isLoading}
                        />
                    </div>
                    <div className="flex flex-col space-y-2">
                        <button
                            onClick={handleSendMessage}
                            disabled={(!newMessage.trim() && selectedFiles.length === 0) || isLoading}
                            className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isLoading ? '...' : 'Envoyer'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};
