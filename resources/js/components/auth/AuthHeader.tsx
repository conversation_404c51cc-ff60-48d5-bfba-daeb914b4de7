import { Store } from 'lucide-react';
import { Link } from '@inertiajs/react';
import { useTranslation } from '@/hooks/use-translation';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import AppearanceToggleDropdown from '@/components/appearance-dropdown';

export default function AuthHeader() {
    const { translate } = useTranslation();

    return (
        <header className="fixed top-0 left-0 right-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container mx-auto px-4">
                <nav className="flex h-16 items-center justify-between">
                    <Link href={route('welcome')} className="flex items-center space-x-2">
                        <span className="text-xl font-bold text-primary">Lorelei</span>
                        <span className="text-lg font-medium text-muted-foreground">Marchand</span>
                    </Link>
                    <div className="flex items-center space-x-4">
                        <LanguageSwitcher />
                        <AppearanceToggleDropdown />
                    </div>
                </nav>
            </div>
        </header>
    );
} 