import { useTranslation } from '@/hooks/use-translation';

export default function AuthFooter() {
    const { translate } = useTranslation();

    return (
        <footer className="fixed bottom-0 left-0 right-0 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container mx-auto px-4 py-4">
                <div className="flex flex-col items-center justify-center space-y-2 text-sm text-muted-foreground">
                    <p>
                        {translate('auth.footer.copyright', { year: new Date().getFullYear() })}
                    </p>
                    <div className="flex items-center space-x-4">
                        <a href="#" className="hover:text-primary hover:underline">
                            {translate('auth.footer.terms')}
                        </a>
                        <span>•</span>
                        <a href="#" className="hover:text-primary hover:underline">
                            {translate('auth.footer.privacy')}
                        </a>
                        <span>•</span>
                        <a href="#" className="hover:text-primary hover:underline">
                            {translate('auth.footer.help')}
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    );
} 