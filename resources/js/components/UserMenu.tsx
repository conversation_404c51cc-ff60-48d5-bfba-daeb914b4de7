import { useState } from 'react';
import { Link, router, usePage } from '@inertiajs/react';
import { User, LogOut, LogIn, UserPlus, Settings, LayoutDashboard } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { type SharedData } from '@/types';

interface UserMenuProps {
  className?: string;
}

export default function UserMenu({ className }: UserMenuProps) {
  const { auth } = usePage<SharedData>().props;
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = () => {
    setIsLoggingOut(true);
    router.post(route('logout'), {}, {
      onFinish: () => setIsLoggingOut(false),
    });
  };

  const getInitials = (name: string) => {
    return name?.length > 0 ?
      name.split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
      : '?';
  };

  if (auth.user) {
    // Utilisateur connecté
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className={`relative h-8 w-8 rounded-full ${className}`}>
            <Avatar className="h-8 w-8">
              <AvatarImage src={auth.user.avatar} alt={auth.user.name} />
              <AvatarFallback className="bg-primary text-primary-foreground">
                {getInitials(auth.user.name)}
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end" forceMount>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">{auth.user.name}</p>
              <p className="text-xs leading-none text-muted-foreground">
                {auth.user.email}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />

          {/* Dashboard - seulement si l'utilisateur a accès */}
          {/* <DropdownMenuItem asChild>
            <Link href={route('welcome')} className="cursor-pointer">
              <LayoutDashboard className="mr-2 h-4 w-4" />
              <span>Tableau de bord</span>
            </Link>
          </DropdownMenuItem> */}

          {/* Paramètres */}
          <DropdownMenuItem asChild>
            <Link href={route('profile.edit')} className="cursor-pointer">
              <Settings className="mr-2 h-4 w-4" />
              <span>Paramètres</span>
            </Link>
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* Déconnexion */}
          <DropdownMenuItem
            onClick={handleLogout}
            disabled={isLoggingOut}
            className="cursor-pointer text-red-600 focus:text-red-600 dark:text-red-400 dark:focus:text-red-400"
          >
            <LogOut className="mr-2 h-4 w-4" />
            <span>{isLoggingOut ? 'Déconnexion...' : 'Se déconnecter'}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // Utilisateur non connecté
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className={className}>
          <User className="h-5 w-5" />
          <span className="sr-only">Menu utilisateur</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-48" align="end">
        <DropdownMenuLabel>Compte</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Connexion */}
        <DropdownMenuItem asChild>
          <Link href={route('login')} className="cursor-pointer">
            <LogIn className="mr-2 h-4 w-4" />
            <span>Se connecter</span>
          </Link>
        </DropdownMenuItem>

        {/* Inscription */}
        <DropdownMenuItem asChild>
          <Link href={route('register')} className="cursor-pointer">
            <UserPlus className="mr-2 h-4 w-4" />
            <span>S'inscrire</span>
          </Link>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
