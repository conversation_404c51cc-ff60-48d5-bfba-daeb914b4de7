import { Link } from '@inertiajs/react';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import AppearanceToggleDropdown from '@/components/appearance-dropdown';
import UserMenu from '@/components/UserMenu';

interface SellerHeaderProps {
  className?: string;
}

export default function SellerHeader({ className }: SellerHeaderProps) {
  return (
    <header className={`border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 ${className}`}>
      <div className="container mx-auto px-4">
        <nav className="flex h-16 items-center justify-between">
          <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
            <span className="text-2xl font-bold text-primary">Lorelei</span>
            <span className="text-xl font-medium text-muted-foreground">Marchand</span>
          </Link>
          <div className="flex items-center space-x-2">
            <LanguageSwitcher />
            <AppearanceToggleDropdown />
            <UserMenu />
          </div>
        </nav>
      </div>
    </header>
  );
}
