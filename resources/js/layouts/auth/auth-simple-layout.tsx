import AppLogoIcon from '@/components/app-logo-icon';
import { Link } from '@inertiajs/react';
import { type PropsWithChildren } from 'react';
import AuthHeader from '@/components/auth/AuthHeader';
import AuthFooter from '@/components/auth/AuthFooter';

interface AuthLayoutProps {
    name?: string;
    title?: React.ReactNode;
    description?: string;
}

export default function AuthSimpleLayout({ children, title, description }: PropsWithChildren<AuthLayoutProps>) {
    return (
        <>
            <AuthHeader />
            <div className="bg-background min-h-svh pt-16 pb-16">
                <div className="flex min-h-[calc(100vh-8rem)] flex-col items-center justify-center gap-6 p-6 md:p-10">
                    <div className="w-full max-w-sm">
                        <div className="flex flex-col gap-8">
                            <div className="flex flex-col items-center gap-4">
                                <Link href={route('welcome')} className="flex flex-col items-center gap-2 font-medium">
                                    <div className="mb-1 flex h-9 w-9 items-center justify-center rounded-md">
                                        <AppLogoIcon className="size-9 fill-current text-[var(--foreground)] dark:text-white" />
                                    </div>
                                    <span className="sr-only">{typeof title === 'string' ? title : 'Lorelei Marchand'}</span>
                                </Link>

                                <div className="space-y-2 text-center">
                                    <div className="text-xl font-medium">{title}</div>
                                    <p className="text-muted-foreground text-center text-sm">{description}</p>
                                </div>
                            </div>
                            {children}
                        </div>
                    </div>
                </div>
            </div>
            <AuthFooter />
        </>
    );
}
