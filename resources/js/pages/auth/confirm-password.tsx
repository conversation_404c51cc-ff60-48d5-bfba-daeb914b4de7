// Components
import { Head, useForm } from '@inertiajs/react';
import { Store, LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';
import { useTranslation } from '@/hooks/use-translation';

export default function ConfirmPassword() {
    const { translate } = useTranslation();
    const { data, setData, post, processing, errors } = useForm({
        password: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('password.confirm'));
    };

    return (
        <AuthLayout
            title={
                <div className="flex items-center justify-center gap-2 text-primary">
                    <Store className="h-6 w-6" />
                    <span>Lorelei Marchand</span>
                </div>
            }
            description={String(translate('auth.confirm_password.description'))}
        >
            <Head title={String(translate('auth.confirm_password.title'))} />

            <form onSubmit={submit} className="space-y-4">
                <div className="grid gap-2">
                    <Label htmlFor="password">{translate('auth.confirm_password.password')}</Label>
                    <Input
                        id="password"
                        type="password"
                        value={data.password}
                        onChange={(e) => setData('password', e.target.value)}
                        className="mt-1 block w-full"
                        required
                        autoFocus
                        placeholder={String(translate('auth.confirm_password.password_placeholder'))}
                    />
                    <InputError message={errors.password} />
                </div>

                <div className="flex items-center justify-between">
                    <Button type="submit" disabled={processing}>
                        {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                        {translate('auth.confirm_password.submit')}
                    </Button>

                    <TextLink href={route('login')} className="text-sm">
                        {translate('auth.confirm_password.back_to_login')}
                    </TextLink>
                </div>
            </form>
        </AuthLayout>
    );
}
