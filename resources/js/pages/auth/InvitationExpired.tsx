import React, { useEffect } from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Clock, Home, Mail } from 'lucide-react';
import { initializeTheme } from '@/hooks/use-appearance';

interface Props {
    type: 'admin' | 'marchand';
}

export default function InvitationExpired({ type }: Props) {
    const isAdmin = type === 'admin';

    // Initialiser le thème au chargement de la page
    useEffect(() => {
        initializeTheme();
    }, []);

    return (
        <>
            <Head title="Invitation expirée" />
            
            <div className="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-md w-full space-y-8">
                    <div className="text-center">
                        <Clock className="mx-auto h-12 w-12 text-red-500" />
                        <h2 className="mt-6 text-3xl font-extrabold text-foreground">
                            Invitation expirée
                        </h2>
                        <p className="mt-2 text-sm text-muted-foreground">
                            Cette invitation n'est plus valide
                        </p>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-center text-red-600">
                                Lien d'invitation expiré
                            </CardTitle>
                            <CardDescription className="text-center">
                                {isAdmin 
                                    ? "Cette invitation pour rejoindre l'équipe d'administration a expiré."
                                    : "Cette invitation pour rejoindre l'équipe marchand a expiré."
                                }
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <Alert>
                                <Clock className="h-4 w-4" />
                                <AlertDescription>
                                    Les invitations {isAdmin ? 'admin' : 'marchand'} expirent après{' '}
                                    {isAdmin ? '24 heures' : '7 jours'} pour des raisons de sécurité.
                                </AlertDescription>
                            </Alert>

                            <div className="space-y-3">
                                <h4 className="font-medium text-foreground">Que faire maintenant ?</h4>
                                <ul className="text-sm text-muted-foreground space-y-2">
                                    <li className="flex items-start">
                                        <Mail className="h-4 w-4 mt-0.5 mr-2 text-blue-500" />
                                        Contactez la personne qui vous a invité pour demander une nouvelle invitation
                                    </li>
                                    <li className="flex items-start">
                                        <span className="inline-block w-4 h-4 mt-0.5 mr-2 text-blue-500">•</span>
                                        {isAdmin 
                                            ? "Un administrateur peut renvoyer l'invitation depuis le dashboard admin"
                                            : "Le propriétaire ou gestionnaire de la boutique peut renvoyer l'invitation"
                                        }
                                    </li>
                                </ul>
                            </div>

                            <div className="pt-4 space-y-3">
                                <Button asChild className="w-full">
                                    <Link href={route('welcome')}>
                                        <Home className="h-4 w-4 mr-2" />
                                        Retour à l'accueil
                                    </Link>
                                </Button>
                                
                                {!isAdmin && (
                                    <Button asChild variant="outline" className="w-full">
                                        <Link href={route('login')}>
                                            Se connecter
                                        </Link>
                                    </Button>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    <div className="text-center">
                        <p className="text-xs text-muted-foreground">
                            Besoin d'aide ? Contactez notre support à{' '}
                            <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                                <EMAIL>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}
