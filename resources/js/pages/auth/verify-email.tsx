// Components
import { Head, Link, useForm } from '@inertiajs/react';
import { Store } from 'lucide-react';
import { FormEventHandler } from 'react';

import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import AuthLayout from '@/layouts/auth-layout';
import { useTranslation } from '@/hooks/use-translation';

interface Props {
    status?: string;
}

export default function VerifyEmail({ status }: Props) {
    const { translate } = useTranslation();
    const { post } = useForm({});

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('verification.send'));
    };

    return (
        <AuthLayout
            title={
                <div className="flex items-center justify-center gap-2 text-primary">
                    <Store className="h-6 w-6" />
                    <span>Lorelei Marchand</span>
                </div>
            }
            description={String(translate('auth.verify_email.description'))}
        >
            <Head title={String(translate('auth.verify_email.title'))} />

            <div className="mb-4 text-sm text-muted-foreground">
                {translate('auth.verify_email.message')}
            </div>

            {status === 'verification-link-sent' && (
                <div className="mb-4 text-sm font-medium text-green-600">
                    {translate('auth.verify_email.success')}
                </div>
            )}

            <form onSubmit={submit} className="space-y-4">
                <div className="flex items-center justify-between">
                    <Button type="submit">
                        {translate('auth.verify_email.resend_link')}
                    </Button>

                    <Link
                        href={route('logout')}
                        method="post"
                        as="button"
                        className="text-sm text-muted-foreground hover:text-primary hover:underline"
                    >
                        {translate('auth.logout')}
                    </Link>
                </div>
            </form>
        </AuthLayout>
    );
}
