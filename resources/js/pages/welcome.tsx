import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    TrendingUp,
    ArrowRight,
    BarChart3,
    Headphones,
    CreditCard,
    CheckCircle,
    Star,
    Menu,
    Store
} from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import AppearanceToggleDropdown from '@/components/appearance-dropdown';
import AppearanceToggleTab from '@/components/appearance-tabs';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import UserMenu from '@/components/UserMenu';

interface PlanName {
    fr: string;
    en: string;
}

interface PricingPlan {
    name: PlanName;
    price: string;
    period: string;
    commission: string;
    features_fr: string[];
    features_en: string[];
    popular: boolean;
}

export default function Welcome() {
    const { auth, pricingPlans } = usePage<{
        auth: SharedData['auth'];
        pricingPlans: PricingPlan[];
    }>().props;
    const { translate } = useTranslation();
    console.log(pricingPlans);
    const current_lang = localStorage.getItem('locale') || 'fr';

    const stats = [
        { number: "10,000+", label: translate('hero.stats.merchants') },
        { number: "500,000+", label: translate('hero.stats.products') },
        { number: "98%", label: translate('hero.stats.satisfaction') }
    ];

    const benefits = [
        {
            icon: TrendingUp,
            title: translate('benefits.visibility.title'),
            description: translate('benefits.visibility.description')
        },
        {
            icon: BarChart3,
            title: translate('benefits.tools.title'),
            description: translate('benefits.tools.description')
        },
        {
            icon: Headphones,
            title: translate('benefits.support.title'),
            description: translate('benefits.support.description')
        },
        {
            icon: CreditCard,
            title: translate('benefits.payments.title'),
            description: translate('benefits.payments.description')
        }
    ];

    const steps = [
        {
            number: "1",
            title: translate('how_it_works.step1.title'),
            description: translate('how_it_works.step1.description')
        },
        {
            number: "2",
            title: translate('how_it_works.step2.title'),
            description: translate('how_it_works.step2.description')
        },
        {
            number: "3",
            title: translate('how_it_works.step3.title'),
            description: translate('how_it_works.step3.description')
        }
    ];

    const testimonials = [
        {
            name: translate('testimonials.marie.name'),
            business: translate('testimonials.marie.business'),
            content: translate('testimonials.marie.content'),
            rating: 5
        },
        {
            name: translate('testimonials.jean.name'),
            business: translate('testimonials.jean.business'),
            content: translate('testimonials.jean.content'),
            rating: 5
        },
        {
            name: translate('testimonials.fatou.name'),
            business: translate('testimonials.fatou.business'),
            content: translate('testimonials.fatou.content'),
            rating: 5
        }
    ];

    return (
        <>
            <Head title={`${translate('hero.title')} - ${translate('hero.subtitle')}`}>
                <meta name="description" content={translate('hero.description')?.toString()} />
            </Head>

            <div className="min-h-screen bg-background text-foreground">
                {/* Header */}
                <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
                    <div className="container mx-auto px-4">
                        <nav className="flex h-16 items-center justify-between">
                            {/* Logo et menu mobile */}
                            <div className="flex items-center justify-between md:w-fit w-full space-x-4 ">
                                <div className='items-center flex space-x-4 '>
                                    <Sheet>
                                        <SheetTrigger asChild>
                                            <Button variant="ghost" size="icon" className="md:hidden">
                                                <Menu className="h-5 w-5" />
                                                <span className="sr-only">{translate('header.menu')}</span>
                                            </Button>
                                        </SheetTrigger>
                                        <SheetContent side="left" className="w-full max-w-sm">
                                            <SheetHeader>
                                                <SheetTitle className="flex items-center space-x-2">
                                                    <span className="text-xl font-bold text-primary">Lorelei</span>
                                                    <span className="text-lg font-medium text-muted-foreground">Marchand</span>
                                                </SheetTitle>
                                            </SheetHeader>
                                            <div className="mt-6 space-y-4">
                                                <a href="#pricing" className="block py-2 text-muted-foreground hover:text-foreground transition-colors">
                                                    {translate('header.pricing')}
                                                </a>
                                                <a href="#services" className="block py-2 text-muted-foreground hover:text-foreground transition-colors">
                                                    {translate('header.services')}
                                                </a>
                                                <a href="#resources" className="block py-2 text-muted-foreground hover:text-foreground transition-colors">
                                                    {translate('header.resources')}
                                                </a>
                                                <div className="border-t pt-4 space-y-4">
                                                    <div className="flex items-center justify-between">
                                                        <span className="text-sm font-medium text-foreground">{translate('header.language')}</span>
                                                        <LanguageSwitcher />
                                                    </div>
                                                    <div className="flex items-center justify-between">
                                                        <span className="text-sm font-medium text-foreground">{translate('header.theme')}</span>
                                                        <AppearanceToggleDropdown />
                                                    </div>
                                                </div>
                                            </div>
                                        </SheetContent>
                                    </Sheet>

                                    <Link href="/" className="flex items-center space-x-2">
                                        <span className="text-2xl font-bold text-primary">Lorelei</span>
                                        <span className="text-xl font-medium text-muted-foreground">Marchand</span>
                                    </Link>
                                </div>
                                <UserMenu className='md:hidden'/>
                            </div>

                            {/* Navigation desktop */}
                            <div className="hidden md:flex items-center space-x-6">
                                <a href="#pricing" className="text-muted-foreground hover:text-foreground transition-colors">
                                    {translate('header.pricing')}
                                </a>
                                <a href="#services" className="text-muted-foreground hover:text-foreground transition-colors">
                                    {translate('header.services')}
                                </a>
                                <a href="#resources" className="text-muted-foreground hover:text-foreground transition-colors">
                                    {translate('header.resources')}
                                </a>
                            </div>

                            {/* Actions utilisateur */}
                            <div className="flex items-center space-x-2">
                                {/* Sélecteurs de langue et thème - desktop seulement */}
                                <div className="hidden md:flex items-center space-x-2">
                                    <LanguageSwitcher />
                                    <AppearanceToggleDropdown />
                                    <UserMenu />
                                </div>
                            </div>
                        </nav>
                    </div>
                </header>

                {/* Hero Section */}
                <section className="bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 dark:from-primary/10 dark:via-primary/20 dark:to-primary/10 py-20">
                    <div className="container mx-auto px-4">
                        <div className="text-center max-w-4xl mx-auto">
                            <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
                                {translate('hero.title')}
                            </h1>
                            <p className="text-xl md:text-2xl text-muted-foreground mb-4">
                                {translate('hero.subtitle')}
                            </p>
                            <p className="text-lg text-muted-foreground mb-8">
                                {translate('hero.description')}
                            </p>

                            {/* Stats */}
                            <div className="flex flex-wrap justify-center gap-8 mb-8">
                                {stats.map((stat, index) => (
                                    <div key={index} className="text-center">
                                        <div className="text-2xl md:text-3xl font-bold text-primary">{stat.number}</div>
                                        <div className="text-muted-foreground">{stat.label}</div>
                                    </div>
                                ))}
                            </div>

                            <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                                <Button size="lg" asChild>
                                    <Link href={auth.user ? route('seller.welcome') : route('register')}>
                                        {translate('hero.cta_start')}
                                        <ArrowRight className="ml-2 h-4 w-4" />
                                    </Link>
                                </Button>
                                <Button size="lg" variant="outline" asChild>
                                    <a href="#pricing">{translate('hero.cta_pricing')}</a>
                                </Button>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Benefits Section */}
                <section id="services" className="py-20 bg-background">
                    <div className="container mx-auto px-4">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                                {translate('benefits.title')}
                            </h2>
                            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                                {translate('benefits.subtitle')}
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                            {benefits.map((benefit, index) => (
                                <Card key={index} className="text-center hover:shadow-lg transition-all duration-300 hover:scale-105 border-border bg-card">
                                    <CardHeader>
                                        <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                                            <benefit.icon className="w-6 h-6 text-primary" />
                                        </div>
                                        <CardTitle className="text-lg text-card-foreground">{benefit.title}</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <CardDescription className="text-muted-foreground">{benefit.description}</CardDescription>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </section>

                {/* How it works */}
                <section className="py-20 bg-muted/30">
                    <div className="container mx-auto px-4">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                                {translate('how_it_works.title')}
                            </h2>
                            <p className="text-xl text-muted-foreground">
                                {translate('how_it_works.subtitle')}
                            </p>
                        </div>

                        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                            {steps.map((step, index) => (
                                <div key={index} className="text-center">
                                    <div className="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold shadow-lg">
                                        {step.number}
                                    </div>
                                    <h3 className="text-xl font-semibold text-foreground mb-2">{step.title}</h3>
                                    <p className="text-muted-foreground">{step.description}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Testimonials */}
                <section className="py-20 bg-background">
                    <div className="container mx-auto px-4">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                                {translate('testimonials.title')}
                            </h2>
                            <p className="text-xl text-muted-foreground">
                                {translate('testimonials.subtitle')}
                            </p>
                        </div>

                        <div className="grid md:grid-cols-3 gap-8">
                            {testimonials.map((testimonial, index) => (
                                <Card key={index} className="hover:shadow-lg transition-all duration-300 hover:scale-105 border-border bg-card">
                                    <CardContent className="p-6">
                                        <div className="flex mb-4">
                                            {[...Array(testimonial.rating)].map((_, i) => (
                                                <Star key={i} className="w-5 h-5 text-yellow-500 fill-current" />
                                            ))}
                                        </div>
                                        <p className="text-muted-foreground mb-4 italic">"{testimonial.content}"</p>
                                        <div>
                                            <div className="font-semibold text-card-foreground">{testimonial.name}</div>
                                            <div className="text-sm text-muted-foreground">{testimonial.business}</div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Pricing Section */}
                <section id="pricing" className="py-20 bg-muted/30">
                    <div className="container mx-auto px-4">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                                {translate('pricing.title')}
                            </h2>
                            <p className="text-xl text-muted-foreground">
                                {translate('pricing.subtitle')}
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
                            {pricingPlans.map((plan, index) => (
                                <Card key={index} className={`relative hover:shadow-xl transition-all duration-300 hover:scale-105 border-border bg-card ${plan.popular ? 'border-primary border-2 shadow-lg' : ''}`}>
                                    {plan.popular && (
                                        <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground">
                                            {translate('pricing.popular')}
                                        </Badge>
                                    )}
                                    {plan.name.fr === 'Premium' && (
                                        <Badge className="absolute -top-3 right-3 bg-violet-600 text-white">
                                            Premium
                                        </Badge>
                                    )}
                                    {plan.name.fr === 'Elite' && (
                                        <Badge className="absolute -top-3 right-3 bg-amber-500 text-white">
                                            Elite
                                        </Badge>
                                    )}
                                    <CardHeader className="text-center pb-4">
                                        <CardTitle className="text-xl text-card-foreground">
                                            {plan.name[current_lang as keyof PlanName]}
                                        </CardTitle>
                                        <div className="mt-4">
                                            <span className="text-3xl font-bold text-foreground">{plan.price}</span>
                                            <span className="text-muted-foreground">{plan.period}</span>
                                        </div>
                                        <div className="text-sm text-muted-foreground">
                                            {translate('pricing.commission')}: {plan.commission}
                                        </div>
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                        <ul className="space-y-3 mb-6">
                                            {(current_lang === 'fr' ? plan.features_fr : plan.features_en).map((feature, featureIndex) => (
                                                <li key={featureIndex} className="flex items-start text-sm">
                                                    <CheckCircle className="w-4 h-4 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                                                    <span className="text-muted-foreground">{feature}</span>
                                                </li>
                                            ))}
                                        </ul>
                                        <Button
                                            className="w-full"
                                            variant={plan.popular ? "default" : "outline"}
                                            size="lg"
                                        >
                                            {translate('pricing.choose_plan')}
                                        </Button>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-20 bg-primary text-primary-foreground">
                    <div className="container mx-auto px-4 text-center">
                        <h2 className="text-3xl md:text-4xl font-bold mb-4">
                            {translate('cta.title')}
                        </h2>
                        <p className="text-xl mb-8 text-primary-foreground/80">
                            {translate('cta.subtitle')}
                        </p>
                        <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                            <Button size="lg" variant="secondary" asChild>
                                <Link href={auth.user ? route('seller.welcome') : route('register')}>
                                    {translate('cta.start_now')}
                                    <ArrowRight className="ml-2 h-4 w-4" />
                                </Link>
                            </Button>
                            <Button size="lg" variant="secondary" className="border-primary-foreground  hover:bg-primary-foreground hover:text-primary" asChild>
                                <a href="mailto:<EMAIL>">{translate('cta.contact_support')}</a>
                            </Button>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-muted/50 border-t py-12">
                    <div className="container mx-auto px-4">
                        <div className="grid md:grid-cols-4 gap-8">
                            <div>
                                <div className="flex items-center space-x-2 mb-4">
                                    <Store className="h-6 w-6 text-primary" />
                                    <span className="text-lg font-bold text-foreground">{translate('footer.about_title')}</span>
                                </div>
                                <p className="text-muted-foreground mb-4">
                                    {translate('footer.about_description')}
                                </p>
                            </div>
                            <div>
                                <h3 className="font-semibold mb-4 text-foreground">{translate('footer.platform')}</h3>
                                <ul className="space-y-2 text-muted-foreground">
                                    <li><a href="#pricing" className="hover:text-foreground transition-colors">{translate('footer.pricing')}</a></li>
                                    <li><a href="#services" className="hover:text-foreground transition-colors">{translate('footer.services')}</a></li>
                                    <li><a href="#" className="hover:text-foreground transition-colors">{translate('footer.documentation')}</a></li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="font-semibold mb-4 text-foreground">{translate('footer.support')}</h3>
                                <ul className="space-y-2 text-muted-foreground">
                                    <li><a href="mailto:<EMAIL>" className="hover:text-foreground transition-colors">{translate('footer.contact')}</a></li>
                                    <li><a href="#" className="hover:text-foreground transition-colors">{translate('footer.faq')}</a></li>
                                    <li><a href="#" className="hover:text-foreground transition-colors">{translate('footer.guides')}</a></li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="font-semibold mb-4 text-foreground">{translate('footer.legal')}</h3>
                                <ul className="space-y-2 text-muted-foreground mb-6">
                                    <li><a href="#" className="hover:text-foreground transition-colors">{translate('footer.terms')}</a></li>
                                    <li><a href="#" className="hover:text-foreground transition-colors">{translate('footer.privacy')}</a></li>
                                </ul>

                                <div>
                                    <h4 className="font-medium mb-3 text-foreground">{translate('footer.appearance')}</h4>
                                    <AppearanceToggleTab />
                                </div>
                            </div>
                        </div>

                        {/* Contact Info */}
                        <div className="border-t border-border mt-8 pt-8">
                            <div className="grid md:grid-cols-3 gap-4 mb-6 text-sm text-muted-foreground">
                                <div className="flex items-center gap-2">
                                    <Store className="h-4 w-4" />
                                    <span>{translate('footer.address')}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <span>📞</span>
                                    <span>{translate('footer.phone')}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <span>✉️</span>
                                    <span>{translate('footer.email')}</span>
                                </div>
                            </div>

                            <div className="text-center text-muted-foreground">
                                <p>{translate('footer.copyright', { year: '2025' })}</p>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
