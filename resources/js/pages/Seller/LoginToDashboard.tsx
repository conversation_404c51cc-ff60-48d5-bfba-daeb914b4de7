import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslation } from '@/hooks/use-translation';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
    };
    marchand?: {
        nomEntreprise?: string;
    };
}

export default function LoginToDashboard({ user, marchand }: Props) {
    const { translate } = useTranslation();
    const { post, processing } = useForm({});

    const handleLogout: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('logout'), {
            onSuccess: () => {
                window.location.href = '/marchand';
            },
        });
    };

    return (
        <>
            <Head title={translate('seller.login_dashboard.title')} />

            <div className="min-h-screen bg-background flex items-center justify-center p-4">
                <Card className="w-full max-w-lg">
                    <CardHeader className="text-center">
                        <CardTitle className="text-2xl font-bold">
                            {translate('seller.login_dashboard.welcome_back')}
                        </CardTitle>
                        <CardDescription className="mt-2">
                            {marchand?.nomEntreprise || user.name}
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <p className="text-center text-muted-foreground">
                                {translate('seller.login_dashboard.description')}
                            </p>
                            
                            <form onSubmit={handleLogout} className="space-y-4">
                                <Button
                                    type="submit"
                                    className="w-full"
                                    size="lg"
                                    disabled={processing}
                                >
                                    {processing
                                        ? translate('seller.login_dashboard.redirecting')
                                        : translate('seller.login_dashboard.go_to_dashboard')
                                    }
                                </Button>
                            </form>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </>
    );
} 