<template>
  <Head title="Bienvenue sur la plateforme marchande" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Bienvenue sur la plateforme marchande
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <div v-if="!user.marchand" class="text-center">
              <h3 class="text-2xl font-bold mb-4">Commencez votre aventure en tant que marchand</h3>
              <p class="mb-6">Rejoignez notre marketplace et développez votre activité en ligne</p>
              <Link
                :href="route('seller.register')"
                class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
              >
                Commencer l'inscription
              </Link>
            </div>
            <div v-else>
              <div class="mb-8">
                <h3 class="text-2xl font-bold mb-4">État de votre inscription</h3>
                <div class="bg-gray-50 p-4 rounded-lg">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="font-medium">Statut :</p>
                      <p :class="{
                        'text-yellow-600': user.marchand.statut_validation === 'en_attente',
                        'text-green-600': user.marchand.statut_validation === 'valide',
                        'text-red-600': user.marchand.statut_validation === 'rejete',
                      }">
                        {{ getStatusLabel(user.marchand.statut_validation) }}
                      </p>
                    </div>
                    <div>
                      <p class="font-medium">Étape actuelle :</p>
                      <p>{{ getEtapeLabel(user.marchand.etape_inscription) }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="space-y-4">
                <Link
                  v-if="shouldShowBusinessInfoLink"
                  :href="route('seller.business-info')"
                  class="block w-full p-4 bg-white border rounded-lg hover:bg-gray-50"
                >
                  <div class="flex justify-between items-center">
                    <div>
                      <h4 class="font-medium">Informations commerciales</h4>
                      <p class="text-sm text-gray-500">Complétez les informations de votre entreprise</p>
                    </div>
                    <ChevronRightIcon class="h-5 w-5 text-gray-400" />
                  </div>
                </Link>

                <Link
                  v-if="shouldShowDocumentsLink"
                  :href="route('seller.documents')"
                  class="block w-full p-4 bg-white border rounded-lg hover:bg-gray-50"
                >
                  <div class="flex justify-between items-center">
                    <div>
                      <h4 class="font-medium">Documents requis</h4>
                      <p class="text-sm text-gray-500">Téléchargez les documents nécessaires</p>
                    </div>
                    <ChevronRightIcon class="h-5 w-5 text-gray-400" />
                  </div>
                </Link>

                <Link
                  v-if="shouldShowDashboardLink"
                  :href="route('dashboard.index')"
                  class="block w-full p-4 bg-white border rounded-lg hover:bg-gray-50"
                >
                  <div class="flex justify-between items-center">
                    <div>
                      <h4 class="font-medium">Tableau de bord</h4>
                      <p class="text-sm text-gray-500">Accédez à votre espace marchand</p>
                    </div>
                    <ChevronRightIcon class="h-5 w-5 text-gray-400" />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3'
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue'
import { ChevronRightIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
  user: Object,
})

const getStatusLabel = (status) => {
  const labels = {
    'en_attente': 'En attente de validation',
    'valide': 'Compte validé',
    'rejete': 'Inscription rejetée',
    'suspendu': 'Compte suspendu'
  }
  return labels[status] || status
}

const getEtapeLabel = (etape) => {
  const labels = {
    'business_info': 'Informations commerciales',
    'documents': 'Soumission des documents',
    'validation': 'En attente de validation',
    'complete': 'Inscription complète'
  }
  return labels[etape] || etape
}

const shouldShowBusinessInfoLink = computed(() => {
  return !user.marchand || user.marchand.etape_inscription === 'business_info'
})

const shouldShowDocumentsLink = computed(() => {
  return user.marchand && ['business_info', 'documents'].includes(user.marchand.etape_inscription)
})

const shouldShowDashboardLink = computed(() => {
  return user.marchand && user.marchand.statut_validation === 'valide'
})
</script> 