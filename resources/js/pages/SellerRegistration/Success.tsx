import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
    CheckCircle, 
    Clock, 
    Mail, 
    Phone, 
    FileText, 
    Store,
    ArrowRight,
    Calendar,
    Users,
    TrendingUp
} from 'lucide-react';

interface Props {
    
    validation:{
        business_info: {
            nomEntreprise: string;
            type_business: string;
            email_business?: string;
            telephone_principal: string;
            etape_inscription: string;
            statut_validation: string;
            date_soumission_documents: string;
        };
        submitted_at: string;
        id: number;
    }
}


export default function Success({ validation }: Props) {
    const nextSteps = [
        {
            icon: Clock,
            title: "Validation en cours",
            description: "Notre équipe examine vos documents sous 48h ouvrées",
            status: "current"
        },
        {
            icon: Mail,
            title: "Notification par email",
            description: "Vous recevrez un email dès que votre dossier sera validé",
            status: "pending"
        },
        {
            icon: Store,
            title: "Accès au dashboard",
            description: "Commencez à ajouter vos produits et gérer votre boutique",
            status: "pending"
        }
    ];

    const benefits = [
        {
            icon: TrendingUp,
            title: "Augmentez vos ventes",
            description: "Accédez à des milliers de clients potentiels"
        },
        {
            icon: Users,
            title: "Support dédié",
            description: "Équipe support disponible pour vous accompagner"
        },
        {
            icon: FileText,
            title: "Outils de gestion",
            description: "Dashboard complet pour gérer votre activité"
        }
    ];

    const getBusinessTypeLabel = (type: string) => {
        const types = {
            'individuel': 'Entrepreneur individuel',
            'entreprise': 'Entreprise',
            'cooperative': 'Coopérative',
            'grande_entreprise': 'Grande entreprise'
        };
        return types[type as keyof typeof types] || type;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('fr-FR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <>
            <Head title="Inscription réussie - Lorelei Seller" />
            
            <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 dark:bg-background">
                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    {/* Header de succès */}
                    <div className="text-center mb-8">
                        <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-4">
                            <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-foreground mb-2">
                            Félicitations ! 🎉
                        </h1>
                        <p className="text-xl text-gray-600 dark:text-muted-foreground mb-4">
                            Votre inscription marchand a été soumise avec succès
                        </p>
                        <Badge className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 px-4 py-2">
                            Dossier en cours de validation
                        </Badge>
                    </div>

                    {/* Résumé de l'inscription */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle>Résumé de votre inscription</CardTitle>
                            <CardDescription>
                                Voici les informations que vous avez soumises
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <div>
                                        <label className="text-sm font-medium text-gray-500 dark:text-muted-foreground">Entreprise</label>
                                        <p className="text-gray-900 dark:text-foreground">{validation?.business_info?.nomEntreprise}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500 dark:text-muted-foreground">Type d'entreprise</label>
                                        <p className="text-gray-900 dark:text-foreground">{getBusinessTypeLabel(validation?.business_info?.type_business)}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500 dark:text-muted-foreground">Téléphone</label>
                                        <p className="text-gray-900 dark:text-foreground">{validation?.business_info?.telephone_principal}</p>
                                    </div>
                                </div>
                                <div className="space-y-3">
                                    {validation?.business_info?.email_business && (
                                        <div>
                                            <label className="text-sm font-medium text-gray-500 dark:text-muted-foreground">Email professionnel</label>
                                            <p className="text-gray-900 dark:text-foreground">{validation?.business_info?.email_business}</p>
                                        </div>
                                    )}
                                    <div>
                                        <label className="text-sm font-medium text-gray-500 dark:text-muted-foreground">Date de soumission</label>
                                        <p className="text-gray-900 dark:text-foreground">{formatDate(validation?.submitted_at)}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500 dark:text-muted-foreground">Numéro de dossier</label>
                                        <p className="text-gray-900 dark:text-foreground font-mono">#{validation?.id.toString().padStart(6, '0')}</p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Prochaines étapes */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle>Prochaines étapes</CardTitle>
                            <CardDescription>
                                Voici ce qui va se passer maintenant
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {nextSteps.map((step, index) => (
                                    <div key={index} className="flex items-start space-x-4">
                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                            step.status === 'current'
                                                ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                                                : 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500'
                                        }`}>
                                            <step.icon className="w-4 h-4" />
                                        </div>
                                        <div className="flex-1">
                                            <h3 className={`font-medium ${
                                                step.status === 'current'
                                                    ? 'text-blue-900 dark:text-blue-400'
                                                    : 'text-gray-900 dark:text-foreground'
                                            }`}>
                                                {step.title}
                                            </h3>
                                            <p className="text-gray-600 dark:text-muted-foreground text-sm">{step.description}</p>
                                        </div>
                                        {step.status === 'current' && (
                                            <Badge className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400">En cours</Badge>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Informations importantes */}
                    <Alert className="mb-8">
                        <Clock className="h-4 w-4" />
                        <AlertDescription>
                            <strong>Délai de validation :</strong> Notre équipe examine votre dossier sous 48h ouvrées. 
                            Vous recevrez un email de confirmation dès que votre compte sera activé.
                        </AlertDescription>
                    </Alert>

                    {/* Avantages */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle>Ce qui vous attend</CardTitle>
                            <CardDescription>
                                Découvrez les avantages de la plateforme Lorelei
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid md:grid-cols-3 gap-6">
                                {benefits.map((benefit, index) => (
                                    <div key={index} className="text-center">
                                        <div className="mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mb-3">
                                            <benefit.icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                                        </div>
                                        <h3 className="font-medium text-gray-900 dark:text-foreground mb-2">{benefit.title}</h3>
                                        <p className="text-sm text-gray-600 dark:text-muted-foreground">{benefit.description}</p>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Actions */}
                    <div className="text-center space-y-4">
                        <div className="space-x-4">
                            <Button asChild>
                                <Link href={route('welcome')}>
                                    Retour à l'accueil
                                </Link>
                            </Button>
                            <Button variant="outline" asChild>
                                <a href="mailto:<EMAIL>">
                                    <Mail className="w-4 h-4 mr-2" />
                                    Contacter le support
                                </a>
                            </Button>
                        </div>
                        
                        <p className="text-sm text-gray-500 dark:text-muted-foreground">
                            Des questions ? Notre équipe support est disponible à{' '}
                            <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 hover:underline">
                                <EMAIL>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}
