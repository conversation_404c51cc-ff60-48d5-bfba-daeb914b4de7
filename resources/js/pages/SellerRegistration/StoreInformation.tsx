import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Store, Package, Building } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import SellerHeader from '@/components/SellerHeader';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
    };
    marchand?: {
        nom_boutique?: string;
        description_boutique?: string;
        a_codes_upc?: string;
        possede_marque?: string;
        type_vendeur?: string;
        categories_produits?: string;
        volume_ventes_mensuel?: string;
    };
}

export default function StoreInformation({ user, marchand }: Props) {
    const { translate } = useTranslation();

    const { data, setData, post, processing, errors } = useForm({
        nom_boutique: marchand?.nom_boutique || '',
        description_boutique: marchand?.description_boutique || '',
        a_codes_upc: marchand?.a_codes_upc || 'no',
        possede_marque: marchand?.possede_marque || 'no',
        type_vendeur: marchand?.type_vendeur || 'individual',
        categories_produits: marchand?.categories_produits || '',
        volume_ventes_mensuel: marchand?.volume_ventes_mensuel || '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('seller.store.store'));
    };

    return (
        <>
            <Head title={translate('seller.store.title')} />

            <div className="min-h-screen bg-background text-foreground">
                <SellerHeader />

                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    {/* Progress Bar */}
                    <div className="mb-8">
                        <div className="flex items-center justify-center mb-6">
                            <div className="flex items-center space-x-4">
                                {/* Étape 1 - Complétée */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center">
                                        ✓
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        {translate('seller.steps.information')}
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-primary"></div>

                                {/* Étape 2 - Complétée */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center">
                                        ✓
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        {translate('seller.steps.billing')}
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-primary"></div>

                                {/* Étape 3 - Active */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold">
                                        3
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        {translate('seller.steps.store')}
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-border"></div>

                                {/* Étape 4 */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold">
                                        4
                                    </div>
                                    <span className="text-sm text-muted-foreground mt-2">
                                        {translate('seller.steps.verification')}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <Progress value={75} className="h-2" />
                    </div>

                    <Card className="border-border bg-card">
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2 text-card-foreground">
                                <Store className="w-5 h-5 text-primary" />
                                <span>{translate('seller.store.form_title')}</span>
                            </CardTitle>
                            <CardDescription className="text-muted-foreground">
                                {translate('seller.store.form_description')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={submit} className="space-y-8">
                                {/* Informations de base de la boutique */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2 flex items-center space-x-2">
                                        <Building className="w-4 h-4" />
                                        <span>{translate('seller.store.basic_info')}</span>
                                    </h3>

                                    <div className="space-y-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="nom_boutique">{translate('seller.store.store_name')} *</Label>
                                            <Input
                                                id="nom_boutique"
                                                value={data.nom_boutique}
                                                onChange={(e) => setData('nom_boutique', e.target.value)}
                                                placeholder={translate('seller.store.store_name_placeholder')}
                                                className={errors.nom_boutique ? 'border-destructive' : ''}
                                            />
                                            {errors.nom_boutique && (
                                                <p className="text-sm text-destructive">{errors.nom_boutique}</p>
                                            )}
                                            <p className="text-sm text-muted-foreground">
                                                {translate('seller.store.store_name_help')}
                                            </p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="description_boutique">{translate('seller.store.store_description')}</Label>
                                            <Textarea
                                                id="description_boutique"
                                                value={data.description_boutique}
                                                onChange={(e) => setData('description_boutique', e.target.value)}
                                                placeholder={translate('seller.store.store_description_placeholder')}
                                                rows={4}
                                                className={errors.description_boutique ? 'border-destructive' : ''}
                                            />
                                            {errors.description_boutique && (
                                                <p className="text-sm text-destructive">{errors.description_boutique}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="type_vendeur">{translate('seller.store.seller_type')} *</Label>
                                            <RadioGroup
                                                value={data.type_vendeur}
                                                onValueChange={(value) => setData('type_vendeur', value)}
                                                className="grid grid-cols-1 md:grid-cols-2 gap-4"
                                            >
                                                <div className="flex items-center space-x-2 border rounded-lg p-4 hover:bg-muted/50">
                                                    <RadioGroupItem value="individual" id="individual" />
                                                    <Label htmlFor="individual" className="flex-1 cursor-pointer">
                                                        <div className="font-medium">{translate('seller.store.individual')}</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            {translate('seller.store.individual_description')}
                                                        </div>
                                                    </Label>
                                                </div>
                                                <div className="flex items-center space-x-2 border rounded-lg p-4 hover:bg-muted/50">
                                                    <RadioGroupItem value="business" id="business" />
                                                    <Label htmlFor="business" className="flex-1 cursor-pointer">
                                                        <div className="font-medium">{translate('seller.store.business')}</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            {translate('seller.store.business_description')}
                                                        </div>
                                                    </Label>
                                                </div>
                                            </RadioGroup>
                                            {errors.type_vendeur && (
                                                <p className="text-sm text-destructive">{errors.type_vendeur}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Questions sur les produits */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2 flex items-center space-x-2">
                                        <Package className="w-4 h-4" />
                                        <span>{translate('seller.store.product_info')}</span>
                                    </h3>

                                    <div className="space-y-6">
                                        <div className="space-y-4">
                                            <Label className="text-base font-medium">
                                                {translate('seller.store.has_upc_codes')}
                                            </Label>
                                            <RadioGroup
                                                value={data.a_codes_upc}
                                                onValueChange={(value) => setData('a_codes_upc', value)}
                                                className="grid grid-cols-1 md:grid-cols-2 gap-4"
                                            >
                                                <div className="flex items-center space-x-2 border rounded-lg p-4 hover:bg-muted/50">
                                                    <RadioGroupItem value="yes" id="upc_yes" />
                                                    <Label htmlFor="upc_yes" className="cursor-pointer">
                                                        {translate('seller.store.yes')}
                                                    </Label>
                                                </div>
                                                <div className="flex items-center space-x-2 border rounded-lg p-4 hover:bg-muted/50">
                                                    <RadioGroupItem value="no" id="upc_no" />
                                                    <Label htmlFor="upc_no" className="cursor-pointer">
                                                        {translate('seller.store.no')}
                                                    </Label>
                                                </div>
                                            </RadioGroup>
                                        </div>

                                        <div className="space-y-4">
                                            <Label className="text-base font-medium">
                                                {translate('seller.store.owns_brand')}
                                            </Label>
                                            <RadioGroup
                                                value={data.possede_marque}
                                                onValueChange={(value) => setData('possede_marque', value)}
                                                className="grid grid-cols-1 md:grid-cols-3 gap-4"
                                            >
                                                <div className="flex items-center space-x-2 border rounded-lg p-4 hover:bg-muted/50">
                                                    <RadioGroupItem value="yes" id="brand_yes" />
                                                    <Label htmlFor="brand_yes" className="cursor-pointer">
                                                        {translate('seller.store.yes')}
                                                    </Label>
                                                </div>
                                                <div className="flex items-center space-x-2 border rounded-lg p-4 hover:bg-muted/50">
                                                    <RadioGroupItem value="no" id="brand_no" />
                                                    <Label htmlFor="brand_no" className="cursor-pointer">
                                                        {translate('seller.store.no')}
                                                    </Label>
                                                </div>
                                                <div className="flex items-center space-x-2 border rounded-lg p-4 hover:bg-muted/50">
                                                    <RadioGroupItem value="some" id="brand_some" />
                                                    <Label htmlFor="brand_some" className="cursor-pointer">
                                                        {translate('seller.store.some_of_them')}
                                                    </Label>
                                                </div>
                                            </RadioGroup>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="categories_produits">{translate('seller.store.product_categories')}</Label>
                                            <Textarea
                                                id="categories_produits"
                                                value={data.categories_produits}
                                                onChange={(e) => setData('categories_produits', e.target.value)}
                                                placeholder={translate('seller.store.product_categories_placeholder')}
                                                rows={3}
                                                className={errors.categories_produits ? 'border-destructive' : ''}
                                            />
                                            {errors.categories_produits && (
                                                <p className="text-sm text-destructive">{errors.categories_produits}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="volume_ventes_mensuel">{translate('seller.store.monthly_sales_volume')}</Label>
                                            <Input
                                                id="volume_ventes_mensuel"
                                                value={data.volume_ventes_mensuel}
                                                onChange={(e) => setData('volume_ventes_mensuel', e.target.value)}
                                                placeholder={translate('seller.store.monthly_sales_placeholder')}
                                                className={errors.volume_ventes_mensuel ? 'border-destructive' : ''}
                                            />
                                            {errors.volume_ventes_mensuel && (
                                                <p className="text-sm text-destructive">{errors.volume_ventes_mensuel}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Actions */}
                                <div className="flex justify-between pt-6 border-t border-border">
                                    <Button type="button" variant="outline" asChild>
                                        <a href={route('seller.billing')}>
                                            {translate('seller.store.previous')}
                                        </a>
                                    </Button>

                                    <Button type="submit" disabled={processing} className="min-w-[150px]">
                                        {processing
                                            ? translate('seller.store.saving')
                                            : translate('seller.store.continue')
                                        }
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
