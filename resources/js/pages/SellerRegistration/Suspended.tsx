import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
    AlertTriangle, 
    Mail, 
    Phone, 
    FileText, 
    MessageCircle,
    Clock,
    Shield,
    Users,
    Scale
} from 'lucide-react';

export default function Suspended() {
    const suspensionReasons = [
        {
            icon: FileText,
            title: "Documents expirés ou invalides",
            description: "Certains de vos documents ont expiré ou ne sont plus valides"
        },
        {
            icon: Scale,
            title: "Non-conformité réglementaire",
            description: "Votre activité ne respecte plus certaines réglementations"
        },
        {
            icon: Users,
            title: "Plaintes clients répétées",
            description: "Plusieurs plaintes clients nécessitent une révision de votre compte"
        },
        {
            icon: Shield,
            title: "Problème de sécurité",
            description: "Activité suspecte détectée nécessitant une vérification"
        }
    ];

    const reactivationSteps = [
        {
            step: "1",
            title: "Contactez notre équipe",
            description: "Obtenez les détails spécifiques de votre suspension et les actions requises"
        },
        {
            step: "2",
            title: "Corrigez les problèmes",
            description: "Mettez à jour vos documents ou corrigez les problèmes identifiés"
        },
        {
            step: "3",
            title: "Soumettez votre demande",
            description: "Envoyez les documents corrigés et votre demande de réactivation"
        },
        {
            step: "4",
            title: "Attendre la validation",
            description: "Notre équipe examine votre demande sous 72h ouvrées"
        }
    ];

    return (
        <>
            <Head title="Compte suspendu - Lorelei Seller" />
            
            <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-orange-50">
                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    {/* Header */}
                    <div className="text-center mb-8">
                        <div className="mx-auto w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                            <AlertTriangle className="w-8 h-8 text-yellow-600" />
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            Compte temporairement suspendu
                        </h1>
                        <p className="text-xl text-gray-600 mb-4">
                            Votre compte marchand a été temporairement suspendu
                        </p>
                        <Badge className="bg-yellow-100 text-yellow-800 px-4 py-2">
                            Suspension temporaire
                        </Badge>
                    </div>

                    {/* Message principal */}
                    <Alert className="mb-8 border-yellow-200 bg-yellow-50">
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        <AlertDescription className="text-yellow-800">
                            <strong>Suspension temporaire :</strong> Votre compte a été suspendu pour des raisons de sécurité 
                            ou de conformité. Cette suspension peut être levée une fois les problèmes résolus.
                        </AlertDescription>
                    </Alert>

                    {/* Raisons possibles */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle>Raisons possibles de suspension</CardTitle>
                            <CardDescription>
                                Voici les principales raisons qui peuvent conduire à une suspension temporaire
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid md:grid-cols-2 gap-6">
                                {suspensionReasons.map((reason, index) => (
                                    <div key={index} className="flex items-start space-x-3">
                                        <div className="w-8 h-8 bg-yellow-100 text-yellow-600 rounded-full flex items-center justify-center flex-shrink-0">
                                            <reason.icon className="w-4 h-4" />
                                        </div>
                                        <div>
                                            <h3 className="font-medium text-gray-900 mb-1">
                                                {reason.title}
                                            </h3>
                                            <p className="text-gray-600 text-sm">
                                                {reason.description}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Processus de réactivation */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle>Processus de réactivation</CardTitle>
                            <CardDescription>
                                Suivez ces étapes pour demander la réactivation de votre compte
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-6">
                                {reactivationSteps.map((step, index) => (
                                    <div key={index} className="flex items-start space-x-4">
                                        <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center flex-shrink-0 font-semibold text-sm">
                                            {step.step}
                                        </div>
                                        <div className="flex-1">
                                            <h3 className="font-medium text-gray-900 mb-1">
                                                {step.title}
                                            </h3>
                                            <p className="text-gray-600 text-sm">
                                                {step.description}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Informations importantes */}
                    <div className="grid md:grid-cols-2 gap-6 mb-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">⏱️ Délais</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Réponse support :</span>
                                    <span className="font-medium">24h</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Examen dossier :</span>
                                    <span className="font-medium">72h</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Réactivation :</span>
                                    <span className="font-medium">Immédiate</span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">📋 Documents requis</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <ul className="text-sm text-gray-600 space-y-1">
                                    <li>• Documents d'identité à jour</li>
                                    <li>• Justificatifs d'activité récents</li>
                                    <li>• Explications écrites si nécessaire</li>
                                    <li>• Preuves de conformité</li>
                                </ul>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Pendant la suspension */}
                    <Alert className="mb-8">
                        <Clock className="h-4 w-4" />
                        <AlertDescription>
                            <strong>Pendant la suspension :</strong> Vous ne pouvez pas accéder à votre dashboard marchand 
                            ni recevoir de nouvelles commandes. Vos produits existants restent visibles mais non commandables.
                        </AlertDescription>
                    </Alert>

                    {/* Actions */}
                    <div className="text-center space-y-4">
                        <div className="space-x-4">
                            <Button asChild>
                                <a href="mailto:<EMAIL>?subject=Demande de réactivation - Compte suspendu">
                                    <Mail className="w-4 h-4 mr-2" />
                                    Demander la réactivation
                                </a>
                            </Button>
                            <Button variant="outline" asChild>
                                <a href="tel:+237123456789">
                                    <Phone className="w-4 h-4 mr-2" />
                                    Appeler le support
                                </a>
                            </Button>
                        </div>
                        
                        <div className="text-sm text-gray-500 space-y-2">
                            <p>
                                <strong>Email support :</strong>{' '}
                                <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                                    <EMAIL>
                                </a>
                            </p>
                            <p>
                                <strong>Téléphone :</strong>{' '}
                                <a href="tel:+237123456789" className="text-blue-600 hover:underline">
                                    +237 123 456 789
                                </a>
                            </p>
                            <p className="text-xs">
                                Support disponible 24/7 pour les comptes suspendus
                            </p>
                        </div>

                        <div className="pt-4">
                            <Button variant="ghost" asChild>
                                <Link href={route('welcome')}>
                                    Retour à l'accueil
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
