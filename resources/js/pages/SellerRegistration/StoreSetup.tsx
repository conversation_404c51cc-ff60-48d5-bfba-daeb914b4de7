import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Store, Package, Building, Globe } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import SellerHeader from '@/components/SellerHeader';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
    };
    validation?: any;
    storeInfo: {
        nom_boutique?: string;
        description_business?: string;
        categories_produits?: string[];
        site_web?: string;
        accepte_conditions?: boolean;
    };
}

export default function StoreSetup({ user, validation, storeInfo }: Props) {
    const { translate } = useTranslation();

    const { data, setData, post, processing, errors } = useForm({
        nom_boutique: storeInfo?.nom_boutique || '',
        description_business: storeInfo?.description_business || '',
        categories_produits: storeInfo?.categories_produits || [],
        site_web: storeInfo?.site_web || '',
        accepte_conditions: storeInfo?.accepte_conditions || false,
    });

    const [availableCategories] = useState([
        'Vêtements et Mode',
        'Électronique',
        'Maison et Jardin',
        'Beauté et Santé',
        'Sports et Loisirs',
        'Livres et Médias',
        'Jouets et Enfants',
        'Automobile',
        'Alimentation',
        'Bijoux et Accessoires',
        'Art et Artisanat',
        'Autres'
    ]);

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('seller.store-setup.store'), {
            onError: (errors) => {
                // Scroll vers le premier champ avec erreur
                setTimeout(() => {
                    const firstErrorField = Object.keys(errors)[0];
                    if (firstErrorField) {
                        const element = document.getElementById(firstErrorField);
                        element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        element?.focus();
                    }
                }, 100);
            },
        });
    };

    const handleCategoryChange = (category: string, checked: boolean) => {
        if (checked) {
            setData('categories_produits', [...data.categories_produits, category]);
        } else {
            setData('categories_produits', data.categories_produits.filter(c => c !== category));
        }
    };

    return (
        <>
            <Head title="Configuration de la boutique - Inscription marchand" />

            <div className="min-h-screen bg-background text-foreground">
                <SellerHeader />

                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    {/* Progress Bar */}
                    <div className="mb-8">
                        <div className="flex items-center justify-center mb-6">
                            <div className="flex items-center space-x-4">
                                {/* Étape 1 - Complétée */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center">
                                        ✓
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        Informations personnelles
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-primary"></div>

                                {/* Étape 2 - Complétée */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center">
                                        ✓
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        Facturation
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-primary"></div>

                                {/* Étape 3 - Active */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold">
                                        3
                                    </div>
                                    <span className="text-sm font-medium text-foreground mt-2">
                                        Boutique
                                    </span>
                                </div>
                                <div className="w-16 h-0.5 bg-border"></div>

                                {/* Étape 4 */}
                                <div className="flex flex-col items-center">
                                    <div className="w-10 h-10 bg-muted text-muted-foreground rounded-full flex items-center justify-center font-semibold">
                                        4
                                    </div>
                                    <span className="text-sm text-muted-foreground mt-2">
                                        Vérification
                                    </span>
                                </div>
                            </div>
                        </div>
                        <Progress value={75} className="h-2" />
                    </div>

                    {/* Afficher les erreurs globales */}
                    {(errors as any).error && (
                        <Alert variant="destructive" className="mb-6">
                            <AlertDescription>{(errors as any).error}</AlertDescription>
                        </Alert>
                    )}

                    <Card className="border-border bg-card">
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2 text-card-foreground">
                                <Store className="w-5 h-5 text-primary" />
                                <span>Configuration de votre boutique</span>
                            </CardTitle>
                            <CardDescription className="text-muted-foreground">
                                Configurez votre boutique en ligne et définissez vos catégories de produits.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={submit} className="space-y-8">
                                {/* Informations de base de la boutique */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2 flex items-center space-x-2">
                                        <Building className="w-4 h-4" />
                                        <span>Informations de base</span>
                                    </h3>

                                    <div className="space-y-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="nom_boutique">Nom de la boutique *</Label>
                                            <Input
                                                id="nom_boutique"
                                                value={data.nom_boutique}
                                                onChange={(e) => setData('nom_boutique', e.target.value)}
                                                placeholder="Ex: Ma Boutique Mode"
                                                className={errors?.nom_boutique ? 'field-error' : ''}
                                            />
                                            {errors?.nom_boutique && (
                                                <p className="error-message">{errors.nom_boutique}</p>
                                            )}
                                            <p className="text-sm text-muted-foreground">
                                                Ce nom apparaîtra sur votre boutique en ligne
                                            </p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="description_business">Description de votre activité *</Label>
                                            <Textarea
                                                id="description_business"
                                                value={data.description_business}
                                                onChange={(e) => setData('description_business', e.target.value)}
                                                placeholder="Décrivez votre activité, vos produits et ce qui vous différencie..."
                                                rows={4}
                                                className={errors?.description_business ? 'field-error' : ''}
                                            />
                                            {errors?.description_business && (
                                                <p className="error-message">{errors.description_business}</p>
                                            )}
                                            <p className="text-sm text-muted-foreground">
                                                Cette description aidera les clients à comprendre votre activité
                                            </p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="site_web">Site web (optionnel)</Label>
                                            <Input
                                                id="site_web"
                                                type="url"
                                                value={data.site_web}
                                                onChange={(e) => setData('site_web', e.target.value)}
                                                placeholder="https://monsite.com"
                                                className={errors?.site_web ? 'field-error' : ''}
                                            />
                                            {errors?.site_web && (
                                                <p className="error-message">{errors.site_web}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Catégories de produits */}
                                <div className="space-y-6">
                                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2 flex items-center space-x-2">
                                        <Package className="w-4 h-4" />
                                        <span>Catégories de produits</span>
                                    </h3>

                                    <div className="space-y-4">
                                        <Label className="text-base font-medium">
                                            Sélectionnez les catégories de produits que vous vendez *
                                        </Label>
                                        
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                            {availableCategories.map((category) => (
                                                <div key={category} className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50">
                                                    <Checkbox
                                                        id={`category-${category}`}
                                                        checked={data.categories_produits.includes(category)}
                                                        onCheckedChange={(checked) => handleCategoryChange(category, checked as boolean)}
                                                    />
                                                    <Label htmlFor={`category-${category}`} className="cursor-pointer text-sm">
                                                        {category}
                                                    </Label>
                                                </div>
                                            ))}
                                        </div>
                                        
                                        {errors?.categories_produits && (
                                            <p className="error-message">{errors.categories_produits}</p>
                                        )}
                                        
                                        <p className="text-sm text-muted-foreground">
                                            Sélectionnez au moins une catégorie. Vous pourrez modifier ces catégories plus tard.
                                        </p>
                                    </div>
                                </div>

                                {/* Conditions d'utilisation */}
                                <div className="space-y-6">
                                    <div className="flex items-center space-x-2 p-4 border rounded-lg">
                                        <Checkbox
                                            id="accepte_conditions"
                                            checked={data.accepte_conditions}
                                            onCheckedChange={(checked) => setData('accepte_conditions', checked as boolean)}
                                            className={errors?.accepte_conditions ? 'border-destructive' : ''}
                                        />
                                        <Label htmlFor="accepte_conditions" className="cursor-pointer text-sm">
                                            J'accepte les <a href="#" className="text-primary hover:underline">conditions d'utilisation</a> et 
                                            la <a href="#" className="text-primary hover:underline">politique de confidentialité</a> de la plateforme marchand *
                                        </Label>
                                    </div>
                                    {errors?.accepte_conditions && (
                                        <p className="error-message">{errors.accepte_conditions}</p>
                                    )}
                                </div>

                                {/* Actions */}
                                <div className="flex justify-between pt-6 border-t border-border">
                                    <Button type="button" variant="outline" asChild>
                                        <a href={route('seller.billing')}>
                                            Retour
                                        </a>
                                    </Button>

                                    <Button type="submit" disabled={processing} className="min-w-[150px]">
                                        {processing ? 'Enregistrement...' : 'Continuer'}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
