@php
    $dispute = $dispute->load(['messages' => function($query) {
        $query->publics()->orderBy('created_at', 'asc');
    }, 'client', 'marchand', 'commandePrincipale']);
@endphp

<div class="space-y-6">
    {{-- En-tête du litige --}}
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <h3 class="font-semibold text-gray-900 dark:text-white">{{ $dispute->sujet }}</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $dispute->type_litige_formate }}</p>
                <div class="flex items-center gap-2 mt-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        @if($dispute->priorite === 'critique') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                        @elseif($dispute->priorite === 'haute') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                        @elseif($dispute->priorite === 'normale') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                        @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                        {{ $dispute->priorite_formate }}
                    </span>
                    @if($dispute->urgent)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            🔥 URGENT
                        </span>
                    @endif
                </div>
            </div>
            
            <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">Client</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ $dispute->client ? $dispute->client->prenom . ' ' . $dispute->client->nom : 'Client supprimé' }}
                </p>
                <p class="text-sm font-medium text-gray-900 dark:text-white mt-2">Marchand</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ $dispute->marchand ? $dispute->marchand->nomEntreprise : 'Marchand supprimé' }}
                </p>
            </div>
            
            <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">Commande</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ $dispute->commandePrincipale ? $dispute->commandePrincipale->numero_commande : 'N/A' }}
                </p>
                @if($dispute->montant_conteste)
                    <p class="text-sm font-medium text-gray-900 dark:text-white mt-2">Montant contesté</p>
                    <p class="text-sm text-red-600 dark:text-red-400 font-semibold">
                        {{ number_format($dispute->montant_conteste, 0, ',', ' ') }} FCFA
                    </p>
                @endif
            </div>
        </div>
        
        @if($dispute->description)
            <div class="mt-4 p-3 bg-white dark:bg-gray-700 rounded border-l-4 border-blue-500">
                <p class="text-sm font-medium text-gray-900 dark:text-white">Description initiale :</p>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $dispute->description }}</p>
            </div>
        @endif
    </div>

    {{-- Zone de chat --}}
    <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg">
        {{-- Messages --}}
        <div class="h-96 overflow-y-auto p-4 space-y-4" id="chat-messages">
            @forelse($dispute->messages as $message)
                <div class="flex {{ $message->auteur_type === 'admin' ? 'justify-end' : 'justify-start' }}">
                    <div class="max-w-xs lg:max-w-md">
                        {{-- Badge auteur --}}
                        <div class="flex items-center gap-2 mb-1 {{ $message->auteur_type === 'admin' ? 'justify-end' : 'justify-start' }}">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                @if($message->auteur_type === 'client') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                @elseif($message->auteur_type === 'admin') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                @elseif($message->auteur_type === 'marchand') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                                @if($message->auteur_type === 'client') 👤 Client
                                @elseif($message->auteur_type === 'admin') 🛡️ Support
                                @elseif($message->auteur_type === 'marchand') 🏪 Marchand
                                @else 🤖 Système @endif
                            </span>
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                {{ $message->created_at->format('d/m/Y H:i') }}
                            </span>
                        </div>
                        
                        {{-- Bulle de message --}}
                        <div class="rounded-lg px-4 py-2 {{ $message->auteur_type === 'admin' 
                            ? 'bg-green-500 text-white' 
                            : ($message->auteur_type === 'client' 
                                ? 'bg-blue-500 text-white' 
                                : 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white') }}">
                            
                            @if($message->type_message === 'solution_proposee')
                                <div class="flex items-center gap-2 mb-2">
                                    <span class="text-yellow-300">💡</span>
                                    <span class="font-semibold">Solution proposée</span>
                                </div>
                            @elseif($message->type_message === 'resolution')
                                <div class="flex items-center gap-2 mb-2">
                                    <span class="text-green-300">✅</span>
                                    <span class="font-semibold">Résolution</span>
                                </div>
                            @elseif($message->type_message === 'escalade')
                                <div class="flex items-center gap-2 mb-2">
                                    <span class="text-red-300">⚠️</span>
                                    <span class="font-semibold">Escalade</span>
                                </div>
                            @endif
                            
                            <p class="text-sm whitespace-pre-wrap">{{ $message->message }}</p>
                            
                            @if($message->a_pieces_jointes)
                                <div class="mt-2 pt-2 border-t border-white/20">
                                    <p class="text-xs opacity-75">📎 {{ $message->nombre_pieces_jointes }} pièce(s) jointe(s)</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">Aucun message pour le moment</p>
                </div>
            @endforelse
        </div>
        
        {{-- Formulaire de réponse --}}
        <div class="border-t border-gray-200 dark:border-gray-700 p-4">
            <form id="admin-response-form" class="space-y-3">
                @csrf
                <input type="hidden" name="dispute_id" value="{{ $dispute->id }}">
                
                {{-- Réponses rapides --}}
                <div class="flex flex-wrap gap-2">
                    <button type="button" class="quick-response-btn px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-full transition-colors"
                            data-message="Bonjour, nous avons bien reçu votre demande et l'étudions attentivement.">
                        📋 Accusé de réception
                    </button>
                    <button type="button" class="quick-response-btn px-3 py-1 text-xs bg-green-100 hover:bg-green-200 text-green-800 rounded-full transition-colors"
                            data-message="Nous avons contacté le marchand pour résoudre votre problème rapidement.">
                        📞 Contact marchand
                    </button>
                    <button type="button" class="quick-response-btn px-3 py-1 text-xs bg-yellow-100 hover:bg-yellow-200 text-yellow-800 rounded-full transition-colors"
                            data-message="Pourriez-vous nous fournir des informations supplémentaires pour mieux vous aider ?">
                        ❓ Demande d'infos
                    </button>
                    <button type="button" class="quick-response-btn px-3 py-1 text-xs bg-purple-100 hover:bg-purple-200 text-purple-800 rounded-full transition-colors"
                            data-message="Nous vous proposons une solution adaptée à votre situation.">
                        💡 Proposition solution
                    </button>
                </div>
                
                <div class="flex gap-3">
                    <textarea name="message" id="admin-message" rows="3" 
                              class="flex-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                              placeholder="Tapez votre réponse..."></textarea>
                    <div class="flex flex-col gap-2">
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            📤 Envoyer
                        </button>
                        <label class="inline-flex items-center">
                            <input type="checkbox" name="internal" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-xs text-gray-600 dark:text-gray-400">Note interne</span>
                        </label>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-scroll vers le bas
    const chatMessages = document.getElementById('chat-messages');
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // Réponses rapides
    document.querySelectorAll('.quick-response-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const message = this.getAttribute('data-message');
            document.getElementById('admin-message').value = message;
        });
    });
    
    // Soumission du formulaire
    document.getElementById('admin-response-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const messageText = formData.get('message');
        
        if (!messageText.trim()) {
            alert('Veuillez saisir un message');
            return;
        }
        
        // Ici vous pouvez ajouter l'appel AJAX pour envoyer le message
        // Pour l'instant, on simule l'envoi
        
        // Ajouter le message à la conversation
        const messagesContainer = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex justify-end';
        messageDiv.innerHTML = `
            <div class="max-w-xs lg:max-w-md">
                <div class="flex items-center gap-2 mb-1 justify-end">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        🛡️ Support
                    </span>
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                        ${new Date().toLocaleString('fr-FR')}
                    </span>
                </div>
                <div class="rounded-lg px-4 py-2 bg-green-500 text-white">
                    <p class="text-sm whitespace-pre-wrap">${messageText}</p>
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // Vider le formulaire
        document.getElementById('admin-message').value = '';
        
        // Notification de succès
        alert('Message envoyé avec succès !');
    });
});
</script>
