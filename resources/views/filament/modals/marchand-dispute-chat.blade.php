@php
    $dispute = $dispute->load(['messages' => function($query) {
        $query->publics()->orderBy('created_at', 'asc');
    }, 'client', 'commandePrincipale']);
@endphp

<div class="space-y-6">
    {{-- En-tête du litige --}}
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-lg p-4 border border-blue-200 dark:border-gray-600">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h3 class="font-bold text-lg text-gray-900 dark:text-white flex items-center gap-2">
                    @if($dispute->priorite === 'critique')
                        🚨
                    @elseif($dispute->priorite === 'haute')
                        ⚠️
                    @else
                        💬
                    @endif
                    {{ $dispute->sujet }}
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $dispute->type_litige_formate }}</p>
                
                <div class="flex items-center gap-2 mt-3">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                        @if($dispute->priorite === 'critique') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                        @elseif($dispute->priorite === 'haute') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                        @elseif($dispute->priorite === 'normale') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                        @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                        Priorité {{ $dispute->priorite_formate }}
                    </span>
                    
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                        @if($dispute->statut === 'attente_marchand') bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                        @elseif($dispute->statut === 'en_cours') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                        @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                        {{ $dispute->statut_formate }}
                    </span>
                </div>
            </div>
            
            <div>
                <div class="bg-white dark:bg-gray-800 rounded-lg p-3 border">
                    <p class="text-sm font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                        👤 Client : {{ $dispute->client ? $dispute->client->prenom . ' ' . $dispute->client->nom : 'Client supprimé' }}
                    </p>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 flex items-center gap-2">
                        🛒 Commande : {{ $dispute->commandePrincipale ? $dispute->commandePrincipale->numero_commande : 'N/A' }}
                    </p>
                    @if($dispute->montant_conteste)
                        <p class="text-sm text-red-600 dark:text-red-400 font-semibold mt-1 flex items-center gap-2">
                            💰 Montant contesté : {{ number_format($dispute->montant_conteste, 0, ',', ' ') }} FCFA
                        </p>
                    @endif
                    @if($dispute->date_limite_reponse)
                        <p class="text-sm mt-1 flex items-center gap-2 
                            @if($dispute->en_retard) text-red-600 dark:text-red-400 
                            @else text-green-600 dark:text-green-400 @endif">
                            ⏰ {{ $dispute->delai_restant }}
                        </p>
                    @endif
                </div>
            </div>
        </div>
        
        @if($dispute->description)
            <div class="mt-4 p-4 bg-white dark:bg-gray-800 rounded-lg border-l-4 border-blue-500">
                <p class="text-sm font-semibold text-gray-900 dark:text-white mb-2">📝 Description du problème :</p>
                <p class="text-sm text-gray-700 dark:text-gray-300">{{ $dispute->description }}</p>
            </div>
        @endif
    </div>

    {{-- Zone de chat --}}
    <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
        {{-- Messages --}}
        <div class="h-96 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-800" id="marchand-chat-messages">
            @forelse($dispute->messages as $message)
                <div class="flex {{ $message->auteur_type === 'marchand' ? 'justify-end' : 'justify-start' }}">
                    <div class="max-w-xs lg:max-w-md">
                        {{-- Badge auteur --}}
                        <div class="flex items-center gap-2 mb-1 {{ $message->auteur_type === 'marchand' ? 'justify-end' : 'justify-start' }}">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                @if($message->auteur_type === 'client') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                @elseif($message->auteur_type === 'admin') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                @elseif($message->auteur_type === 'marchand') bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                                @if($message->auteur_type === 'client') 👤 Client
                                @elseif($message->auteur_type === 'admin') 🛡️ Support
                                @elseif($message->auteur_type === 'marchand') 🏪 Vous
                                @else 🤖 Système @endif
                            </span>
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                {{ $message->created_at->format('d/m H:i') }}
                            </span>
                        </div>
                        
                        {{-- Bulle de message --}}
                        <div class="rounded-lg px-4 py-3 shadow-sm {{ $message->auteur_type === 'marchand' 
                            ? 'bg-orange-500 text-white' 
                            : ($message->auteur_type === 'client' 
                                ? 'bg-blue-500 text-white' 
                                : ($message->auteur_type === 'admin'
                                    ? 'bg-green-500 text-white'
                                    : 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white')) }}">
                            
                            @if($message->type_message === 'solution_proposee')
                                <div class="flex items-center gap-2 mb-2 pb-2 border-b border-white/20">
                                    <span class="text-yellow-300">💡</span>
                                    <span class="font-semibold">Solution proposée</span>
                                </div>
                            @elseif($message->type_message === 'resolution')
                                <div class="flex items-center gap-2 mb-2 pb-2 border-b border-white/20">
                                    <span class="text-green-300">✅</span>
                                    <span class="font-semibold">Résolution</span>
                                </div>
                            @endif
                            
                            <p class="text-sm whitespace-pre-wrap leading-relaxed">{{ $message->message }}</p>
                            
                            @if($message->a_pieces_jointes)
                                <div class="mt-2 pt-2 border-t border-white/20">
                                    <p class="text-xs opacity-75">📎 {{ $message->nombre_pieces_jointes }} pièce(s) jointe(s)</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-8">
                    <div class="text-6xl mb-4">💬</div>
                    <p class="text-gray-500 dark:text-gray-400">Première conversation avec ce client</p>
                    <p class="text-sm text-gray-400 dark:text-gray-500">Répondez rapidement pour maintenir votre excellente réputation !</p>
                </div>
            @endforelse
        </div>
        
        {{-- Formulaire de réponse marchand --}}
        <div class="border-t border-gray-200 dark:border-gray-700 p-4 bg-white dark:bg-gray-900">
            <form id="marchand-response-form" class="space-y-3">
                @csrf
                <input type="hidden" name="dispute_id" value="{{ $dispute->id }}">
                
                {{-- Réponses rapides pour marchands --}}
                <div class="flex flex-wrap gap-2 mb-3">
                    <button type="button" class="marchand-quick-response-btn px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-full transition-colors"
                            data-message="Bonjour ! Merci de nous avoir contactés. Nous étudions votre demande avec attention.">
                        👋 Salutation
                    </button>
                    <button type="button" class="marchand-quick-response-btn px-3 py-1 text-xs bg-yellow-100 hover:bg-yellow-200 text-yellow-800 rounded-full transition-colors"
                            data-message="Nous nous excusons sincèrement pour ce désagrément. Nous allons tout faire pour résoudre rapidement votre problème.">
                        🙏 Excuses
                    </button>
                    <button type="button" class="marchand-quick-response-btn px-3 py-1 text-xs bg-green-100 hover:bg-green-200 text-green-800 rounded-full transition-colors"
                            data-message="Nous vous proposons un remboursement complet. Le processus sera initié sous 24h.">
                        💰 Remboursement
                    </button>
                    <button type="button" class="marchand-quick-response-btn px-3 py-1 text-xs bg-purple-100 hover:bg-purple-200 text-purple-800 rounded-full transition-colors"
                            data-message="Nous vous proposons un échange gratuit de votre produit. Merci de nous confirmer votre adresse.">
                        🔄 Échange
                    </button>
                    <button type="button" class="marchand-quick-response-btn px-3 py-1 text-xs bg-orange-100 hover:bg-orange-200 text-orange-800 rounded-full transition-colors"
                            data-message="Pourriez-vous nous envoyer des photos du produit pour mieux comprendre le problème ?">
                        📸 Demande photos
                    </button>
                </div>
                
                <div class="flex gap-3">
                    <div class="flex-1">
                        <textarea name="message" id="marchand-message" rows="3" 
                                  class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                                  placeholder="Répondez à votre client avec professionnalisme..."></textarea>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            💡 Conseil : Une réponse rapide et empathique améliore votre réputation
                        </p>
                    </div>
                    <div class="flex flex-col gap-2">
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors">
                            📤 Envoyer
                        </button>
                        <button type="button" id="propose-solution-btn"
                                class="inline-flex items-center px-4 py-2 border border-orange-300 text-sm font-medium rounded-md text-orange-700 bg-orange-50 hover:bg-orange-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors">
                            💡 Solution
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    {{-- Conseils pour le marchand --}}
    <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-lg p-4 border border-green-200 dark:border-gray-600">
        <h4 class="font-semibold text-green-800 dark:text-green-200 mb-2">💡 Conseils pour une résolution efficace :</h4>
        <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
            <li>• Répondez dans les 24h pour maintenir votre excellente réputation</li>
            <li>• Montrez de l'empathie et reconnaissez le problème du client</li>
            <li>• Proposez des solutions concrètes (remboursement, échange, compensation)</li>
            <li>• Restez professionnel et courtois dans tous vos échanges</li>
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-scroll vers le bas
    const chatMessages = document.getElementById('marchand-chat-messages');
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // Réponses rapides
    document.querySelectorAll('.marchand-quick-response-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const message = this.getAttribute('data-message');
            const textarea = document.getElementById('marchand-message');
            if (textarea.value.trim()) {
                textarea.value += '\n\n' + message;
            } else {
                textarea.value = message;
            }
            textarea.focus();
        });
    });
    
    // Soumission du formulaire
    document.getElementById('marchand-response-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const messageText = formData.get('message');
        
        if (!messageText.trim()) {
            alert('Veuillez saisir un message');
            return;
        }
        
        // Ajouter le message à la conversation
        const messagesContainer = document.getElementById('marchand-chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex justify-end';
        messageDiv.innerHTML = `
            <div class="max-w-xs lg:max-w-md">
                <div class="flex items-center gap-2 mb-1 justify-end">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                        🏪 Vous
                    </span>
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                        ${new Date().toLocaleString('fr-FR', {day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit'})}
                    </span>
                </div>
                <div class="rounded-lg px-4 py-3 shadow-sm bg-orange-500 text-white">
                    <p class="text-sm whitespace-pre-wrap leading-relaxed">${messageText}</p>
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // Vider le formulaire
        document.getElementById('marchand-message').value = '';
        
        // Notification de succès
        alert('✅ Message envoyé avec succès ! Votre client sera notifié.');
    });
    
    // Bouton proposer solution
    document.getElementById('propose-solution-btn').addEventListener('click', function() {
        const textarea = document.getElementById('marchand-message');
        const solutionTemplate = `🔧 **SOLUTION PROPOSÉE**

Après analyse de votre demande, nous vous proposons :

□ Remboursement partiel/total : _____ FCFA
□ Échange du produit sous 48h
□ Avoir en boutique de _____ FCFA
□ Compensation pour le désagrément

Merci de nous confirmer la solution qui vous convient le mieux.

Cordialement,
L'équipe ${document.querySelector('input[name="dispute_id"]').closest('form').dataset.marchandName || 'de la boutique'}`;
        
        textarea.value = solutionTemplate;
        textarea.focus();
    });
});
</script>
