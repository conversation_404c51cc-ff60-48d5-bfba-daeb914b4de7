<x-filament-panels::page>
    <div class="space-y-6">
        <!-- R<PERSON><PERSON><PERSON> du marchand -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Informations principales -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Informations personnelles -->
                <x-filament::section>
                    <x-slot name="heading">
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-user class="w-5 h-5" />
                            Informations personnelles
                        </div>
                    </x-slot>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Nom complet</label>
                            <p class="text-gray-900 dark:text-white">{{ $record->user->name }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
                            <p class="text-gray-900 dark:text-white">{{ $record->user->email }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Téléphone</label>
                            <p class="text-gray-900 dark:text-white">{{ $record->personal_info['telephone'] ?? 'Non renseigné' }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Date de naissance</label>
                            <p class="text-gray-900 dark:text-white">
                                {{ isset($record->personal_info['date_naissance']) ? \Carbon\Carbon::parse($record->personal_info['date_naissance'])->format('d/m/Y') : 'Non renseigné' }}
                            </p>
                        </div>
                    </div>
                </x-filament::section>

                <!-- Informations business -->
                @if($record->business_info)
                <x-filament::section>
                    <x-slot name="heading">
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-building-office class="w-5 h-5" />
                            Informations business
                        </div>
                    </x-slot>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Nom de l'entreprise</label>
                            <p class="text-gray-900 dark:text-white">{{ $record->business_info['nomEntreprise'] ?? 'Non renseigné' }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Type d'entreprise</label>
                            <p class="text-gray-900 dark:text-white">{{ $record->business_info['type_business'] ?? 'Non renseigné' }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Téléphone principal</label>
                            <p class="text-gray-900 dark:text-white">{{ $record->business_info['telephone_principal'] ?? 'Non renseigné' }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Email business</label>
                            <p class="text-gray-900 dark:text-white">{{ $record->business_info['email_business'] ?? 'Non renseigné' }}</p>
                        </div>
                        @if(isset($record->business_info['adresse']))
                        <div class="md:col-span-2">
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Adresse</label>
                            <p class="text-gray-900 dark:text-white">{{ $record->business_info['adresse'] }}</p>
                        </div>
                        @endif
                        @if(isset($record->business_info['description']))
                        <div class="md:col-span-2">
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Description de l'activité</label>
                            <p class="text-gray-900 dark:text-white">{{ $record->business_info['description'] }}</p>
                        </div>
                        @endif
                    </div>
                </x-filament::section>
                @endif

                <!-- Documents soumis -->
                @if($record->documents->count() > 0)
                <x-filament::section>
                    <x-slot name="heading">
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-document-text class="w-5 h-5" />
                            Documents soumis ({{ $record->documents->count() }})
                        </div>
                    </x-slot>

                    <div class="space-y-4">
                        @foreach($record->documents as $document)
                        <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="flex-shrink-0">
                                    @php
                                        $extension = strtolower(pathinfo($document->nom_fichier, PATHINFO_EXTENSION));
                                        $isPdf = $extension === 'pdf';
                                        $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
                                    @endphp
                                    
                                    @if($isPdf)
                                        <x-heroicon-o-document-text class="w-8 h-8 text-red-500" />
                                    @elseif($isImage)
                                        <x-heroicon-o-photo class="w-8 h-8 text-blue-500" />
                                    @else
                                        <x-heroicon-o-document class="w-8 h-8 text-gray-500" />
                                    @endif
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900 dark:text-white">{{ $document->type_document }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $document->nom_fichier }}</p>
                                    <p class="text-xs text-gray-400">
                                        {{ number_format($document->file_size / 1024, 2) }} KB • 
                                        Uploadé le {{ \Carbon\Carbon::parse($document->created_at)->format('d/m/Y à H:i') }}
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                @if($isImage || $isPdf)
                                <a href="{{ route('admin.merchant-validation.view-document', ['validation' => $record->id, 'document' => $document->id]) }}" 
                                   target="_blank"
                                   class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800">
                                    <x-heroicon-m-eye class="w-4 h-4 mr-1" />
                                    Visualiser
                                </a>
                                @endif
                                <a href="{{ route('admin.merchant-validation.download-document', ['validation' => $record->id, 'document' => $document->id]) }}" 
                                   target="_blank"
                                   class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700">
                                    <x-heroicon-m-arrow-down-tray class="w-4 h-4 mr-1" />
                                    Télécharger
                                </a>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </x-filament::section>
                @endif
            </div>

            <!-- Sidebar avec statut et actions -->
            <div class="space-y-6">
                <!-- Statut actuel -->
                <x-filament::section>
                    <x-slot name="heading">Statut de la demande</x-slot>

                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Statut actuel</label>
                            <div class="mt-1">
                                @php
                                    $statusColors = [
                                        'EN_ATTENTE_VALIDATION' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
                                        'DOCUMENTS_SOUMIS' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                        'VALIDE' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                                        'REJETE' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                                    ];
                                    $statusLabels = [
                                        'EN_ATTENTE_VALIDATION' => 'En attente de validation',
                                        'DOCUMENTS_SOUMIS' => 'Documents soumis',
                                        'VALIDE' => 'Validé',
                                        'REJETE' => 'Rejeté',
                                    ];
                                @endphp
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusColors[$record->status] ?? 'bg-gray-100 text-gray-800' }}">
                                    {{ $statusLabels[$record->status] ?? $record->status }}
                                </span>
                            </div>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Soumis le</label>
                            <p class="text-gray-900 dark:text-white">{{ $record->submitted_at?->format('d/m/Y à H:i') ?? 'Non soumis' }}</p>
                        </div>

                        @if($record->validated_at)
                        <div>
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Traité le</label>
                            <p class="text-gray-900 dark:text-white">{{ $record->validated_at->format('d/m/Y à H:i') }}</p>
                        </div>
                        @endif

                        @if($record->validator)
                        <div>
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Traité par</label>
                            <p class="text-gray-900 dark:text-white">{{ $record->validator->name }}</p>
                        </div>
                        @endif

                        @if($record->rejection_reason)
                        <div>
                            <label class="text-sm font-medium text-gray-500 dark:text-gray-400">Raison du rejet</label>
                            <p class="text-gray-900 dark:text-white">{{ $record->rejection_reason }}</p>
                        </div>
                        @endif
                    </div>
                </x-filament::section>

                <!-- Formulaire de décision -->
                <x-filament::section>
                    <x-slot name="heading">Prendre une décision</x-slot>
                    
                    <div class="space-y-4">
                        {{ $this->form }}
                    </div>
                </x-filament::section>
            </div>
        </div>
    </div>
</x-filament-panels::page>
