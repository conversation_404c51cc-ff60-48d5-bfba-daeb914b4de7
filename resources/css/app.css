@import 'tailwindcss';
@import './form-errors.css';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

:root {
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.55 0.22 264); /* Bleu moderne */
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.985 0 0);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.55 0.22 264);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --radius: 0.625rem;
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.55 0.22 264);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.55 0.22 264);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.205 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.205 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.65 0.22 264); /* Bleu plus clair pour le mode sombre */
    --primary-foreground: oklch(0.145 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.985 0 0);
    --border: oklch(0.269 0 0);
    --input: oklch(0.269 0 0);
    --ring: oklch(0.65 0.22 264);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.65 0.22 264);
    --sidebar-primary-foreground: oklch(0.145 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(0.269 0 0);
    --sidebar-ring: oklch(0.65 0.22 264);
}

/* FilePond Custom Styles */
@layer components {
    .filepond-wrapper {
        @apply w-full;
    }

    .filepond--root {
        font-family: inherit;
    }

    .filepond--drop-label {
        color: hsl(var(--muted-foreground));
    }

    .filepond--label-action {
        color: hsl(var(--primary));
        text-decoration: underline;
        text-decoration-color: hsl(var(--primary));
    }

    .filepond--panel-root {
        background-color: hsl(var(--background));
        border: 2px dashed hsl(var(--border));
        border-radius: calc(var(--radius) - 2px);
        transition: border-color 0.2s ease;
    }

    .filepond--panel-root:hover {
        border-color: hsl(var(--primary));
    }

    .filepond--item {
        border-radius: calc(var(--radius) - 4px);
    }

    .filepond--item-panel {
        background-color: hsl(var(--card));
        border: 1px solid hsl(var(--border));
        border-radius: calc(var(--radius) - 4px);
    }

    .filepond--file-status-main {
        color: hsl(var(--foreground));
    }

    .filepond--file-status-sub {
        color: hsl(var(--muted-foreground));
    }

    .filepond--file-info-main {
        color: hsl(var(--foreground));
        font-weight: 500;
    }

    .filepond--file-info-sub {
        color: hsl(var(--muted-foreground));
    }

    /* Styles pour les fichiers validés (vert) */
    .filepond--item[data-filepond-item-state="processing-complete"] .filepond--item-panel {
        background-color: hsl(var(--primary) / 0.1);
        border-color: hsl(var(--primary));
        box-shadow: 0 0 0 1px hsl(var(--primary) / 0.2);
    }

    .filepond--item[data-filepond-item-state="processing-complete"] .filepond--file-status-main {
        color: hsl(var(--primary));
        font-weight: 600;
    }

    /* Styles pour les erreurs */
    .filepond--item[data-filepond-item-state="processing-error"] .filepond--item-panel {
        background-color: hsl(var(--destructive) / 0.1);
        border-color: hsl(var(--destructive));
        box-shadow: 0 0 0 1px hsl(var(--destructive) / 0.2);
    }

    .filepond--item[data-filepond-item-state="processing-error"] .filepond--file-status-main {
        color: hsl(var(--destructive));
        font-weight: 600;
    }

    /* Boutons d'action */
    .filepond--action-remove-item {
        background-color: hsl(var(--destructive));
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .filepond--action-remove-item:hover {
        background-color: hsl(var(--destructive) / 0.8);
        transform: scale(1.1);
    }

    .filepond--action-retry-item-load,
    .filepond--action-retry-item-processing {
        background-color: hsl(var(--primary));
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .filepond--action-retry-item-load:hover,
    .filepond--action-retry-item-processing:hover {
        background-color: hsl(var(--primary) / 0.8);
        transform: scale(1.1);
    }

    /* Progress bar */
    .filepond--progress-indicator {
        background-color: hsl(var(--primary));
    }

    /* Image preview */
    .filepond--image-preview {
        border-radius: calc(var(--radius) - 6px);
    }

    /* Loading indicator */
    .filepond--loading-indicator {
        color: hsl(var(--primary));
    }
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}
