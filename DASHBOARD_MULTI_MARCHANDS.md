# 📊 Dashboard Multi-Marchands - Documentation

## 🎯 Vue d'ensemble

Ce document décrit les améliorations apportées aux dashboards pour supporter le nouveau système multi-marchands avec intégration des sous-commandes, versements et commissions.

## 🏗️ Architecture

### Services Principaux

#### DashboardStatsService
**Localisation :** `app/Services/DashboardStatsService.php`

Service unifié pour la gestion des statistiques multi-marchands :
- **Statistiques Marchand** : Combine données nouveau système + legacy
- **Statistiques Admin** : Vue d'ensemble globale de la plateforme
- **Top Marchands** : Classement par performance
- **Cross-Database** : Requêtes entre lorrelei et admin_marchand_lorrelei

### Configuration Base de Données

**Connexion 'lorrelei'** ajoutée dans `config/database.php` :
```php
'lorrelei' => [
    'driver' => 'mysql',
    'host' => env('LORRELEI_DB_HOST', env('DB_HOST', '127.0.0.1')),
    'database' => env('LORRELEI_DB_DATABASE', 'lorrelei'),
    // ...
]
```

**Variables d'environnement requises :**
```env
LORRELEI_DB_HOST=127.0.0.1
LORRELEI_DB_PORT=3306
LORRELEI_DB_DATABASE=lorrelei
LORRELEI_DB_USERNAME=root
LORRELEI_DB_PASSWORD=
```

## 🎨 Widgets Dashboard Marchand

### 1. MarchandStatsOverview (Mis à jour)
**Localisation :** `app/Filament/Marchand/Widgets/MarchandStatsOverview.php`

**Métriques affichées :**
- Commandes ce mois (nouveau + legacy)
- Commandes en attente
- Commandes livrées
- Revenus ce mois avec évolution
- Produits actifs
- Produits en rupture
- Versements en attente
- Commissions collectées

### 2. SalesChart (Refactorisé)
**Localisation :** `app/Filament/Marchand/Widgets/SalesChart.php`

**Fonctionnalités :**
- 3 courbes : Sous-commandes, Legacy, Total
- Données 30 derniers jours
- Graphique Chart.js responsive
- Légende claire avec couleurs distinctives

### 3. LatestOrders (Transformé)
**Localisation :** `app/Filament/Marchand/Widgets/LatestOrders.php`

**Améliorations :**
- Vue unifiée sous-commandes + commandes legacy
- Union SQL pour performance
- Badges de type pour différencier
- Actions contextuelles

### 4. VersementsWidget (Nouveau)
**Localisation :** `app/Filament/Marchand/Widgets/VersementsWidget.php`

**Fonctionnalités :**
- Table complète avec filtres avancés
- Actions : Détails, téléchargement reçus
- Statuts visuels avec icônes
- Export et pagination

### 5. CommissionsWidget (Nouveau)
**Localisation :** `app/Filament/Marchand/Widgets/CommissionsWidget.php`

**Fonctionnalités :**
- Suivi détaillé par type de commission
- Filtres par période, statut, type
- Calculs automatiques avec détails
- Interface factures et exports

## 📊 Widgets Dashboard Admin

### 1. StatsOverview (Amélioré)
**Localisation :** `app/Filament/Widgets/StatsOverview.php`

**Métriques globales :**
- Utilisateurs (clients + marchands actifs)
- Commandes principales (nouveau système)
- Revenus totaux ce mois
- Commissions plateforme
- Marchands en attente de validation
- Versements en cours
- Produits avec ruptures

### 2. TopMarchandsWidget (Nouveau)
**Localisation :** `app/Filament/Widgets/TopMarchandsWidget.php`

**Fonctionnalités :**
- Classement dynamique des meilleurs marchands
- Métriques : Commandes, revenus, commissions
- Performance par commande
- Actions rapides vers détails

## 🧪 Tests et Validation

### Routes de Test
**Localisation :** `routes/test-dashboard.php`

**Endpoints disponibles :**
- `/test-dashboard/` - Page d'accueil des tests
- `/test-dashboard/database-test` - Test connexions DB
- `/test-dashboard/admin` - Statistiques admin
- `/test-dashboard/marchand/{id}` - Statistiques marchand
- `/test-dashboard/top-marchands` - Top marchands

**Utilisation :**
```bash
# Accéder à la page de test
http://votre-domaine.com/test-dashboard/

# Test direct d'un marchand
http://votre-domaine.com/test-dashboard/marchand/1
```

## 🚀 Déploiement

### 1. Variables d'Environnement
Ajouter dans `.env` :
```env
# Base de données Lorrelei
LORRELEI_DB_HOST=127.0.0.1
LORRELEI_DB_PORT=3306
LORRELEI_DB_DATABASE=lorrelei
LORRELEI_DB_USERNAME=root
LORRELEI_DB_PASSWORD=
```

### 2. Cache et Optimisations
```bash
# Vider le cache de configuration
php artisan config:clear

# Reconstruire le cache
php artisan config:cache

# Optimiser les routes
php artisan route:cache
```

### 3. Permissions Base de Données
Assurer que l'utilisateur DB a accès aux deux bases :
- `admin_marchand_lorrelei` (base principale)
- `lorrelei` (base client)

## 📈 Performance

### Optimisations Implémentées
- **Requêtes optimisées** avec indexes appropriés
- **Cache des calculs** fréquents
- **Pagination** des widgets tables
- **Lazy loading** des relations

### Métriques Cibles
- **Chargement dashboard** : < 2 secondes
- **Requêtes cross-database** : < 500ms
- **Widgets interactifs** : < 1 seconde

## 🔧 Maintenance

### Logs et Monitoring
- Surveiller les erreurs de connexion cross-database
- Monitorer les performances des requêtes complexes
- Vérifier la cohérence des données entre bases

### Mises à Jour
- Synchroniser les structures de données
- Maintenir la compatibilité avec l'ancien système
- Tester les migrations de données

## 📝 Notes de Développement

### Conventions de Code
- Services dans `app/Services/`
- Widgets Filament dans `app/Filament/*/Widgets/`
- Tests dans `routes/test-*.php` (dev uniquement)

### Bonnes Pratiques
- Toujours gérer les exceptions cross-database
- Utiliser les scopes Eloquent pour les requêtes complexes
- Documenter les méthodes de calcul des statistiques
- Tester avec des données volumineuses

## 🆘 Dépannage

### Erreurs Communes

**1. Connexion 'lorrelei' échoue**
```bash
# Vérifier la configuration
php artisan config:show database.connections.lorrelei

# Tester la connexion
php artisan tinker
DB::connection('lorrelei')->getPdo();
```

**2. Widgets vides**
- Vérifier les données dans les deux bases
- Contrôler les permissions utilisateur
- Examiner les logs Laravel

**3. Performance dégradée**
- Analyser les requêtes avec `EXPLAIN`
- Vérifier les indexes sur les tables
- Optimiser les requêtes cross-database

## 📞 Support

Pour toute question ou problème :
1. Consulter les logs Laravel
2. Utiliser les routes de test
3. Vérifier la documentation des modèles
4. Contacter l'équipe de développement
